import { NextRequest, NextResponse } from "next/server"
import { requireAdmin } from "@/lib/auth"
import { db } from "@/lib/db"
import { generateSlug } from "@/lib/slug"

export async function GET() {
  try {
    await requireAdmin()

    const stores = await db.store.findMany({
      orderBy: {
        name: 'asc'
      }
    })

    const formattedStores = stores.map(store => ({
      id: store.id,
      name: store.name,
      slug: store.slug,
      url: store.url,
      logoUrl: store.logoUrl,
      description: store.description,
      active: store.active,
      featured: store.featured,
      dealCount: store.dealCount,
      averageDiscount: store.averageDiscount
    }))

    const response = NextResponse.json({ stores: formattedStores })
    
    // Prevent caching for admin endpoints to ensure fresh data
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')
    
    return response
  } catch (error) {
    console.error("Error fetching stores:", error)
    return NextResponse.json(
      { error: "Failed to fetch stores" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    await requireAdmin()

    const { name, url, description, logoUrl } = await request.json()

    if (!name || !name.trim()) {
      return NextResponse.json(
        { error: "Store name is required" },
        { status: 400 }
      )
    }

    if (!url || !url.trim()) {
      return NextResponse.json(
        { error: "Store URL is required" },
        { status: 400 }
      )
    }

    const slug = generateSlug(name)

    // Check if store with this slug already exists
    const existingStore = await db.store.findUnique({
      where: { slug }
    })

    if (existingStore) {
      return NextResponse.json(
        { error: "A store with this name already exists" },
        { status: 400 }
      )
    }

    const store = await db.store.create({
      data: {
        name: name.trim(),
        slug,
        url: url.trim(),
        description: description?.trim() || null,
        logoUrl: logoUrl?.trim() || null,
        active: true,
        featured: false
      }
    })

    return NextResponse.json({
      id: store.id,
      name: store.name,
      slug: store.slug,
      url: store.url,
      logoUrl: store.logoUrl,
      description: store.description,
      active: store.active,
      featured: store.featured,
      dealCount: 0,
      averageDiscount: 0
    })
  } catch (error) {
    console.error("Error creating store:", error)
    return NextResponse.json(
      { error: "Failed to create store" },
      { status: 500 }
    )
  }
}
