import { NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json()

    if (!token || typeof token !== 'string') {
      return NextResponse.json({ error: "Token is required" }, { status: 400 })
    }

    // Find the email change request
    const emailChangeRequest = await db.emailChangeRequest.findUnique({
      where: { token },
      include: { user: true }
    })

    if (!emailChangeRequest) {
      return NextResponse.json({ error: "Invalid or expired confirmation link" }, { status: 400 })
    }

    // Check if token has expired
    if (new Date() > emailChangeRequest.expiresAt) {
      // Clean up expired request
      await db.emailChangeRequest.delete({
        where: { id: emailChangeRequest.id }
      })
      return NextResponse.json({ error: "Confirmation link has expired" }, { status: 400 })
    }

    // Check if the new email is still available (someone else might have taken it)
    const existingUser = await db.user.findUnique({
      where: { email: emailChangeRequest.newEmail }
    })

    if (existingUser && existingUser.id !== emailChangeRequest.userId) {
      // Clean up the request
      await db.emailChangeRequest.delete({
        where: { id: emailChangeRequest.id }
      })
      return NextResponse.json({ error: "Email address is no longer available" }, { status: 400 })
    }

    // Update the user's email address
    await db.user.update({
      where: { id: emailChangeRequest.userId },
      data: { email: emailChangeRequest.newEmail }
    })

    // Clean up the email change request
    await db.emailChangeRequest.delete({
      where: { id: emailChangeRequest.id }
    })

    return NextResponse.json({ 
      message: "Email address updated successfully!",
      newEmail: emailChangeRequest.newEmail
    })

  } catch (error) {
    console.error("Email confirmation error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
} 