import { NextRequest, NextResponse } from 'next/server';
import { 
  sendWelcomeEmail, 
  sendDealApprovalNotification, 
  sendDealRejectionNotification,
  sendPasswordResetEmail
} from '@/lib/email';

export async function GET() {
  return NextResponse.json({
    message: 'Email Test API',
    availableTypes: [
      'welcome',
      'deal-approval', 
      'deal-rejection',
      'password-reset',
      'custom'
    ],
    usage: {
      method: 'POST',
      body: {
        type: 'welcome | deal-approval | deal-rejection | password-reset | custom',
        email: '<EMAIL>',
        name: 'User Name (for welcome/password-reset)',
        dealTitle: 'Deal Title (for deal notifications)',
        dealUrl: 'Deal URL (for deal notifications)',
        reason: 'Rejection reason (for deal-rejection)',
        resetToken: 'Reset token (for password-reset)',
        subject: 'Custom subject (for custom)',
        message: 'Custom message (for custom)'
      }
    }
  });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, email, name, dealTitle, dealUrl, reason, resetToken, subject, message } = body;

    if (!type || !email) {
      return NextResponse.json(
        { error: 'Type and email are required' },
        { status: 400 }
      );
    }

    let result;

    switch (type) {
      case 'welcome':
        if (!name) {
          return NextResponse.json(
            { error: 'Name is required for welcome email' },
            { status: 400 }
          );
        }
        result = await sendWelcomeEmail(email, name);
        break;

      case 'deal-approval':
        if (!dealTitle || !dealUrl) {
          return NextResponse.json(
            { error: 'Deal title and URL are required for deal approval email' },
            { status: 400 }
          );
        }
        result = await sendDealApprovalNotification(email, dealTitle, dealUrl);
        break;

      case 'deal-rejection':
        if (!dealTitle || !reason) {
          return NextResponse.json(
            { error: 'Deal title and reason are required for deal rejection email' },
            { status: 400 }
          );
        }
        result = await sendDealRejectionNotification(email, dealTitle, reason);
        break;

      case 'password-reset':
        if (!name || !resetToken) {
          return NextResponse.json(
            { error: 'Name and reset token are required for password reset email' },
            { status: 400 }
          );
        }
        result = await sendPasswordResetEmail(email, name, resetToken);
        break;

      case 'custom':
        if (!subject || !message) {
          return NextResponse.json(
            { error: 'Subject and message are required for custom email' },
            { status: 400 }
          );
        }
        // For custom emails, we'll use the welcome email template as a base
        result = await sendWelcomeEmail(email, name || 'User');
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid email type' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      message: `${type} email sent successfully`,
      emailId: result?.id,
      details: result
    });

  } catch (error) {
    console.error('Email test error:', error);
    return NextResponse.json(
      { error: 'Failed to send email', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
} 