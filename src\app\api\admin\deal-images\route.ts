import { NextResponse } from "next/server";
import { requireAdmin } from "@/lib/auth";
import { db } from "@/lib/db";

export async function GET(request: Request) {
  try {
    await requireAdmin();

    const { searchParams } = new URL(request.url);
    const dealIdParam = searchParams.get("dealId");

    if (!dealIdParam) {
      return NextResponse.json(
        { error: "Deal ID is required" },
        { status: 400 }
      );
    }

    const dealId = parseInt(dealIdParam, 10);
    if (isNaN(dealId)) {
      return NextResponse.json(
        { error: "Invalid Deal ID" },
        { status: 400 }
      );
    }

    // Check if the deal exists
    const deal = await db.deal.findUnique({
      where: { id: dealId },
      select: { id: true },
    });

    if (!deal) {
      return NextResponse.json(
        { error: "Deal not found" },
        { status: 404 }
      );
    }

    // Get all images for the deal
    const images = await db.dealImage.findMany({
      where: { dealId },
      select: {
        id: true,
        imageUrl: true,
        thumbnailUrl: true,
        altText: true,
        caption: true,
        isMain: true,
        sortOrder: true,
      },
      orderBy: [
        { isMain: 'desc' },
        { sortOrder: 'asc' },
        { createdAt: 'asc' }
      ]
    });

    const response = NextResponse.json({ images })
    
    // Prevent caching for admin endpoints to ensure fresh data
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')
    
    return response
  } catch (error) {
    console.error("Error fetching deal images:", error);
    return NextResponse.json(
      { error: "Failed to fetch deal images" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    await requireAdmin();
    
    const data = await request.json();
    const { dealId: dealIdParam, imageUrl, thumbnailUrl, altText, caption, isMain } = data;

    if (!dealIdParam || !imageUrl) {
      return NextResponse.json(
        { error: "Deal ID and image URL are required" },
        { status: 400 }
      );
    }

    const dealId = parseInt(dealIdParam, 10);
    if (isNaN(dealId)) {
      return NextResponse.json(
        { error: "Invalid Deal ID" },
        { status: 400 }
      );
    }

    // Check if the deal exists
    const deal = await db.deal.findUnique({
      where: { id: dealId },
      select: { id: true },
    });

    if (!deal) {
      return NextResponse.json(
        { error: "Deal not found" },
        { status: 404 }
      );
    }

    // Create the image record
    const image = await db.dealImage.create({
      data: {
        dealId,
        imageUrl,
        thumbnailUrl: thumbnailUrl || imageUrl,
        altText: altText || "",
        caption: caption || "",
        isMain: Boolean(isMain),
      },
    });

    // If this is set as main, unset any other main images for this deal
    if (isMain) {
      await db.dealImage.updateMany({
        where: {
          dealId,
          isMain: true,
          id: { not: image.id },
        },
        data: { isMain: false },
      });
    }

    return NextResponse.json({ image });
  } catch (error) {
    console.error("Error adding deal image:", error);
    return NextResponse.json(
      { error: "Failed to add deal image" },
      { status: 500 }
    );
  }
}
