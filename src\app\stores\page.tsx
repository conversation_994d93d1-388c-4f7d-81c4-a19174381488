import Link from "next/link";
import { ExternalLink } from "lucide-react";
import { db } from "@/lib/db";

// Use ISR for production build performance
// NOTE: Next.js only allows literal values - no env vars, imports, or functions  
export const revalidate = 60

// Fetch stores from the database
export const metadata = {
  title: "Browse Stores | NiceDeals",
  description: "Discover deals from your favorite retailers. Shop from Amazon, John Lewis, Currys, H&M, and hundreds more trusted stores.",
};

export default async function StoresPage() {
  // Fetch all stores with their deal counts using proper relationships
  const stores = await db.store.findMany({
    include: {
      deals: {
        where: {
          status: {
            in: ['APPROVED', 'ACTIVE']
          }
        }
      },
      _count: {
        select: {
          deals: {
            where: {
              status: {
                in: ['APPROVED', 'ACTIVE']
              },
              createdAt: {
                gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
              }
            }
          }
        }
      }
    },
    orderBy: { name: "asc" },
  });

  // Calculate total active deals across all stores
  const totalDeals = stores.reduce((sum, store) => sum + store.deals.length, 0);

  // Get top 4 stores with most new deals in last 7 days (Popular Stores)
  const storesWithRecentDeals = stores
    .filter(store => store._count.deals > 0) // Only stores with recent deals
    .sort((a, b) => b._count.deals - a._count.deals) // Sort by recent deal count desc
    .slice(0, 4); // Take top 4

  // Remaining stores (excluding the popular ones)
  const otherStores = stores.filter(store => !storesWithRecentDeals.includes(store));
  // Remove average discount as a stat site-wide
  // const averageDiscount = ...

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-serif font-bold text-gray-900 mb-4">
            Browse by Store
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-6">
            Shop deals from your favorite retailers. We partner with the UK&rsquo;s leading stores 
            to bring you the best discounts and offers.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <div className="bg-white px-6 py-3 rounded-lg shadow-sm border border-gray-200">
              <span className="text-2xl font-bold text-orange-600">{stores.length}</span>
              <span className="text-gray-600 ml-2">trusted stores</span>
            </div>
            <div className="bg-white px-6 py-3 rounded-lg shadow-sm border border-gray-200">
              <span className="text-2xl font-bold text-orange-600">{totalDeals.toLocaleString()}</span>
              <span className="text-gray-600 ml-2">active deals</span>
            </div>
            {/* Removed average discount stat */}
          </div>
        </div>

        {/* Popular Stores - Most Active in Last 7 Days */}
        {storesWithRecentDeals.length > 0 && (
          <section className="mb-16">
            <h2 className="text-2xl font-serif font-bold text-gray-900 mb-8 text-center">
              Popular Stores
            </h2>
            <p className="text-center text-gray-600 mb-8">
              Stores with the most new deals in the last 7 days
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {storesWithRecentDeals.map((store) => (
              <div
                key={store.slug}
                className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
              >
                {/* Store Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-8 bg-gray-200 rounded flex items-center justify-center">
                      <span className="text-xs text-gray-500">Logo</span>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 text-lg">
                        {store.name}
                      </h3>
                      <div className="flex items-center gap-2 text-sm">
                        <span className="text-gray-600">{store.deals.length} deals</span>
                        <span className="text-orange-600 font-medium">({store._count.deals} new this week)</span>
                      </div>
                    </div>
                  </div>
                  <Link
                    href={store.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <ExternalLink className="w-5 h-5" />
                  </Link>
                </div>

                {/* Description */}
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                  {store.description}
                </p>

                {/* Categories */}
                {/* Category display removed for now */}

                {/* Stats and Action */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4 text-sm">
                    {/* Removed average discount per store */}
                  </div>
                  <Link
                    href={`/stores/${store.slug}`}
                    className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors text-sm font-medium"
                  >
                    View Deals
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </section>
        )}

        {/* All Stores */}
        <section>
          <h2 className="text-2xl font-serif font-bold text-gray-900 mb-8 text-center">
            All Stores
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {otherStores.map((store) => (
              <div
                key={store.slug}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-sm transition-all duration-200"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-6 bg-gray-200 rounded flex items-center justify-center">
                      <span className="text-xs text-gray-500">Logo</span>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {store.name}
                      </h3>
                      <div className="flex items-center gap-2 text-xs">
                        <span className="text-gray-600">{store.deals.length} deals</span>
                      </div>
                    </div>
                  </div>
                  <Link
                    href={store.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <ExternalLink className="w-4 h-4" />
                  </Link>
                </div>

                <div className="flex items-center justify-between">
                  {/* Removed average discount per store */}
                  <Link
                    href={`/stores/${store.slug}`}
                    className="text-orange-600 hover:text-orange-700 text-sm font-medium transition-colors"
                  >
                    View Deals →
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Partner CTA */}
        <section className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl p-8 text-white">
            <h2 className="text-3xl font-serif font-bold mb-4">
              Are You a Retailer?
            </h2>
            <p className="text-xl text-blue-100 mb-6 max-w-2xl mx-auto">
              Partner with NiceDeals to showcase your best offers to our community of savvy shoppers.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/partner"
                className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
              >
                Become a Partner
              </Link>
              <Link
                href="/contact"
                className="border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
              >
                Contact Us
              </Link>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
