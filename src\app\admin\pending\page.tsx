import { redirect } from "next/navigation"
import { requireAdmin } from "@/lib/auth"
import AdminLayout from "@/components/admin/AdminLayout"
import { getAdminStats } from "@/lib/admin"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import PendingDeals from "@/components/admin/PendingDeals"

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'

export default async function AdminPendingPage() {
  try {
    await requireAdmin()
  } catch {
    redirect("/auth/signin")
  }

  const stats = await getAdminStats()

  return (
    <AdminLayout stats={stats} currentPage="pending">
      <Card>
        <CardHeader>
          <CardTitle>Pending Deals</CardTitle>
          <CardDescription>
            Review and approve deals submitted by users
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PendingDeals />
        </CardContent>
      </Card>
    </AdminLayout>
  )
}
