import { notFound } from "next/navigation"
import { db } from "@/lib/db"
import DealCard from "@/components/DealCard"
import { Metadata } from "next"
import Link from "next/link"
import { Search } from "lucide-react"

// Use ISR for production build performance
// NOTE: Next.js only allows literal values - no env vars, imports, or functions
export const revalidate = 60

interface CategoryPageProps {
  params: Promise<{ slug: string }>
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

async function getCategory(slug: string) {
  const category = await db.category.findFirst({
    where: { 
      slug: slug,
      active: true 
    }
  })
  
  if (!category) {
    return null
  }
  
  return category
}

async function getCategoryDeals(categoryId: number, page: number = 1, limit: number = 12) {
  const skip = (page - 1) * limit
  
  const [deals, total] = await Promise.all([
    db.deal.findMany({
      where: {
        status: "ACTIVE",
        categoryId: categoryId
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        store: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            votes: true,
            comments: true,
            savedDeals: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,
    }),
    db.deal.count({
      where: {
        status: "ACTIVE",
        categoryId: categoryId
      }
    })
  ])

  return { deals, total, pages: Math.ceil(total / limit) }
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const { slug } = await params
  const category = await getCategory(slug)
  
  if (!category) {
    return {
      title: "Category Not Found | NiceDeals"
    }
  }
  
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3010";
  const fullImageUrl = `${baseUrl}/nicedeals-logo.png`;

  const title = `${category.name} Deals | NiceDeals`;
  const description = `Find the best ${category.name.toLowerCase()} deals and discounts.`;

  return {
    title,
    description: `${description} ${category.description || "Browse top brands and retailers."}`,
    openGraph: {
      title,
      description,
      images: [fullImageUrl]
    },
    twitter: {
      card: "summary",
      title,
      description,
      images: [fullImageUrl]
    }
  }
}

export default async function CategoryPage({ params, searchParams }: CategoryPageProps) {
  const { slug } = await params
  const { page: pageParam } = await searchParams
  
  const currentPage = pageParam ? parseInt(Array.isArray(pageParam) ? pageParam[0] : pageParam) : 1
  
  const category = await getCategory(slug)
  
  if (!category) {
    notFound()
  }
  
  const { deals, total, pages } = await getCategoryDeals(category.id, currentPage)
  
  // Transform deals to match DealCard interface
  const transformedDeals = deals.map(deal => {
    // Calculate isNew server-side to avoid hydration mismatch
    const isNew = (() => {
      if (!deal.createdAt) return false;
      const createdAt = new Date(deal.createdAt);
      const now = new Date();
      const diffInHours = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
      return diffInHours <= 24;
    })();

    return {
      id: deal.id,
      slug: deal.slug,
      title: deal.title,
      description: deal.description,
      originalPrice: deal.originalPrice ? Number(deal.originalPrice) : 0,
      dealPrice: Number(deal.dealPrice),
      discount: deal.discountPercentage || 0,
      store: {
        id: deal.store.id,
        name: deal.store.name,
        slug: deal.store.slug,
      },
      category: {
        id: deal.category.id,
        name: deal.category.name,
        slug: deal.category.slug,
      },
      dealUrl: deal.dealUrl,
      imageUrl: deal.imageUrl || "",
      upvotes: deal.upvotes,
      downvotes: deal.downvotes,
      couponCode: deal.couponCode || undefined,
      expiresAt: deal.expiresAt?.toISOString(),
      createdDate: deal.createdAt.toISOString(),
      isNew: isNew,
    };
  })

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
                     <nav className="text-sm text-gray-500 mb-4">
             <Link href="/" className="hover:text-orange-600">Home</Link>
             <span className="mx-2">›</span>
             <Link href="/categories" className="hover:text-orange-600">Categories</Link>
             <span className="mx-2">›</span>
             <span className="text-gray-900">{category.name}</span>
           </nav>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-2">
                {category.name} Deals
              </h1>
              {category.description && (
                <p className="text-gray-600 text-lg">
                  {category.description}
                </p>
              )}
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-orange-600">
                {total}
              </div>
              <div className="text-sm text-gray-500">
                active deals
              </div>
            </div>
          </div>
        </div>

        {/* Deals Grid */}
        {deals.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
              {transformedDeals.map((deal) => (
                <DealCard key={deal.id} deal={deal} />
              ))}
            </div>

            {/* Pagination */}
            {pages > 1 && (
              <div className="flex justify-center items-center space-x-2">
                {currentPage > 1 && (
                  <a
                    href={`/categories/${slug}?page=${currentPage - 1}`}
                    className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                  >
                    Previous
                  </a>
                )}
                
                {Array.from({ length: Math.min(5, pages) }, (_, i) => {
                  const page = Math.max(1, currentPage - 2) + i
                  if (page > pages) return null
                  
                  return (
                    <a
                      key={page}
                      href={`/categories/${slug}?page=${page}`}
                      className={`px-4 py-2 rounded-md ${
                        page === currentPage
                          ? "bg-orange-600 text-white"
                          : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
                      }`}
                    >
                      {page}
                    </a>
                  )
                })}
                
                {currentPage < pages && (
                  <a
                    href={`/categories/${slug}?page=${currentPage + 1}`}
                    className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                  >
                    Next
                  </a>
                )}
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Search className="w-12 h-12 text-orange-600" />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              No deals found
            </h2>
            <p className="text-gray-600 mb-6">
              There are currently no active deals in the {category.name} category.
            </p>
                         <Link
               href="/browse"
               className="bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700 transition-colors"
             >
               Browse All Deals
             </Link>
          </div>
        )}
      </div>
    </div>
  )
} 