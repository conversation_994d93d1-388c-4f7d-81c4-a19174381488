"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { MessageCir<PERSON>, <PERSON>ly, Clock, User } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface CommentUser {
  id: number
  name: string
  email?: string
  image?: string
}

interface Comment {
  id: string
  content: string
  createdAt: string
  user: CommentUser
  replies?: Comment[]
}

interface CommentsProps {
  dealId: number
}

export default function Comments({ dealId }: CommentsProps) {
  const { data: session } = useSession()
  const router = useRouter()
  const [comments, setComments] = useState<Comment[]>([])
  const [newComment, setNewComment] = useState("")
  const [replyTo, setReplyTo] = useState<string | null>(null)
  const [replyContent, setReplyContent] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState("")

  // Fetch comments
  useEffect(() => {
    const fetchComments = async () => {
      try {
        const response = await fetch(`/api/deals/${dealId}/comments`)
        if (response.ok) {
          const data = await response.json()
          setComments(data.comments)
        }
      } catch (error) {
        console.error("Failed to fetch comments:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchComments()
  }, [dealId])

  const handleSubmitComment = async (e: React.FormEvent, isReply: boolean = false) => {
    e.preventDefault()
    
    if (!session?.user) {
      router.push(`/auth/signin?callbackUrl=/deals/${dealId}`)
      return
    }

    const content = isReply ? replyContent : newComment
    if (!content.trim()) return

    setIsSubmitting(true)
    setError("")

    try {
      const response = await fetch(`/api/deals/${dealId}/comments`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: content.trim(),
          parentCommentId: isReply ? replyTo : null
        }),
      })

      const data = await response.json()

      if (response.ok) {
        // Refresh comments
        const commentsResponse = await fetch(`/api/deals/${dealId}/comments`)
        if (commentsResponse.ok) {
          const commentsData = await commentsResponse.json()
          setComments(commentsData.comments)
        }

        // Reset form
        if (isReply) {
          setReplyContent("")
          setReplyTo(null)
        } else {
          setNewComment("")
        }
      } else {
        setError(data.message || "Failed to post comment")
      }
    } catch (error) {
      setError("An error occurred. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
    
    if (diffInSeconds < 60) return "Just now"
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`
    return date.toLocaleDateString()
  }

  // Get user initials from email
  const getUserInitial = (email: string | null | undefined) => {
    if (!email) return 'U';
    return email.charAt(0).toUpperCase();
  }

  const CommentItem = ({ comment, isReply = false }: { comment: Comment; isReply?: boolean }) => (
    <div className={`${isReply ? 'ml-12 mt-4' : 'mb-6'}`}>
      <div className="flex items-start space-x-3">
        <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0 text-white font-semibold text-xs">
          {comment.user.image ? (
            <img
              src={comment.user.image}
              alt={comment.user.email || comment.user.name}
              className="w-8 h-8 rounded-full"
            />
          ) : (
            getUserInitial(comment.user.email || comment.user.name)
          )}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <span className="font-medium text-gray-900 text-sm">
              {comment.user.email || comment.user.name}
            </span>
            <div className="flex items-center text-gray-500 text-xs">
              <Clock className="w-3 h-3 mr-1" />
              <span>{formatTimeAgo(comment.createdAt)}</span>
            </div>
          </div>
          
          <p className="text-gray-700 text-sm leading-relaxed mb-2">
            {comment.content}
          </p>
          
          {!isReply && (
            <button
              onClick={() => setReplyTo(replyTo === comment.id ? null : comment.id)}
              className="text-orange-600 hover:text-orange-700 text-xs font-medium flex items-center"
            >
              <Reply className="w-3 h-3 mr-1" />
              Reply
            </button>
          )}
          
          {replyTo === comment.id && (
            <form onSubmit={(e) => handleSubmitComment(e, true)} className="mt-3">
              <textarea
                value={replyContent}
                onChange={(e) => setReplyContent(e.target.value)}
                placeholder="Write a reply..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                rows={2}
                disabled={isSubmitting}
              />
              <div className="flex items-center justify-end space-x-2 mt-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setReplyTo(null)
                    setReplyContent("")
                  }}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  size="sm"
                  disabled={isSubmitting || !replyContent.trim()}
                >
                  {isSubmitting ? "Posting..." : "Reply"}
                </Button>
              </div>
            </form>
          )}
        </div>
      </div>
      
      {/* Replies */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="mt-4">
          {comment.replies.map((reply) => (
            <CommentItem key={reply.id} comment={reply} isReply={true} />
          ))}
        </div>
      )}
    </div>
  )

  return (
    <Card className="">
      <CardHeader>
        <CardTitle className="flex items-center border-b border-gray-200 pb-4">
          <MessageCircle className="w-5 h-5 mr-2" />
          Comments ({comments.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Comment Form */}
        <div className="mb-6">
          {session?.user ? (
            <form onSubmit={(e) => handleSubmitComment(e, false)}>
              {error && (
                <Alert variant="destructive" className="mb-4">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0 text-white font-semibold text-xs">
                  {session.user.image ? (
                    <img
                      src={session.user.image}
                      alt={session.user.email || "User"}
                      className="w-8 h-8 rounded-full"
                    />
                  ) : (
                    getUserInitial(session.user.email)
                  )}
                </div>
                
                <div className="flex-1">
                  <textarea
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    placeholder="Share your thoughts about this deal..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    rows={3}
                    disabled={isSubmitting}
                  />
                  <div className="flex items-center justify-between mt-3">
                    <p className="text-xs text-gray-500">
                      {newComment.length}/1000 characters
                    </p>
                    <Button
                      type="submit"
                      disabled={isSubmitting || !newComment.trim()}
                    >
                      {isSubmitting ? "Posting..." : "Post Comment"}
                    </Button>
                  </div>
                </div>
              </div>
            </form>
          ) : (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Join the Discussion
              </h3>
              <p className="text-gray-600 mb-4">
                Sign in to share your thoughts and engage with the community.
              </p>
              <Button asChild>
                <a href={`/auth/signin?callbackUrl=/deals/${dealId}`}>
                  Sign In to Comment
                </a>
              </Button>
            </div>
          )}
        </div>

        {/* Comments List */}
        {isLoading ? (
          <div className="text-center py-8">
            <div className="w-8 h-8 border-2 border-orange-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">Loading comments...</p>
          </div>
        ) : comments.length > 0 ? (
          <div className="space-y-6">
            {comments.map((comment) => (
              <CommentItem key={comment.id} comment={comment} />
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No comments yet
            </h3>
            <p className="text-gray-600">
              Be the first to share your thoughts about this deal!
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
