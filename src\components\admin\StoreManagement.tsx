"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Plus, Edit, Store, Star, RefreshCw } from "lucide-react"
import Link from "next/link"

interface StoreData {
  id: string
  name: string
  slug: string
  url: string
  logoUrl?: string
  description?: string
  active: boolean
  featured: boolean
  dealCount: number
  averageDiscount: number
}

export default function StoreManagement() {
  const [stores, setStores] = useState<StoreData[]>([])
  const [loading, setLoading] = useState(true)
  const [newStore, setNewStore] = useState({
    name: "",
    url: "",
    description: "",
    logoUrl: "",
  })

  useEffect(() => {
    fetchStores()
  }, [])

  // Refresh data when the window gains focus (user returns to tab)
  useEffect(() => {
    const handleFocus = () => {
      fetchStores()
    }

    window.addEventListener('focus', handleFocus)
    return () => window.removeEventListener('focus', handleFocus)
  }, [])

  // Refresh data when the page becomes visible (user switches back to tab)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        fetchStores()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [])

  async function fetchStores() {
    setLoading(true)
    try {
      const response = await fetch("/api/admin/stores")
      if (response.ok) {
        const data = await response.json()
        setStores(data.stores)
      }
    } catch (error) {
      console.error("Error fetching stores:", error)
    } finally {
      setLoading(false)
    }
  }

  async function handleAddStore() {
    if (!newStore.name.trim() || !newStore.url.trim()) return

    try {
      const response = await fetch("/api/admin/stores", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newStore),
      })

      if (response.ok) {
        setNewStore({ name: "", url: "", description: "", logoUrl: "" })
        // Refresh the entire list to ensure data consistency
        await fetchStores()
      }
    } catch (error) {
      console.error("Error adding store:", error)
    }
  }

  async function handleToggleFeatured(storeId: string, featured: boolean) {
    try {
      const response = await fetch("/api/admin/feature-store", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          storeId,
          featured: !featured,
        }),
      })

      if (response.ok) {
        setStores(stores.map(store => 
          store.id === storeId 
            ? { ...store, featured: !featured }
            : store
        ))
      }
    } catch (error) {
      console.error("Error toggling featured status:", error)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Refresh Button */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Store Management</h3>
        <Button
          variant="outline"
          onClick={() => fetchStores()}
          disabled={loading}
          size="sm"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Add New Store */}
      <Card>
        <CardContent className="p-4">
          <h3 className="font-medium mb-4">Add New Store</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="storeName">Store Name</Label>
              <Input
                id="storeName"
                value={newStore.name}
                onChange={(e) => setNewStore({...newStore, name: e.target.value})}
                placeholder="Store name"
              />
            </div>
            <div>
              <Label htmlFor="storeUrl">Website URL</Label>
              <Input
                id="storeUrl"
                value={newStore.url}
                onChange={(e) => setNewStore({...newStore, url: e.target.value})}
                placeholder="https://example.com"
              />
            </div>
            <div>
              <Label htmlFor="storeDescription">Description</Label>
              <Input
                id="storeDescription"
                value={newStore.description}
                onChange={(e) => setNewStore({...newStore, description: e.target.value})}
                placeholder="Store description"
              />
            </div>
            <div>
              <Label htmlFor="logoUrl">Logo URL</Label>
              <Input
                id="logoUrl"
                value={newStore.logoUrl}
                onChange={(e) => setNewStore({...newStore, logoUrl: e.target.value})}
                placeholder="https://example.com/logo.png"
              />
            </div>
          </div>
          <Button onClick={handleAddStore} className="mt-4">
            <Plus className="w-4 h-4 mr-2" />
            Add Store
          </Button>
        </CardContent>
      </Card>

      {/* Stores List */}
      <div className="grid gap-4">
        {stores.map((store) => (
          <Card key={store.id}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                    {store.logoUrl ? (
                      <img
                        src={store.logoUrl}
                        alt={store.name}
                        className="w-full h-full object-contain"
                      />
                    ) : (
                      <Store className="w-6 h-6 text-gray-600" />
                    )}
                  </div>
                  <div>
                    <h3 className="font-medium">{store.name}</h3>
                    <p className="text-sm text-gray-500">{store.description}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="secondary">
                        {store.dealCount} deals
                      </Badge>
                      {store.featured && (
                        <Badge className="bg-yellow-500">
                          <Star className="w-3 h-3 mr-1" />
                          Featured
                        </Badge>
                      )}
                      <Badge variant={store.active ? "default" : "outline"}>
                        {store.active ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="text-right text-sm">
                    <div className="font-medium">
                      {store.averageDiscount > 0 ? `${store.averageDiscount.toFixed(0)}% avg discount` : "No discount data"}
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant={store.featured ? "default" : "outline"}
                      onClick={() => handleToggleFeatured(store.id, store.featured)}
                      className={store.featured ? "bg-yellow-500 hover:bg-yellow-600" : ""}
                    >
                      <Star className="w-4 h-4 mr-1" />
                      {store.featured ? "Unfeature" : "Feature"}
                    </Button>
                    <Link href={`/admin/stores/${store.id}/edit`}>
                      <Button size="sm" variant="outline">
                        <Edit className="w-4 h-4 mr-1" />
                        Edit
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
