# NiceDeals: Complete Product & Development Guide

**Version:** 2.0  
**Date:** June 2025  
**Product Owner:** [User]  
**Development Platform:** Next.js with SQLite & NextAuth.js

---

## Table of Contents
1. [Executive Summary](#1-executive-summary)
2. [User Personas & Authentication](#2-user-personas--authentication)
3. [Core Features & Functionality](#3-core-features--functionality)
4. [Categories & Store Management](#4-categories--store-management)
5. [User Interface & Design System](#5-user-interface--design-system)
6. [Admin Panel & Moderation](#6-admin-panel--moderation)
7. [Notification System](#7-notification-system)
8. [Technical Architecture](#8-technical-architecture)
9. [User Experience Flows](#9-user-experience-flows)
10. [Quality Assurance & Content Guidelines](#10-quality-assurance--content-guidelines)
11. [Success Metrics & KPIs](#11-success-metrics--kpis)
12. [Future Enhancement Opportunities](#12-future-enhancement-opportunities)
13. [Development Progress & Implementation Plan](#13-development-progress--implementation-plan)

---

## 1. Executive Summary

NiceDeals is a comprehensive, community-driven deals aggregation platform designed to empower users to discover, share, and vote on the best deals across a wide range of categories and stores. The platform's core is built on user-generated content, moderated by an administrative team to ensure high-quality, trustworthy deal discovery.

### 1.1. Key Value Propositions

-   **Community-Driven:** Users are at the heart of the platform, with the ability to post, vote, and comment on deals.
-   **Curated Content:** Admin moderation ensures all deals meet quality standards.
-   **Comprehensive Coverage:** An extensive database of stores and categories provides a wide variety of deals.
-   **Personalized Experience:** Registered users can save deals and set up custom notifications.
-   **Public Access:** Anyone can browse deals without an account, with registration required for interactive features.

### 1.2. Core Technical Principle: SEO & Server-Side Rendering (SSR)

To maximize search engine visibility and ensure optimal performance, **all public-facing pages must be Server-Side Rendered (SSR)**. This approach guarantees that search engine crawlers can effectively index all content, including meta tags, which is critical for SEO success. Client-Side Rendering (CSR) is not suitable for this project due to its inherent SEO limitations.

---

## 2. User Personas & Authentication

### 2.1. User Types

-   **Anonymous Users (Public Access):**
    -   **Capabilities:** Browse all deals, view deal details, access category and store pages, use search and filtering.
    -   **Restrictions:** Cannot post deals, vote, comment, save deals, or access notifications.
-   **Registered Users:**
    -   **Authentication:** Google OAuth integration via NextAuth.js.
    -   **Capabilities:** All anonymous user capabilities, plus posting new deals (subject to admin approval), voting (upvote/downvote), commenting, saving deals, setting up notifications, and maintaining a user profile.
    -   **Profile Features:** Bio, location, avatar upload, deal posting history, and reputation tracking.
-   **Admin Users:**
    -   **Role-Based Access:** An "admin" role assigned to a user entity.
    -   **Capabilities:** All registered user capabilities, plus access to an admin panel for moderating deals, managing categories and stores, featuring deals, moderating comments, managing users, and performing bulk operations.

---

## 3. Core Features & Functionality

### 3.1. Deal Management System

-   **Deal Entity Structure:**
    ```json
    {
      "title": "string (required)",
      "description": "string (required)",
      "original_price": "number",
      "deal_price": "number (required)",
      "discount_percentage": "number (calculated)",
      "store": "string (required)",
      "store_slug": "string (auto-generated)",
      "category": "string (required - slug)",
      "deal_url": "string (required)",
      "image_url": "string (uploaded)",
      "expires_at": "datetime",
      "status": "enum [active, expired, pending, rejected]",
      "upvotes": "number (default: 0)",
      "downvotes": "number (default: 0)",
      "average_rating": "number (default: 0)",
      "rating_count": "number (default: 0)",
      "view_count": "number (default: 0)",
      "featured": "boolean (default: false)",
      "coupon_code": "string",
      "created_by": "string (user email)",
      "created_date": "datetime (auto)",
      "updated_date": "datetime (auto)"
    }
    ```
-   **Deal Posting Workflow:**
    1.  A registered user submits a deal via a form.
    2.  The system validates all required fields.
    3.  An image is uploaded via Base44 integration.
    4.  The deal is created with a "pending" status.
    5.  An admin is notified for approval.
    6.  The admin approves or rejects the deal, triggering an email notification to the user.
    7.  Approved deals become "active" and visible on the site.

-   **Deal Display Features:**
    -   **Deal Score:** Calculated as `(upvotes - downvotes)`.
    -   **Discount Percentage:** Automatically calculated if both original and deal prices are provided.
    -   **Coupon Codes:** An optional field with a "copy to clipboard" functionality.
    -   **Expiration Tracking:** Visual indicators for time-sensitive deals.

### 3.2. Voting & Rating System

-   **Voting Entity:**
    ```json
    {
      "deal_id": "string (required)",
      "user_email": "string (required)",
      "vote_type": "enum [upvote, downvote]"
    }
    ```
-   **Rating Entity:**
    ```json
    {
      "deal_id": "string (required)",
      "user_email": "string (required)",
      "rating": "number (1-5, required)",
      "review": "string (optional)"
    }
    ```
-   **Voting Rules:**
    -   One vote per user per deal.
    -   Users can change their vote.
    -   Anonymous users can see voting results but cannot vote.
    -   Vote counts update in real-time.

### 3.3. Comment System

-   **Comment Entity:**
    ```json
    {
      "deal_id": "string (required)",
      "user_email": "string (required)",
      "content": "string (required)",
      "parent_comment_id": "string (for replies)",
      "status": "enum [approved, hidden, pending]",
      "flagged_count": "number (default: 0)",
      "created_by": "string (user email)",
      "created_date": "datetime (auto)"
    }
    ```
-   **Comment Features:**
    -   Threaded comments with support for replies.
    -   Admin moderation to hide inappropriate comments.
    -   Comments are auto-approved by default.
    -   User attribution with commenter's name and avatar.

### 3.4. Save Deal Functionality

-   **SavedDeal Entity:**
    ```json
    {
      "deal_id": "string (required)",
      "user_email": "string (required)",
      "created_date": "datetime (auto)"
    }
    ```
-   **Save Features:**
    -   A "heart" icon on all deal cards.
    -   A "Save Deal" button on deal pages.
    -   A personal collection of saved deals for each user.
    -   Easy "unsave" functionality.

---

## 4. Categories & Store Management

### 4.1. Category System

-   **Category Entity:**
    ```json
    {
      "name": "string (required)",
      "slug": "string (required, URL-friendly)",
      "description": "string",
      "icon": "string (Lucide icon name)",
      "active": "boolean (default: true)",
      "sort_order": "number (default: 0)"
    }
    ```
-   **Predefined Categories:**
    - Electronics, Computers, Gaming, Home & Garden, Clothing & Accessories
    - Beauty & Health, Toys & Kids, Books & Media, Food & Drink, Travel
    - Sports & Outdoors, Automotive, Services, Entertainment, Home Improvement
    - Office Supplies, Groceries, Health & Beauty, Fashion & Accessories
    - Culture & Leisure, Broadband & Phone Contracts, Home & Living
    - Garden & Do It Yourself, Family & Kids, Freebies, Services & Contracts, Other

### 4.2. Store Management

-   **Store Entity:**
    ```json
    {
      "name": "string (required)",
      "slug": "string (required, URL-friendly)",
      "url": "string (required, store website)",
      "logo_url": "string (store logo)",
      "description": "string",
      "active": "boolean (default: true)",
      "featured": "boolean (default: false)",
      "sort_order": "number (default: 0)"
    }
    ```
-   **Integrated Stores:** 99+ major UK retailers including:
    - Amazon, eBay, Currys, Argos, Adidas, H&M, John Lewis, Marks & Spencer
    - Boots, Superdrug, Sephora, GAME, PlayStation Store, easyJet, Expedia, Emirates
    - And 90+ more major UK retailers

---

## 5. User Interface & Design System

### 5.1. Design Principles

-   **Visual Style:** Clean, modern, and inviting design inspired by `nicedeals_screen.png`. UI prioritizes clarity and ease of use.
-   **Typography:**
    -   **Headlines (h1, h2, h3):** Instrument Serif (elegant, high-impact look).
    -   **Body & UI Elements:** Inter (excellent readability and clean, modern feel).
-   **Color Palette:**
    -   **Primary Action:** Orange (`#f97316`) for buttons, links, and highlighted elements.
    -   **Secondary Accent:** Cool blue for secondary actions or informational elements.
    -   **Background:** Subtle gradient from light slate gray to soft blue for depth.
    -   **Text:** Dark gray/black for body text (`#1f2937`) and lighter gray for secondary text.
    -   **Borders & Dividers:** Light gray (`#e5e7eb`).
-   **Layout:**
    -   **Responsive:** Fluid, mobile-first grid system adapting from mobile to desktop.
    -   **Spacing:** Consistent spacing and padding for balanced, uncluttered layout.
-   **Iconography:**
    -   **Style:** Lucide React icons for clean, consistent, modern look.

### 5.2. Navigation Structure

-   **Header:** Logo, Hot Deals, Browse Deals, Post Deal, User Menu, Login Button.
-   **Footer:** Quick Links, Customer Support, Admin Panel (for admins), Newsletter Signup, Legal Links.

### 5.3. Page Structure (All SSR)

-   **Homepage:** Hero section, featured deals, statistics cards, hot deals, recent deals.
-   **Browse Deals Page:** Search bar, collapsible filter sidebar, sort controls, deal grid, filter persistence via URL parameters.
-   **Deal Detail Page:** Breadcrumb navigation, deal score bar, two-column layout (image/info and related deals), tabbed sections (description, details, comments), action buttons.
-   **Categories Index Page:** Grid layout with category cards.
-   **Stores Index Page:** Grid layout with store logos.

### 5.4. Interactive Elements

-   **Deal Cards:** Clickable title, store badge, category badge, save icon, vote buttons, hover effects.

### 5.5. Component-Specific Design Guidelines

-   **Buttons:**
    -   **Primary:** Solid orange background (`#f97316`) with white text.
    -   **Secondary:** Outlined with blue border and blue text.
    -   **Hover/Focus States:** Clear visual feedback with brightness/shadow changes.
-   **Forms & Inputs:**
    -   Clean, simple design with clear labels.
    -   Consistent border radius and padding.
    -   Validation states clearly indicated with color and helper text.
-   **Deal Cards:**
    -   Prominent image display.
    -   Clear hierarchy for title, price, and store.
    -   Subtle box-shadow to lift card off background.
    -   Intuitive, accessible interactive elements (vote, save).
-   **Modals & Pop-ups:**
    -   Semi-transparent overlay for focus.
    -   Clear "close" button.

---

## 6. Admin Panel & Moderation

### 6.1. Admin Dashboard

-   **Statistics Overview:** Key metrics at a glance.
-   **Tabbed Interface:**
    -   **Pending Deals:** Approval/rejection with bulk actions.
    -   **Deal Moderation:** Feature, remove, search, and filter active deals.
    -   **User Management:** User list, stats, and role management.
    -   **Category Management:** CRUD operations for categories.
    -   **Store Management:** CRUD operations for stores.
    -   **Comment Moderation:** Manage flagged comments.

### 6.2. Moderation Workflows

-   **Deal Approval:** Clear process from submission to approval/rejection with email notifications.
-   **Comment Moderation:** Auto-approval with user flagging and admin oversight.

---

## 7. Notification System

### 7.1. Notification Entity

```json
{
  "user_email": "string (required)",
  "type": "enum [keyword, category]",
  "value": "string (keyword or category slug)",
  "active": "boolean (default: true)"
}
```

### 7.2. Notification Features

-   Email notifications (toggle on/off).
-   Keyword and category alerts.
-   User management of notification preferences.

---

## 8. Technical Architecture

### 8.1. Platform & Frontend

-   **Framework:** Next.js (with App Router for SSR).
-   **Language:** TypeScript.
-   **Styling:** Tailwind CSS with Shadcn/UI components.
-   **Icons:** Lucide React.
-   **Animations:** Framer Motion.
-   **Authentication:** NextAuth.js with Google OAuth.
-   **Database:** SQLite with Prisma ORM.

### 8.2. Database Entities

-   User, Deal, Vote, Rating, Comment, SavedDeal, Notification, Category, Store.

### 8.3. Integrations

-   **Image Storage:** Local file system storage.
-   **Email Service:** Nodemailer for notification emails.
-   **Database:** SQLite with Prisma ORM for data management.

### 8.4. Data Management

-   Automatic generation of IDs, timestamps, and slugs.
-   Image handling via local file system.
-   Full-text search across deals using SQLite FTS.
-   Prisma ORM for type-safe database operations.

### 8.5. SEO & Performance

-   **SSR:** All public pages server-rendered.
-   **Meta Tags:** Dynamically generated for all pages.
-   **Structured Data:** JSON-LD for rich snippets.
-   **Sitemap:** Auto-generated XML sitemap.
-   **Performance:** Optimized Core Web Vitals.

---

## 9. User Experience Flows

-   **Anonymous User Journey:** From landing on homepage to login prompts for interactive features.
-   **New User Registration:** Google OAuth flow, profile setup, and feature exploration.
-   **Deal Posting Flow:** From accessing form to deal going live.
-   **Admin Moderation Flow:** From accessing admin panel to managing content and users.

---

## 10. Quality Assurance & Content Guidelines

### 10.1. Deal Quality Standards

-   Accurate information, clear descriptions, valid links, high-quality images, and current deals.

### 10.2. Community Guidelines

-   Respectful communication, no spam, accurate voting, and content moderation.

### 10.3. Admin Responsibilities

-   Timely moderation, quality control, community management, and platform maintenance.

---

## 11. Success Metrics & KPIs

### 11.1. User Engagement

-   Daily/Monthly Active Users, Deal Submission Rate, Voting Participation, Comment Engagement, User Retention.

### 11.2. Content Quality

-   Deal Approval Rate, Average Deal Score, Content Moderation Volume, User-Reported Issues.

### 11.3. Platform Growth

-   New User Registrations, Store Partnership Growth, Category Expansion, Mobile Usage Statistics.

---

## 12. Future Enhancement Opportunities

-   Mobile App (iOS/Android), Price Tracking, Push Notifications, Enhanced Social Sharing
-   Merchant API Integrations, User Rankings and Leaderboards, Advanced Analytics
-   International Expansion, AI-Powered Recommendations

---

## 13. Development Progress & Implementation Plan

### ✅ COMPLETED - Phase 1: Project Foundation

#### 1. Project Setup
- ✅ **Next.js 15 with TypeScript** - Created with App Router for SSR
- ✅ **Tailwind CSS & Shadcn/UI** - Design system configured
- ✅ **Custom Fonts** - Instrument Serif (headlines) and Inter (body)
- ✅ **Color Scheme** - Orange primary (#f97316) with proper CSS variables
- ✅ **Project Structure** - Organized components and pages

#### 2. Core Layout Components
- ✅ **Header Component** - Navigation with logo, menu items, login button
- ✅ **Footer Component** - Links, newsletter signup, legal pages
- ✅ **Root Layout** - Proper HTML structure with header/footer integration

#### 3. Homepage Implementation
- ✅ **Hero Section** - Orange gradient with call-to-action buttons
- ✅ **Statistics Cards** - Community metrics display
- ✅ **Featured Deals** - Card-based layout with pricing, ratings
- ✅ **Hot Deals Section** - Trending deals grid
- ✅ **Recent Deals** - List format with comprehensive deal info

### ✅ COMPLETED - Phase 2: Core Pages & Components

#### 4. Browse Deals Page (SSR)
- ✅ **Server-side data fetching** - Optimized for SEO with mock data
- ✅ **Search functionality** - Real-time search with filters
- ✅ **Category and store filtering** - Advanced filtering sidebar
- ✅ **Sort controls** - Recent, popular, price, rating sorting
- ✅ **Grid/List views** - Toggle between card layouts
- ✅ **Deal grid** - Consistent card design with DealCard component

#### 5. Deal Detail Pages (SSR)
- ✅ **Dynamic routes** - `/deals/[id]` with proper routing
- ✅ **SEO-optimized meta tags** - Per deal optimization
- ✅ **Breadcrumb navigation** - Clear navigation path
- ✅ **Deal score visualization** - Community voting interface
- ✅ **Two-column layout** - Image/info + related deals sidebar
- ✅ **Tabbed sections** - Description, details, comments
- ✅ **Related deals section** - Category-based recommendations
- ✅ **404 handling** - Custom not-found page

#### 6. Categories & Stores Pages (SSR)
- ✅ **Categories Index** - Grid layout with 24+ categories
- ✅ **Stores Index** - 15+ major UK retailers
- ✅ **Deal counts** - Statistics per category/store
- ✅ **Lucide icons** - Consistent iconography
- ✅ **Featured sections** - Popular categories and stores
- ✅ **SEO optimization** - Proper meta tags and descriptions

#### 7. Deal Posting System
- ✅ **Post Deal Page** - Complete form interface
- ✅ **Form validation** - Required fields and input types
- ✅ **Category/store selection** - Dropdown menus
- ✅ **Image upload interface** - Drag-and-drop area
- ✅ **Optional fields** - Coupon codes, expiry dates
- ✅ **Submission guidelines** - Quality content rules

### 🚧 Phase 3: Database & Authentication (Next Steps)
### ✅ COMPLETED - Phase 3: Core User Experience

#### 8. Enhanced Navigation & Connectivity
- ✅ **Deal Card Links** - All deal cards link to detail pages
- ✅ **Functional Homepage** - All buttons link to appropriate pages
- ✅ **Cross-page navigation** - Seamless flow between sections
- ✅ **Filter persistence** - URL parameters maintain state

### 🚧 Phase 4: Database & Authentication (Next Steps)

#### 4. Browse Deals Page (SSR Priority)
```typescript
// File: src/app/browse/page.tsx
- Server-side data fetching for SEO
- Search functionality with filters
- Category and store filtering
- Sort controls (recent, popular, price, rating)
- Pagination with URL parameters
- Deal grid with consistent card design
```

#### 5. Deal Detail Pages (SSR)
```typescript
// File: src/app/deals/[slug]/page.tsx
- Dynamic routes with generateStaticParams
- SEO-optimized meta tags per deal
- Breadcrumb navigation
- Deal score visualization
- Two-column layout (image/info + sidebar)
- Tabbed sections (description, details, comments)
- Related deals section
- Voting interface
- Comment system
```

#### 6. Categories & Stores Pages (SSR)
```typescript
// Files: 
// - src/app/categories/page.tsx
// - src/app/categories/[slug]/page.tsx
// - src/app/stores/page.tsx
// - src/app/stores/[slug]/page.tsx

Categories Index:
- Grid layout with category cards
- Deal counts per category
- Lucide icons for each category

Stores Index:
- Store logos in grid layout
- Featured store badges
- Store statistics

Category/Store Detail Pages:
- Filtered deal listings
- SEO-optimized for each category/store
- Breadcrumb navigation
```

### Phase 3: User Authentication & Interactive Features

#### 7. Authentication System
```typescript
// Integration with Base44 Google OAuth
// Files: src/lib/auth.ts, src/components/auth/

- Google OAuth configuration
- User session management
- Role-based access control (user, admin)
- Protected routes middleware
- Login/logout functionality
```

#### 8. User Dashboard & Profile
```typescript
// Files: src/app/profile/*, src/app/dashboard/*

- User profile management
- Saved deals collection
- Deal posting history
- Notification preferences
- Reputation tracking
```

#### 9. Deal Management System
```typescript
// Files: src/app/post/page.tsx, src/components/deals/

Deal Posting:
- Form validation
- Image upload via Base44
- Category/store selection
- Preview functionality
- Draft saving

Deal Moderation:
- Pending status workflow
- Email notifications
- Admin approval process
```

### Phase 4: Interactive Features

#### 10. Voting & Rating System
```typescript
// Files: src/components/voting/, src/lib/votes.ts

- Real-time vote updates
- One vote per user per deal
- Vote change functionality
- Rating with reviews
- Optimistic UI updates
```

#### 11. Comment System
```typescript
// Files: src/components/comments/

- Threaded comments
- Reply functionality
- User attribution
- Admin moderation
- Real-time updates
```

#### 12. Save Deal Functionality
```typescript
// Files: src/components/SaveButton.tsx, src/lib/saved-deals.ts

- Heart icon toggle
- Personal collection
- Easy unsave
- Saved deals dashboard
```

### Phase 5: Admin Panel

#### 13. Admin Dashboard
```typescript
// Files: src/app/admin/*, src/components/admin/

Dashboard Overview:
- Statistics widgets
- Pending content alerts
- Quick actions

Tabbed Interface:
- Pending Deals (approval/rejection)
- Deal Moderation (feature/remove)
- User Management
- Category Management
- Store Management
- Comment Moderation
```

#### 14. Content Management
```typescript
// CRUD operations for:
- Categories (icons, descriptions, sorting)
- Stores (logos, featured status, URLs)
- Deals (approval, featuring, removal)
- Users (role management, statistics)
```

### Phase 6: Advanced Features

#### 15. Notification System
```typescript
// Files: src/lib/notifications.ts, src/components/notifications/

- Email notifications via Base44
- Keyword alerts
- Category alerts
- User preference management
- Notification templates
```

#### 16. Search & SEO Optimization
```typescript
// Files: src/lib/search.ts, src/app/sitemap.ts

- Full-text search implementation
- Advanced filtering
- SEO meta tag generation
- Sitemap generation
- Schema.org structured data
- Open Graph tags
```

### Phase 7: Database & API Layer

#### 17. Database Schema Implementation
```sql
-- Based on PRD entities:
- Users (with profiles and roles)
- Deals (complete entity structure)
- Votes (user voting records)
- Ratings (5-star ratings with reviews)
- Comments (threaded with moderation)
- SavedDeals (user favorites)
- Notifications (alert preferences)
- Categories (with icons and metadata)
- Stores (with logos and details)
```

#### 18. API Routes
```typescript
// Files: src/app/api/*/route.ts

API Endpoints:
- /api/deals (CRUD operations)
- /api/votes (voting system)
- /api/comments (comment system)
- /api/auth (authentication)
- /api/admin (admin operations)
- /api/search (search functionality)
- /api/notifications (alert system)
```

### Phase 8: Performance & Production

#### 19. Performance Optimization
- Image optimization, caching strategies (Redis), database query optimization
- Code splitting, CDN integration, Core Web Vitals optimization

#### 20. Testing & Deployment
- Unit tests for components, integration tests for API routes, E2E tests for user flows
- SEO audit and validation, performance testing, production deployment

## Key Technical Decisions

### SEO-First Architecture
- **All public pages are SSR** for optimal search engine indexing
- **Dynamic meta tags** generated for each deal, category, and search result
- **Structured data** (JSON-LD) for rich snippets
- **URL structure** optimized for SEO
- **Sitemap generation** for all content

### Performance Considerations
- **Next.js App Router** for optimal SSR performance
- **Static generation** where possible (categories, stores)
- **Incremental Static Regeneration** for frequently updated content
- **Image optimization** with Next.js Image component
- **Font optimization** with next/font

### Component Architecture
- **Server Components** by default for better performance
- **Client Components** only when interactivity is needed
- **Reusable UI components** with consistent design system
- **Type-safe** development with TypeScript

## File Structure
```
nicedeals-app/
├── src/
│   ├── app/                    # App Router pages
│   │   ├── (auth)/            # Auth-related pages
│   │   ├── admin/             # Admin panel
│   │   ├── api/               # API routes
│   │   ├── browse/            # Browse deals
│   │   ├── categories/        # Category pages
│   │   ├── deals/             # Deal detail pages
│   │   ├── stores/            # Store pages
│   │   └── globals.css        # Global styles
│   ├── components/            # Reusable components
│   │   ├── admin/             # Admin components
│   │   ├── auth/              # Auth components
│   │   ├── deals/             # Deal-related components
│   │   ├── ui/                # Base UI components (Shadcn)
│   │   ├── Header.tsx
│   │   └── Footer.tsx
│   ├── lib/                   # Utilities and configs
│   │   ├── auth.ts
│   │   ├── db.ts
│   │   ├── utils.ts
│   │   └── validations.ts
│   └── types/                 # TypeScript definitions
├── public/                    # Static assets
├── components.json            # Shadcn/UI config
├── tailwind.config.ts         # Tailwind configuration
└── next.config.ts             # Next.js configuration
```

## Current Development Status

### Currently Running
- ✅ Development server at `http://localhost:3010`
- ✅ Homepage fully functional with mock data
- ✅ Header and Footer components integrated
- ✅ Orange color scheme applied
- ✅ Custom fonts (Instrument Serif + Inter) working

### Next Immediate Steps
1. **Browse Deals Page** - Create SSR page with filters and search
2. **Deal Detail Page** - Dynamic routes with full deal information
3. **Categories/Stores Pages** - Index and detail pages
4. **Database Integration** - Set up data layer with Base44
5. **Authentication** - Google OAuth implementation

This comprehensive guide ensures no aspect of the build will be overlooked, combining all product requirements with the complete development roadmap in a single authoritative document.
