import { NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get("q") || ""
    const category = searchParams.get("category") || ""
    const store = searchParams.get("store") || ""
    const minPrice = searchParams.get("minPrice")
    const maxPrice = searchParams.get("maxPrice")
    const sortBy = searchParams.get("sort") || "recent"
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "12")
    
    const skip = (page - 1) * limit

    // Build where clause for advanced filtering
    const where: {
      status?: string;
      OR?: Array<
        | { title?: { contains: string } } 
        | { description?: { contains: string } }
        | { store?: { name?: { contains: string } } }
        | { category?: { name?: { contains: string } } }
      >;
      category?: { slug?: string };
      store?: { slug?: string };
      dealPrice?: { gte?: number; lte?: number };
      expiresAt?: { not: null };
    } = {
      status: "ACTIVE",
    }

    // Full-text search across title and description
    if (query) {
      where.OR = [
        { title: { contains: query } },
        { description: { contains: query } },
        { store: { name: { contains: query } } },
        { category: { name: { contains: query } } },
      ]
    }

    // Category filter
    if (category) {
      where.category = { slug: category }
    }

    // Store filter
    if (store) {
      where.store = { slug: store }
    }

    // Price range filter
    if (minPrice || maxPrice) {
      where.dealPrice = {}
      if (minPrice) where.dealPrice.gte = parseFloat(minPrice)
      if (maxPrice) where.dealPrice.lte = parseFloat(maxPrice)
    }

    // Build orderBy clause
    let orderBy: 
      | { createdAt: "desc" }
      | { upvotes: "desc" }
      | { dealPrice: "asc" | "desc" }
      | { discountPercentage: "desc" }
      | { averageRating: "desc" }
      | { expiresAt: "asc" } = { createdAt: "desc" } // default: recent

    switch (sortBy) {
      case "popular":
        orderBy = { upvotes: "desc" }
        break
      case "price-low":
        orderBy = { dealPrice: "asc" }
        break
      case "price-high":
        orderBy = { dealPrice: "desc" }
        break
      case "discount":
        orderBy = { discountPercentage: "desc" }
        break
      case "rating":
        orderBy = { averageRating: "desc" }
        break
      case "expiring":
        orderBy = { expiresAt: "asc" }
        where.expiresAt = { not: null }
        break
    }

    // Execute search query
    const [deals, total, suggestions] = await Promise.all([
      db.deal.findMany({
        where,
        orderBy,
        skip,
        take: limit,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          store: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          _count: {
            select: {
              votes: true,
              comments: true,
              savedDeals: true,
            },
          },
        },
      }),
      db.deal.count({ where }),
      // Get search suggestions if query is provided
      query ? db.deal.findMany({
        where: {
          status: "ACTIVE",
          OR: [
            { title: { contains: query } },
            { store: { name: { contains: query } } },
            { category: { name: { contains: query } } },
          ],
        },
        select: {
          title: true,
          store: {
            select: {
              name: true,
            },
          },
          category: {
            select: {
              name: true,
            },
          },
        },
        take: 5,
        distinct: ["title"],
      }) : [],
    ])

    // Generate search suggestions
    const searchSuggestions = query ? [
      ...new Set([
        ...suggestions.map(d => d.title),
        ...suggestions.map(d => d.store.name),
        ...suggestions.map(d => d.category.name),
      ])
    ].slice(0, 5) : []

    return NextResponse.json({
      deals,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
      query,
      filters: {
        category,
        store,
        minPrice: minPrice ? parseFloat(minPrice) : null,
        maxPrice: maxPrice ? parseFloat(maxPrice) : null,
        sortBy,
      },
      suggestions: searchSuggestions,
    })
  } catch (error) {
    console.error("Error searching deals:", error)
    return NextResponse.json(
      { error: "Failed to search deals" },
      { status: 500 }
    )
  }
}
