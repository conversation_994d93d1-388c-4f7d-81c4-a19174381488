import { redirect } from "next/navigation"
import { requireAdmin } from "@/lib/auth"
import AdminLayout from "@/components/admin/AdminLayout"
import { getAdminStats } from "@/lib/admin"

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'

export default async function AdminDashboard() {
  try {
    await requireAdmin()
  } catch {
    redirect("/auth/signin")
  }

  const stats = await getAdminStats()

  return (
    <AdminLayout stats={stats} currentPage="dashboard">
      <div className="space-y-6">
        {/* Additional dashboard content can go here */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Welcome to Admin Dashboard</h3>
          <p className="text-gray-600">
            Use the navigation on the left to manage different aspects of your NiceDeals platform.
          </p>
        </div>
      </div>
    </AdminLayout>
  )
}
