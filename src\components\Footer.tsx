"use client"

import Link from "next/link";
import { useSession } from "next-auth/react";
import { useState } from "react";
import { toast } from "react-hot-toast";

export default function Footer() {
  const { data: session } = useSession();
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) {
      toast.error("Please enter your email address");
      return;
    }
    setLoading(true);
    try {
      const res = await fetch("/api/newsletter", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email }),
      });
      const data = await res.json();
      if (res.ok) {
        if (data.message && data.message.toLowerCase().includes("already")) {
          toast.success("You are already subscribed to the NiceDeals daily update");
        } else {
          toast.success("You've been registered for the NiceDeals daily update");
        }
        setEmail("");
      } else {
        toast.error(data.message || "Failed to subscribe");
      }
    } catch (err) {
      console.error("Newsletter subscribe error", err);
      toast.error("Something went wrong. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <footer className="bg-white border-t border-border text-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8">
          {/* About */}
          <div>
            <h3 className="text-lg font-semibold mb-4">About NiceDeals</h3>
            <p className="text-sm leading-relaxed text-gray-600">
              NiceDeals helps you discover amazing discounts, share great
              offers, and never miss a bargain again.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-3 text-sm">
              <li>
                <Link href="/browse" className="hover:text-orange-600">
                  Browse Deals
                </Link>
              </li>
              <li>
                <Link href="/browse?sort=nice" className="hover:text-orange-600">
                  Nice Deals
                </Link>
              </li>
              <li>
                <Link href="/categories" className="hover:text-orange-600">
                  Categories
                </Link>
              </li>
              <li>
                <Link href="/stores" className="hover:text-orange-600">
                  Stores
                </Link>
              </li>
            </ul>
          </div>

          {/* Customer Support */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Customer Support</h3>
            <ul className="space-y-3 text-sm">
              <li>
                <Link href="/help" className="hover:text-orange-600">
                  Help Center
                </Link>
              </li>
              <li>
                <Link href="/help#contact-us" className="hover:text-orange-600">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link href="/help#faq" className="hover:text-orange-600">
                  FAQ
                </Link>
              </li>
              {session?.user?.role === "ADMIN" && (
                <li>
                  <Link href="/admin" className="text-orange-600 font-medium hover:underline">
                    Admin Panel
                  </Link>
                </li>
              )}
            </ul>
          </div>

          {/* Subscribe */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Subscribe</h3>
            <p className="text-sm text-gray-600 mb-4">
              Get the latest deals delivered to your inbox.
            </p>

            <form onSubmit={handleSubscribe} className="flex w-full max-w-xs sm:max-w-none">
              <label htmlFor="footer-email" className="sr-only">
                Email address
              </label>
              <input
                id="footer-email"
                type="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={loading}
                className="flex-grow px-4 py-2 border border-gray-300 rounded-l-full focus:outline-none focus:ring-2 focus:ring-orange-500 text-sm"
                placeholder="Your email"
              />
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-r-full text-sm font-semibold"
              >
                {loading ? "..." : "Subscribe"}
              </button>
            </form>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-12 pt-8 border-t border-gray-200 flex flex-col md:flex-row justify-between items-center text-sm text-gray-600">
          <p>&copy; {new Date().getFullYear()} NiceDeals. All rights reserved.</p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link href="/help#terms" className="hover:text-orange-600">
              Terms
            </Link>
            <Link href="/help#privacy" className="hover:text-orange-600">
              Privacy
            </Link>
            <Link href="/help#cookies" className="hover:text-orange-600">
              Cookies
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
