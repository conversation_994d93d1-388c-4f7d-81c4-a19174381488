import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  try {
    const { title } = await req.json();
    console.log("[AI IMPROVE TITLE] Incoming title:", title);
    if (!title) {
      console.error("No title provided");
      return NextResponse.json({ success: false, error: "Title is required" }, { status: 400 });
    }

    // Use the AI_TITLE_PROMPT from env
    const prompt = process.env.AI_TITLE_PROMPT?.replace("{{TITLE}}", title);
    console.log("[AI IMPROVE TITLE] Using prompt:", prompt);
    if (!prompt) {
      console.error("AI_TITLE_PROMPT not set or invalid");
      return NextResponse.json({ success: false, error: "AI_TITLE_PROMPT not set" }, { status: 500 });
    }
    if (!process.env.OPENROUTER_API_KEY) {
      console.error("OPENROUTER_API_KEY not set");
      return NextResponse.json({ success: false, error: "OPENROUTER_API_KEY not set" }, { status: 500 });
    }

    // Call OpenRouter AI API using the free model and correct headers
    const openrouterBody = {
      //model: "deepseek/deepseek-chat:free",
      model: "qwen/qwen3-32b:free",
      messages: [
        { role: "user", content: prompt }
      ]
    };
    console.log("[AI IMPROVE TITLE] Posting to OpenRouter:", JSON.stringify(openrouterBody, null, 2));

    const openrouterRes = await fetch("https://openrouter.ai/api/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${process.env.OPENROUTER_API_KEY}`,
        "Content-Type": "application/json",
        "HTTP-Referer": process.env.FRONTEND_URL || "http://localhost:3010",
        "X-Title": "NiceDeals"
      },
      body: JSON.stringify(openrouterBody)
    });

    console.log("[AI IMPROVE TITLE] OpenRouter status:", openrouterRes.status);
    let aiData;
    try {
      aiData = await openrouterRes.json();
      console.log("[AI IMPROVE TITLE] OpenRouter response:", aiData);
    } catch (err) {
      console.error("[AI IMPROVE TITLE] Failed to parse OpenRouter response:", err);
      return NextResponse.json({ success: false, error: "Failed to parse AI response" }, { status: 500 });
    }

    if (!openrouterRes.ok) {
      return NextResponse.json({ success: false, error: "AI request failed", aiData }, { status: 500 });
    }

    let improvedTitle = aiData.choices?.[0]?.message?.content?.trim();

    if (!improvedTitle) {
      return NextResponse.json({ success: false, error: "No title returned from AI", aiData }, { status: 500 });
    }

    // Strip surrounding quotes from AI response
    improvedTitle = improvedTitle.replace(/^["']+|["']+$/g, '').trim();

    return NextResponse.json({ success: true, title: improvedTitle, aiData });
  } catch (error) {
    console.error("[AI IMPROVE TITLE] Server error:", error);
    return NextResponse.json({ success: false, error: "Server error", details: String(error) }, { status: 500 });
  }
}
