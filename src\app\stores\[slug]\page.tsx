import { notFound } from "next/navigation"
import { db } from "@/lib/db"
import DealCard from "@/components/DealCard"
import { Metadata } from "next"
import Link from "next/link"
import { ShoppingBag } from "lucide-react"

// Use ISR for production build performance
// NOTE: Next.js only allows literal values - no env vars, imports, or functions
export const revalidate = 60

interface StorePageProps {
  params: Promise<{ slug: string }>
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

async function getStore(slug: string) {
  const store = await db.store.findFirst({
    where: { 
      slug: slug,
      active: true 
    }
  })
  
  if (!store) {
    return null
  }
  
  return store
}

async function getStoreDeals(storeId: number, page: number = 1, limit: number = 12) {
  const skip = (page - 1) * limit
  
  const [deals, total] = await Promise.all([
    db.deal.findMany({
      where: {
        status: "ACTIVE",
        storeId: storeId
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        store: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            votes: true,
            comments: true,
            savedDeals: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,
    }),
    db.deal.count({
      where: {
        status: "ACTIVE",
        storeId: storeId
      }
    })
  ])

  return { deals, total, pages: Math.ceil(total / limit) }
}

export async function generateMetadata({ params }: StorePageProps): Promise<Metadata> {
  const { slug } = await params
  const store = await getStore(slug)
  
  if (!store) {
    return {
      title: "Store Not Found | NiceDeals"
    }
  }
  
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3010";
  const ogImage = store.logoUrl ? store.logoUrl : "/nicedeals-logo.png";
  const fullImageUrl = ogImage.startsWith("http") ? ogImage : `${baseUrl}${ogImage}`;

  return {
    title: `${store.name} Deals | NiceDeals`,
    description: `Find the best deals from ${store.name}. ${store.description || `Browse ${store.name} products and discounts.`}`,
    openGraph: {
      title: `${store.name} Deals | NiceDeals`,
      description: `Find the best deals from ${store.name}.`,
      images: [fullImageUrl]
    },
    twitter: {
      card: "summary",
      title: `${store.name} Deals | NiceDeals`,
      description: `Find the best deals from ${store.name}.`,
      images: [fullImageUrl]
    }
  }
}

export default async function StorePage({ params, searchParams }: StorePageProps) {
  const { slug } = await params
  const { page: pageParam } = await searchParams
  
  const currentPage = pageParam ? parseInt(Array.isArray(pageParam) ? pageParam[0] : pageParam) : 1
  
  const store = await getStore(slug)
  
  if (!store) {
    notFound()
  }
  
  const { deals, total, pages } = await getStoreDeals(store.id, currentPage)
  
  // Transform deals to match DealCard interface
  const transformedDeals = deals.map(deal => {
    // Calculate isNew server-side to avoid hydration mismatch
    const isNew = (() => {
      if (!deal.createdAt) return false;
      const createdAt = new Date(deal.createdAt);
      const now = new Date();
      const diffInHours = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
      return diffInHours <= 24;
    })();

    return {
      id: deal.id,
      slug: deal.slug,
      title: deal.title,
      description: deal.description,
      originalPrice: deal.originalPrice ? Number(deal.originalPrice) : 0,
      dealPrice: Number(deal.dealPrice),
      discount: deal.discountPercentage || 0,
      store: {
        id: deal.store.id,
        name: deal.store.name,
        slug: deal.store.slug,
      },
      category: {
        id: deal.category.id,
        name: deal.category.name,
        slug: deal.category.slug,
      },
      dealUrl: deal.dealUrl,
      imageUrl: deal.imageUrl || "",
      upvotes: deal.upvotes,
      downvotes: deal.downvotes,
      couponCode: deal.couponCode || undefined,
      expiresAt: deal.expiresAt?.toISOString(),
      createdDate: deal.createdAt.toISOString(),
      isNew: isNew,
    };
  })

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
                     <nav className="text-sm text-gray-500 mb-4">
             <Link href="/" className="hover:text-orange-600">Home</Link>
             <span className="mx-2">›</span>
             <Link href="/stores" className="hover:text-orange-600">Stores</Link>
             <span className="mx-2">›</span>
             <span className="text-gray-900">{store.name}</span>
           </nav>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              {store.logoUrl && (
                <img 
                  src={store.logoUrl} 
                  alt={`${store.name} logo`}
                  className="w-16 h-16 object-contain rounded-lg border border-gray-200 bg-white p-2"
                />
              )}
              <div>
                <h1 className="text-4xl font-bold text-gray-900 mb-2">
                  {store.name} Deals
                </h1>
                {store.description && (
                  <p className="text-gray-600 text-lg">
                    {store.description}
                  </p>
                )}
                {store.url && (
                  <a 
                    href={store.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-orange-600 hover:text-orange-700 text-sm"
                  >
                    Visit Store →
                  </a>
                )}
              </div>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-orange-600">
                {total}
              </div>
              <div className="text-sm text-gray-500">
                active deals
              </div>
            </div>
          </div>
        </div>

        {/* Deals Grid */}
        {deals.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
              {transformedDeals.map((deal) => (
                <DealCard key={deal.id} deal={deal} />
              ))}
            </div>

            {/* Pagination */}
            {pages > 1 && (
              <div className="flex justify-center items-center space-x-2">
                {currentPage > 1 && (
                  <a
                    href={`/stores/${slug}?page=${currentPage - 1}`}
                    className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                  >
                    Previous
                  </a>
                )}
                
                {Array.from({ length: Math.min(5, pages) }, (_, i) => {
                  const page = Math.max(1, currentPage - 2) + i
                  if (page > pages) return null
                  
                  return (
                    <a
                      key={page}
                      href={`/stores/${slug}?page=${page}`}
                      className={`px-4 py-2 rounded-md ${
                        page === currentPage
                          ? "bg-orange-600 text-white"
                          : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
                      }`}
                    >
                      {page}
                    </a>
                  )
                })}
                
                {currentPage < pages && (
                  <a
                    href={`/stores/${slug}?page=${currentPage + 1}`}
                    className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                  >
                    Next
                  </a>
                )}
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <ShoppingBag className="w-12 h-12 text-orange-600" />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              No deals found
            </h2>
            <p className="text-gray-600 mb-6">
              There are currently no active deals from {store.name}.
            </p>
                         <Link
               href="/browse"
               className="bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700 transition-colors"
             >
               Browse All Deals
             </Link>
          </div>
        )}
      </div>
    </div>
  )
} 