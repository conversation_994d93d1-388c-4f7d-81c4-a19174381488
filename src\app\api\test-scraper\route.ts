import { NextRequest, NextResponse } from "next/server";
import { scrapeProductPage, formatScrapedContentForAI } from "@/lib/scraper";

export async function POST(req: NextRequest) {
  try {
    const { url } = await req.json();
    if (!url) {
      return NextResponse.json({ success: false, error: "URL is required" }, { status: 400 });
    }

    console.log('[TEST SCRAPER] Testing URL:', url);
    const scrapedContent = await scrapeProductPage(url);
    
    const formattedContent = formatScrapedContentForAI(scrapedContent, "Test Product", url);
    
    return NextResponse.json({ 
      success: true, 
      scrapedContent,
      formattedContent,
      message: "Scraping completed successfully"
    });
  } catch (error) {
    console.error('[TEST SCRAPER] Error:', error);
    return NextResponse.json({ 
      success: false, 
      error: "Server error", 
      details: String(error) 
    }, { status: 500 });
  }
} 