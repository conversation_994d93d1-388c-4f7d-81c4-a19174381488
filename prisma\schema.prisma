generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id                  Int                  @id @default(autoincrement())
  name                String?
  email               String               @unique
  emailVerified       DateTime?
  image               String?
  password            String?
  role                String               @default("USER")
  bio                 String?
  location            String?
  reputation          Int                  @default(0)
  resetToken          String?              @map("reset_token")
  resetTokenExpires   DateTime?            @map("reset_token_expires")
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  newsletter          Boolean?
  accounts            Account[]
  comments            Comment[]
  deals               Deal[]
  emailChangeRequests EmailChangeRequest[]
  notifications       Notification[]
  ratings             Rating[]
  savedDeals          SavedDeal[]
  sessions            Session[]
  votes               Vote[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            Int
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       Int
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

model EmailChangeRequest {
  id           String   @id @default(cuid())
  userId       Int      @map("user_id")
  currentEmail String   @map("current_email")
  newEmail     String   @map("new_email")
  token        String   @unique
  expiresAt    DateTime @map("expires_at")
  createdAt    DateTime @default(now()) @map("created_at")
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("email_change_requests")
}

model Deal {
  id                 Int         @id @default(autoincrement())
  slug               String      @unique
  title              String
  description        String
  originalPrice      Decimal?    @map("original_price")
  dealPrice          Decimal     @map("deal_price")
  discountPercentage Int?        @map("discount_percentage")
  storeId            Int         @map("store_id")
  categoryId         Int         @map("category_id")
  dealUrl            String      @map("deal_url")
  imageUrl           String?     @map("image_url")
  thumbnailUrl       String?     @map("thumbnail_url")
  couponCode         String?     @map("coupon_code")
  expiresAt          DateTime?   @map("expires_at")
  status             String      @default("PENDING")
  featured           Boolean     @default(false)
  upvotes            Int         @default(0)
  downvotes          Int         @default(0)
  averageRating      Float       @default(0) @map("average_rating")
  ratingCount        Int         @default(0) @map("rating_count")
  viewCount          Int         @default(0) @map("view_count")
  source             String      @default("manual")
  externalId         String?     @map("external_id")
  temperature        Float?
  niceAt             DateTime?   @map("nice_at")
  createdAt          DateTime    @default(now()) @map("created_at")
  updatedAt          DateTime    @updatedAt @map("updated_at")
  authorId           Int         @map("author_id")
  comments           Comment[]
  images             DealImage[]
  category           Category    @relation(fields: [categoryId], references: [id])
  store              Store       @relation(fields: [storeId], references: [id])
  author             User        @relation(fields: [authorId], references: [id], onDelete: Cascade)
  ratings            Rating[]
  savedDeals         SavedDeal[]
  votes              Vote[]

  @@index([niceAt])
  @@map("deals")
}

model DealImage {
  id           String   @id @default(cuid())
  dealId       Int      @map("deal_id")
  imageUrl     String   @map("image_url")
  thumbnailUrl String?  @map("thumbnail_url")
  altText      String?  @map("alt_text")
  caption      String?
  width        Int?
  height       Int?
  fileSize     Int?     @map("file_size")
  mimeType     String?  @map("mime_type")
  sortOrder    Int      @default(0) @map("sort_order")
  isMain       Boolean  @default(false) @map("is_main")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  deal         Deal     @relation(fields: [dealId], references: [id], onDelete: Cascade)

  @@map("deal_images")
}

model Vote {
  id        String   @id @default(cuid())
  type      String
  dealId    Int      @map("deal_id")
  userId    Int      @map("user_id")
  createdAt DateTime @default(now()) @map("created_at")
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  deal      Deal     @relation(fields: [dealId], references: [id], onDelete: Cascade)

  @@unique([dealId, userId])
  @@map("votes")
}

model Rating {
  id        String   @id @default(cuid())
  rating    Int
  review    String?
  dealId    Int      @map("deal_id")
  userId    Int      @map("user_id")
  createdAt DateTime @default(now()) @map("created_at")
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  deal      Deal     @relation(fields: [dealId], references: [id], onDelete: Cascade)

  @@unique([dealId, userId])
  @@map("ratings")
}

model Comment {
  id              String    @id @default(cuid())
  content         String
  status          String    @default("APPROVED")
  flaggedCount    Int       @default(0) @map("flagged_count")
  dealId          Int       @map("deal_id")
  userId          Int       @map("user_id")
  parentCommentId String?   @map("parent_comment_id")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  parentComment   Comment?  @relation("CommentReplies", fields: [parentCommentId], references: [id])
  replies         Comment[] @relation("CommentReplies")
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  deal            Deal      @relation(fields: [dealId], references: [id], onDelete: Cascade)

  @@map("comments")
}

model SavedDeal {
  id        String   @id @default(cuid())
  dealId    Int      @map("deal_id")
  userId    Int      @map("user_id")
  createdAt DateTime @default(now()) @map("created_at")
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  deal      Deal     @relation(fields: [dealId], references: [id], onDelete: Cascade)

  @@unique([dealId, userId])
  @@map("saved_deals")
}

model Notification {
  id        String   @id @default(cuid())
  type      String
  value     String
  active    Boolean  @default(true)
  userId    Int      @map("user_id")
  createdAt DateTime @default(now()) @map("created_at")
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model Category {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  slug        String   @unique
  description String?
  icon        String?
  active      Boolean  @default(true)
  sortOrder   Int      @default(0) @map("sort_order")
  dealCount   Int      @default(0) @map("deal_count")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  deals       Deal[]

  @@map("categories")
}

model Store {
  id              Int      @id @default(autoincrement())
  name            String   @unique
  slug            String   @unique
  url             String
  logoUrl         String?  @map("logo_url")
  description     String?
  active          Boolean  @default(true)
  featured        Boolean  @default(false)
  sortOrder       Int      @default(0) @map("sort_order")
  dealCount       Int      @default(0) @map("deal_count")
  averageDiscount Float    @default(0) @map("average_discount")
  rating          Float    @default(0)
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  deals           Deal[]

  @@map("stores")
}

model ScrapeLog {
  id           Int       @id @default(autoincrement())
  source       String
  status       String
  dealsFound   Int       @default(0) @map("deals_found")
  dealsAdded   Int       @default(0) @map("deals_added")
  dealsSkipped Int       @default(0) @map("deals_skipped")
  error        String?
  dataFile     String?   @map("data_file")
  diagnostics  String?
  startedAt    DateTime  @default(now()) @map("started_at")
  completedAt  DateTime? @map("completed_at")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")

  @@map("scrape_logs")
}
