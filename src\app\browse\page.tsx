import { Suspense } from 'react'
import { db } from "@/lib/db"
import BrowseContent from './BrowseContent'

// Force fresh data on every request for up-to-date deals
export const dynamic = 'force-dynamic'

interface SearchParams {
  search?: string
  category?: string
  store?: string
  sort?: string
  view?: string
}

interface BrowsePageProps {
  searchParams: Promise<SearchParams>
}

async function getInitialData() {
  const [deals, categories, stores] = await Promise.all([
    db.deal.findMany({
      where: { status: 'ACTIVE' },
      include: {
        store: { select: { id: true, name: true, slug: true } },
        category: { select: { id: true, name: true, slug: true } },
      },
      orderBy: { createdAt: 'desc' },
    }),
    db.category.findMany({
      select: { name: true },
      orderBy: { name: 'asc' },
    }),
    db.store.findMany({
      select: { name: true },
      orderBy: { name: 'asc' },
    }),
  ])

  return {
    deals: deals.map(deal => ({
      ...deal,
      originalPrice: deal.originalPrice ? Number(deal.originalPrice) : null,
      dealPrice: Number(deal.dealPrice),
      discountPercentage: deal.discountPercentage ? Number(deal.discountPercentage) : null,
      imageUrl: deal.imageUrl || undefined, // Convert null to undefined
      createdAt: deal.createdAt.getTime(),
      updatedAt: deal.updatedAt.getTime(),
      expiresAt: deal.expiresAt?.getTime() || null,
      niceAt: deal.niceAt?.getTime() || null,
    })),
    categories: ['All Categories', ...categories.map(c => c.name)],
    stores: ['All Stores', ...stores.map(s => s.name)],
  }
}

export default async function BrowsePage({ searchParams }: BrowsePageProps) {
  const params = await searchParams
  const initialData = await getInitialData()

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <BrowseContent 
        initialData={initialData}
        searchParams={params}
      />
    </Suspense>
  )
}
