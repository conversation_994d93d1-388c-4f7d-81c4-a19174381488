import { NextRequest, NextResponse } from "next/server";
import { scrapeProductPage, formatScrapedContentForAI } from "@/lib/scraper";

export async function POST(req: NextRequest) {
  try {
    const { url, title } = await req.json();
    if (!url || !title) {
      return NextResponse.json({ success: false, error: "URL and title are required" }, { status: 400 });
    }

    // First, scrape the webpage content
    console.log('[AI IMPROVE DESC] Scraping URL:', url);
    const scrapedContent = await scrapeProductPage(url);
    
    // Format the scraped content for AI processing
    const webpageContent = formatScrapedContentForAI(scrapedContent, title, url);
    console.log('[AI IMPROVE DESC] Webpage Content:', webpageContent);
    
    // Create a new prompt that works with scraped content instead of URLs
    const prompt = `Please analyze this product information and create an improved description for a UK deals website. 

PRODUCT INFORMATION SCRAPED FROM URL ${url}:
${webpageContent}

INSTRUCTIONS:
- Create a compelling product description focusing on key features and benefits
- Use bullet points with emoji categorization where appropriate
- Keep under 200 words
- Focus on what makes this product worth buying
- Ignore any prices mentioned in the content
- Make it engaging for UK deal hunters but do not mention who this description is for, or that it is for a UK deals website
- If the scraped content you are attempting to use to build the description is nonsensical and bears no relationship to the product ( ${title} ) then do NOT use your memory to attempt to create a description, just return "COULD NOT CREATE DESCRIPTION"`;

    if (!process.env.OPENROUTER_API_KEY) {
      return NextResponse.json({ success: false, error: "OPENROUTER_API_KEY not set" }, { status: 500 });
    }

    // Call OpenRouter AI API using the free model and correct headers
    const openrouterBody = {
      //model: "deepseek/deepseek-chat:free",
      //model: "deepseek/deepseek-r1-0528:free",
      model: "qwen/qwen3-32b:free",
      messages: [
        { role: "user", content: prompt }
      ]
    };

    const openrouterRes = await fetch("https://openrouter.ai/api/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${process.env.OPENROUTER_API_KEY}`,
        "Content-Type": "application/json",
        "HTTP-Referer": process.env.FRONTEND_URL || "http://localhost:3010",
        "X-Title": "NiceDeals"
      },
      body: JSON.stringify(openrouterBody)
    });

    let aiData;
    try {
      aiData = await openrouterRes.json();
      console.log('[AI IMPROVE DESC] Raw DeepSeek Response:', JSON.stringify(aiData, null, 2));
    } catch {
      return NextResponse.json({ success: false, error: "Failed to parse AI response" }, { status: 500 });
    }

    if (!openrouterRes.ok) {
      console.log('[AI IMPROVE DESC] OpenRouter Error - Status:', openrouterRes.status, 'Response:', aiData);
      return NextResponse.json({ success: false, error: "AI request failed", aiData }, { status: 500 });
    }

    let description = aiData.choices?.[0]?.message?.content?.trim() || "";

    // Clean up description (remove markdown, bullets, etc.)
    description = description
      .replace(/^\*\*Description:?\*\*/i, "")
      .replace(/^Description:?/i, "")
      .replace(/\*\*/g, "")
      .replace(/\*/g, "")
      .replace(/^[-*•]/g, "")
      .trim();

    return NextResponse.json({ success: true, description });
  } catch (error) {
    return NextResponse.json({ success: false, error: "Server error", details: String(error) }, { status: 500 });
  }
}
