"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { ThumbsUp, ThumbsDown } from "lucide-react"
import { NDFireIcon } from "@/components/icons/NiceDealsIcons"

interface DealScoreLiveProps {
  dealId: number
  initialUpvotes: number
  initialDownvotes: number
}

export default function DealScoreLive({
  dealId,
  initialUpvotes,
  initialDownvotes,
}: DealScoreLiveProps) {
  const { data: session } = useSession()
  const router = useRouter()
  const [upvotes, setUpvotes] = useState(initialUpvotes)
  const [downvotes, setDownvotes] = useState(initialDownvotes)
  const [userVote, setUserVote] = useState<"UPVOTE" | "DOWNVOTE" | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    if (session?.user) {
      fetch(`/api/deals/${dealId}/vote`)
        .then((res) => res.json())
        .then((data) => {
          if (data.userVote) {
            setUserVote(data.userVote)
          }
        })
    }
  }, [dealId, session])

  const handleVote = async (type: "UPVOTE" | "DOWNVOTE") => {
    if (isLoading) return
    if (!session?.user) {
      router.push(`/auth/signin?callbackUrl=${window.location.pathname}`)
      return
    }

    if (userVote === type) {
      const voteType = type.toLowerCase()
      const confirmed = confirm(
        `You have already ${voteType}d this deal. Do you want to remove your ${voteType}?`
      )
      if (!confirmed) return
    } else if (userVote && userVote !== type) {
      alert(
        `You have already ${userVote.toLowerCase()}d this deal. Please remove your vote before casting a new one.`
      )
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch(`/api/deals/${dealId}/vote`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ type }),
      })
      const data = await response.json()
      if (response.ok) {
        setUpvotes(data.deal.upvotes)
        setDownvotes(data.deal.downvotes)
        setUserVote(data.userVote)
      } else {
        alert(data.message || "An error occurred.")
      }
    } catch {
      alert("An error occurred while voting.")
    } finally {
      setIsLoading(false)
    }
  }

  const score = upvotes - downvotes

  return (
    <div className="text-white p-4 rounded-lg mb-8 shadow-sm bg-gradient-to-r from-deal-orange to-deal-orange-dark">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="bg-white/20 p-2 rounded-lg">
            <NDFireIcon className="w-6 h-6 text-white" />
          </div>
          <div>
            <div className="text-2xl font-bold text-white">Deal Score: {score}</div>
            <div className="text-sm text-white/80">
              Based on community votes
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-6">
          <div className="flex flex-col items-center">
            <button
              onClick={() => handleVote("UPVOTE")}
              className={`flex items-center gap-2 p-2 rounded-lg transition-colors ${
                userVote === "UPVOTE" 
                  ? "border-2 border-green-400 bg-green-600 text-white"
                  : "border-2 border-green-400 bg-green-600 hover:bg-green-700 text-white"
              }`}
              aria-label="Nice"
              disabled={isLoading}
            >
              <ThumbsUp className={`w-5 h-5 ${userVote === "UPVOTE" ? "fill-white" : ""}`} />
              <span className="text-sm font-medium">Nice</span>
            </button>
            <span className="text-lg font-semibold mt-1">{upvotes}</span>
          </div>
          <div className="flex flex-col items-center">
            <button
              onClick={() => handleVote("DOWNVOTE")}
              className={`flex items-center gap-2 p-2 rounded-lg transition-colors ${
                userVote === "DOWNVOTE" 
                  ? "border-2 border-red-400 bg-red-600  text-white" 
                  : "border-2 border-red-400 bg-red-600 hover:bg-red-700 text-white"
              }`}
              aria-label="Not Nice"
              disabled={isLoading}
            >
              <ThumbsDown className={`w-5 h-5 ${userVote === "DOWNVOTE" ? "fill-white" : ""}`} />
              <span className="text-sm font-medium">Not Nice</span>
            </button>
            <span className="text-lg font-semibold mt-1">{downvotes}</span>
          </div>
        </div>
      </div>
    </div>
  )
}
