import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "@/lib/auth"
import { validateImageFile, processImageUrls } from "@/lib/image-utils"
import { mkdir } from "fs/promises"
import { join } from "path"
import { existsSync } from "fs"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession()
    
    if (!session?.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      )
    }

    const contentType = request.headers.get("content-type")
    
    if (contentType?.includes("multipart/form-data")) {
      // Handle file uploads
      return handleFileUploads(request)
    } else if (contentType?.includes("application/json")) {
      const data = await request.json()
      
      // Check if this is a request to localize an external image
      if (data.localizeUrl && data.dealId) {
        return handleExternalImageLocalization(data.localizeUrl, data.dealId)
      } else {
        // Handle URL submissions with already parsed data
        return handleUrlSubmissionsWithData(data)
      }
    } else {
      return NextResponse.json(
        { message: "Invalid content type" },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error("Image upload error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

async function handleFileUploads(request: NextRequest) {
  try {
    const formData = await request.formData()
    const dealId = formData.get("dealId") as string
    
    if (!dealId) {
      return NextResponse.json(
        { message: "Deal ID is required" },
        { status: 400 }
      )
    }

    const uploadedImages: Array<{
      url: string
      thumbnailUrl: string
      width: number
      height: number
      size: number
      type: 'main' | 'gallery'
    }> = []

    const errors: string[] = []

    // Process main image
    const mainImageFile = formData.get("mainImage") as File
    if (mainImageFile && mainImageFile.size > 0) {
      const validation = validateImageFile(mainImageFile)
      if (!validation.valid) {
        errors.push(`Main image: ${validation.error}`)
      } else {
        try {
          const uploadResult = await saveImageFile(mainImageFile, dealId, 'main')
          if (uploadResult.success && uploadResult.url) {
            uploadedImages.push({
              url: uploadResult.url,
              thumbnailUrl: uploadResult.thumbnailUrl || uploadResult.url,
              width: 800, // Would get actual dimensions in production
              height: 600,
              size: mainImageFile.size,
              type: 'main'
            })
          } else {
            errors.push(`Main image upload failed: ${uploadResult.error}`)
          }
        } catch (error) {
          errors.push(`Main image upload failed: ${error}`)
        }
      }
    }

    // Process gallery images
    const galleryFiles = formData.getAll("galleryImages") as File[]
    for (let i = 0; i < galleryFiles.length; i++) {
      const file = galleryFiles[i]
      if (file && file.size > 0) {
        const validation = validateImageFile(file)
        if (!validation.valid) {
          errors.push(`Gallery image ${i + 1}: ${validation.error}`)
        } else {
          try {
            const uploadResult = await saveImageFile(file, dealId, 'gallery')
            if (uploadResult.success && uploadResult.url) {
              uploadedImages.push({
                url: uploadResult.url,
                thumbnailUrl: uploadResult.thumbnailUrl || uploadResult.url,
                width: 800,
                height: 600,
                size: file.size,
                type: 'gallery'
              })
            } else {
              errors.push(`Gallery image ${i + 1} upload failed: ${uploadResult.error}`)
            }
          } catch (error) {
            errors.push(`Gallery image ${i + 1} upload failed: ${error}`)
          }
        }
      }
    }

    return NextResponse.json({
      success: true,
      images: uploadedImages,
      errors: errors.length > 0 ? errors : undefined
    })

  } catch (error) {
    console.error("File upload processing error:", error)
    return NextResponse.json(
      { message: "Failed to process file uploads" },
      { status: 500 }
    )
  }
}

async function handleUrlSubmissions(request: NextRequest) {
  try {
    const data = await request.json()
    return handleUrlSubmissionsWithData(data)
  } catch (error) {
    console.error("URL submission processing error:", error)
    return NextResponse.json(
      { message: "Failed to process image URLs" },
      { status: 500 }
    )
  }
}

async function handleUrlSubmissionsWithData(data: any) {
  try {
    const { urls, dealId } = data

    if (!dealId) {
      return NextResponse.json(
        { message: "Deal ID is required" },
        { status: 400 }
      )
    }

    if (!Array.isArray(urls)) {
      return NextResponse.json(
        { message: "URLs must be an array" },
        { status: 400 }
      )
    }

    const result = await processImageUrls(urls)

    return NextResponse.json({
      success: true,
      validUrls: result.validUrls,
      errors: result.errors.length > 0 ? result.errors : undefined
    })

  } catch (error) {
    console.error("URL submission processing error:", error)
    return NextResponse.json(
      { message: "Failed to process image URLs" },
      { status: 500 }
    )
  }
}

async function handleExternalImageLocalization(imageUrl: string, dealId: string) {
  try {
    if (!imageUrl) {
      return NextResponse.json(
        { message: "Image URL is required" },
        { status: 400 }
      )
    }

    if (!dealId) {
      return NextResponse.json(
        { message: "Deal ID is required" },
        { status: 400 }
      )
    }

    // Fetch the external image
    const response = await fetch(imageUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Fetch-Dest': 'image',
        'Sec-Fetch-Mode': 'no-cors',
        'Sec-Fetch-Site': 'cross-site',
        'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"'
      }
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`)
    }

    const contentType = response.headers.get('content-type')
    if (!contentType?.startsWith('image/')) {
      throw new Error('URL does not point to a valid image')
    }

    // Convert response to buffer
    const arrayBuffer = await response.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    // Create a mock file object to reuse existing saveImageFile function
    const mockFile = {
      arrayBuffer: async () => arrayBuffer,
      size: buffer.length
    } as File

    // Use existing save function
    const uploadResult = await saveImageFile(mockFile, dealId, 'main')
    
    if (uploadResult.success && uploadResult.url) {
      return NextResponse.json({
        success: true,
        images: [{
          url: uploadResult.url,
          thumbnailUrl: uploadResult.thumbnailUrl || uploadResult.url,
          width: 800,
          height: 600,
          size: buffer.length,
          type: 'main'
        }]
      })
    } else {
      throw new Error(uploadResult.error || "Failed to save localized image")
    }

  } catch (error: unknown) {
    console.error("External image localization error:", error)
    const errorMessage = error instanceof Error ? error.message : "Failed to localize external image"
    return NextResponse.json(
      { message: errorMessage },
      { status: 500 }
    )
  }
}

import sharp from "sharp"

// Configure Sharp for better memory management
sharp.cache({ memory: 50 }); // Limit cache to 50MB
sharp.concurrency(1); // Process one image at a time to reduce memory usage

async function saveImageFile(
  file: File, 
  dealId: string, 
  type: 'main' | 'gallery'
): Promise<{ success: boolean; url?: string; thumbnailUrl?: string; error?: string }> {
  try {
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Create directory structure
    const uploadDir = join(process.cwd(), "public", "uploads", "deals")
    const thumbnailDir = join(process.cwd(), "public", "uploads", "deals", "thumbnails")
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
    }
    if (!existsSync(thumbnailDir)) {
      await mkdir(thumbnailDir, { recursive: true })
    }

    // Generate unique filename (always .webp)
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 8)
    const filename = `${dealId}_${type}_${timestamp}_${random}.webp`
    const filepath = join(uploadDir, filename)
    const thumbnailFilename = `${dealId}_${type}_${timestamp}_${random}.webp`
    const thumbnailPath = join(thumbnailDir, thumbnailFilename)

    // Save main image as .webp (800x800 max) with memory management
    const mainProcessor = sharp(buffer, { limitInputPixels: 268402689 }) // ~16k x 16k max
      .resize(800, 800, { fit: "inside", withoutEnlargement: true })
      .webp({ quality: 80, effort: 1 }) // Lower effort for faster processing

    await mainProcessor.toFile(filepath)
    mainProcessor.destroy() // Clean up resources

    // Save thumbnail as .webp (200x200 max) with memory management
    const thumbProcessor = sharp(buffer, { limitInputPixels: 268402689 })
      .resize(200, 200, { fit: "inside", withoutEnlargement: true })
      .webp({ quality: 70, effort: 1 })

    await thumbProcessor.toFile(thumbnailPath)
    thumbProcessor.destroy() // Clean up resources

    // Generate URLs
    const url = `/uploads/deals/${filename}`
    const thumbnailUrl = `/uploads/deals/thumbnails/${thumbnailFilename}`

    return {
      success: true,
      url,
      thumbnailUrl
    }

  } catch (error) {
    console.error("File save error:", error)
    return {
      success: false,
      error: "Failed to save file"
    }
  }
}
