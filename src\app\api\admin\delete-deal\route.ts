import { NextRequest, NextResponse } from "next/server"
import { requireAdmin } from "@/lib/auth"
import { db } from "@/lib/db"
import { deleteImageFiles } from "@/lib/admin"

export async function DELETE(request: NextRequest) {
  try {
    await requireAdmin()

    const { dealId } = await request.json()

    if (!dealId) {
      return NextResponse.json(
        { error: "Deal ID is required" },
        { status: 400 }
      )
    }

    // Convert string ID to number
    const numericDealId = parseInt(dealId, 10)
    if (isNaN(numericDealId)) {
      return NextResponse.json(
        { error: "Invalid deal ID" },
        { status: 400 }
      )
    }

    // Delete image files first
    await deleteImageFiles(numericDealId)

    // Delete deal and all related data (cascading deletes will handle related records)
    await db.deal.delete({
      where: {
        id: numericDealId
      }
    })

    return NextResponse.json({
      success: true,
      message: "Deal and all related data deleted successfully"
    })

  } catch (error) {
    console.error("Error deleting deal:", error)
    return NextResponse.json(
      { error: "Failed to delete deal" },
      { status: 500 }
    )
  }
} 