import { NextResponse } from 'next/server';
import { requireAdmin } from '@/lib/auth';
import { db } from '@/lib/db';

export async function POST(request: Request) {
  try {
    await requireAdmin();
    
    const { sql, params = [] } = await request.json();
    
    if (!sql) {
      return NextResponse.json(
        { error: 'SQL query is required' },
        { status: 400 }
      );
    }

    // Execute the SQL query directly
    const result = await db.$executeRawUnsafe(sql, ...params);
    
    return NextResponse.json({ success: true, result });
  } catch (error) {
    console.error('Error executing SQL:', error);
    return NextResponse.json(
      { error: 'Failed to execute SQL', details: String(error) },
      { status: 500 }
    );
  }
}
