import { NextResponse } from "next/server"
import { requireAdmin } from "@/lib/auth"
import { db } from "@/lib/db"

export async function GET() {
  try {
    await requireAdmin()

    const deals = await db.deal.findMany({
      where: {
        status: "PENDING",
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        store: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    const response = NextResponse.json({ deals })
    
    // Prevent caching for admin endpoints to ensure fresh data
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')
    
    return response
  } catch (error) {
    console.error("Error fetching pending deals:", error)
    return NextResponse.json(
      { error: "Failed to fetch pending deals" },
      { status: 500 }
    )
  }
}
