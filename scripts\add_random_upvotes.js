/* eslint-disable */
const { PrismaClient } = require('@prisma/client');

// Load env vars if dotenv is available (dev environment)
try {
  require('dotenv').config();
} catch (e) {
  // Expected in production - environment variables are set directly
}

const prisma = new PrismaClient();

/**
 * Returns a random integer between min and max (inclusive)
 */
function randInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

async function main() {
  console.log('📈 Adding random up-votes to ACTIVE deals created within the last 48 hours…');

  // Fetch all user IDs once – we'll randomly pick from this pool.
  const users = await prisma.user.findMany({ select: { id: true } });
  const userIds = users.map((u) => u.id);

  if (userIds.length === 0) {
    console.error('❌ No users found in database – cannot create votes.');
    process.exit(1);
  }

  // Calculate the date 48 hours ago
  const fortyEightHoursAgo = new Date();
  fortyEightHoursAgo.setHours(fortyEightHoursAgo.getHours() - 48);

  const deals = await prisma.deal.findMany({
    where: {
      status: 'ACTIVE',
      createdAt: {
        gte: fortyEightHoursAgo,
      },
    },
    select: {
      id: true,
      upvotes: true,
      downvotes: true,
      niceAt: true,
      createdAt: true,
    },
  });

  const threshold = Number(process.env.NICE_THRESHOLD) || 10;

  console.log(`🔍 Found ${deals.length} ACTIVE deals created within the last 48 hours`);

  if (deals.length === 0) {
    console.log('ℹ️  No deals match the criteria. Exiting.');
    return;
  }

  for (const deal of deals) {
    const votesToAdd = randInt(1, 4); // 1–4 inclusive
    let added = 0;
    let attempts = 0;

    while (added < votesToAdd && attempts < userIds.length * 2) {
      attempts += 1;
      const randomUserId = userIds[randInt(0, userIds.length - 1)];

      try {
        // Create the vote (unique constraint on dealId+userId ensures no dupes)
        await prisma.vote.create({
          data: {
            type: 'UPVOTE',
            dealId: deal.id,
            userId: randomUserId,
          },
        });
        added += 1;
      } catch (err) {
        // Duplicate vote for that user/deal – pick another user and retry.
        if (err.code !== 'P2002') {
          console.error(`Unexpected error creating vote for deal ${deal.id}:`, err);
        }
      }
    }

    if (added === 0) continue; // Nothing added to this deal

    // Increment cached counters on the deal
    const updated = await prisma.deal.update({
      where: { id: deal.id },
      data: {
        upvotes: { increment: added },
      },
      select: {
        upvotes: true,
        downvotes: true,
        niceAt: true,
      },
    });

    const score = updated.upvotes - updated.downvotes;
    if (score >= threshold && !updated.niceAt) {
      await prisma.deal.update({
        where: { id: deal.id },
        data: { niceAt: new Date() },
      });
    }

    console.log(`✅ Deal ${deal.id}: +${added} up-votes (score = ${score})`);
  }

  console.log('🎉 Done – random up-votes added to qualifying deals.');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 