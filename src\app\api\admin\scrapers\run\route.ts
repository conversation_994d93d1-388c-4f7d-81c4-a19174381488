import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { HotUKDealsScraper } from "@/lib/scrapers/HotUKDealsScraper";

export async function POST(req: NextRequest) {
  try {
    // Check authentication and admin role
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await db.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { source } = await req.json();

    if (!source) {
      return NextResponse.json(
        { success: false, error: "Source is required" }, 
        { status: 400 }
      );
    }

    let result;

    switch (source) {
      case 'hukd':
      case 'hotukdeals':
        console.log('[SCRAPER API] Starting HotUKDeals scraper...');
        const scraper = new HotUKDealsScraper(db);
        result = await scraper.run();
        break;
      default:
        return NextResponse.json({
          success: false,
          error: `Unknown scraper source: ${source}`
        }, { status: 400 });
    }

    console.log('[SCRAPER API] Scraper completed:', result);

    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('[SCRAPER API] Error:', error);
    return NextResponse.json({
      success: false,
      error: "Failed to run scraper",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
} 