import { NextRequest, NextResponse } from "next/server"
import { requireAdmin } from "@/lib/auth"
import { db } from "@/lib/db"
import { deleteImageFiles } from "@/lib/admin"

export async function DELETE(request: NextRequest) {
  try {
    await requireAdmin()

    const { dealIds } = await request.json()

    if (!dealIds || !Array.isArray(dealIds) || dealIds.length === 0) {
      return NextResponse.json(
        { error: "Deal IDs array is required" },
        { status: 400 }
      )
    }

    // Convert string IDs to numbers
    const numericDealIds = dealIds.map(id => {
      const num = parseInt(id, 10)
      if (isNaN(num)) {
        throw new Error(`Invalid deal ID: ${id}`)
      }
      return num
    })

    // Delete image files first
    await deleteImageFiles(numericDealIds)

    // Delete deals and all related data (cascading deletes will handle related records)
    const deletedDeals = await db.deal.deleteMany({
      where: {
        id: {
          in: numericDealIds
        }
      }
    })

    return NextResponse.json({
      success: true,
      deletedCount: deletedDeals.count,
      message: `Successfully deleted ${deletedDeals.count} deals and all related data`
    })

  } catch (error) {
    console.error("Error bulk deleting deals:", error)
    return NextResponse.json(
      { error: "Failed to delete deals" },
      { status: 500 }
    )
  }
} 