import { NextResponse } from "next/server"
import { db } from "@/lib/db"

export async function GET() {
  try {
    // Fetch categories and stores from database
    const [categories, stores] = await Promise.all([
      db.category.findMany({
        where: { active: true },
        select: {
          id: true,
          name: true,
          slug: true,
        },
        orderBy: { name: 'asc' }
      }),
      db.store.findMany({
        where: { active: true },
        select: {
          id: true,
          name: true,
          slug: true,
        },
        orderBy: { name: 'asc' }
      })
    ])

    return NextResponse.json({
      categories,
      stores
    })

  } catch (error) {
    console.error("Form data fetch error:", error)
    return NextResponse.json(
      { message: "Failed to fetch form data" },
      { status: 500 }
    )
  }
}
