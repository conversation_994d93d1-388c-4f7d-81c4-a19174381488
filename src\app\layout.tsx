import type { Metada<PERSON> } from "next";
import { Manrope, Inter } from "next/font/google";
import Footer from "@/components/Footer";
import Header from "@/components/Header";
import SessionProvider from "@/components/SessionProvider";
import ToasterProvider from "@/components/ToasterProvider";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter"
});

const manrope = Manrope({
  subsets: ["latin"],
  weight: "400",
  variable: "--font-manrope",
});

export const metadata: Metadata = {
  title: "NiceDeals",
  description: "Community-driven deals platform",
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3010"),
  openGraph: {
    type: "website",
    siteName: "NiceDeals",
    title: "NiceDeals - Find and share the best UK bargains",
    description: "Community-powered UK deals, discounts and voucher codes.",
    images: [
      {
        url: "/nicedeals-logo.png",
        width: 1200,
        height: 630,
        alt: "NiceDeals logo"
      }
    ]
  },
  twitter: {
    card: "summary",
    title: "NiceDeals - UK's community bargain finder",
    description: "Discover and share the hottest UK deals, discounts and voucher codes.",
    images: ["/nicedeals-logo.png"]
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.variable} ${manrope.variable} antialiased`}
      >
        <SessionProvider>
          <div className="flex flex-col min-h-screen">
            <Header />
            <main className="flex-grow">{children}</main>
            <Footer />
          </div>
          <ToasterProvider />
        </SessionProvider>
      </body>
    </html>
  );
}
