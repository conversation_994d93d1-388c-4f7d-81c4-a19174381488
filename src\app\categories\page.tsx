import Link from "next/link";
import { 
  Smartphone, 
  Laptop, 
  Gamepad2, 
  Home, 
  Shirt, 
  Heart, 
  Baby, 
  Book, 
  Coffee, 
  Plane, 
  <PERSON><PERSON><PERSON>, 
  Car, 
  Wrench, 
  Sparkles, 
  Hammer, 
  FileText, 
  ShoppingCart, 
  Palette, 
  Phone, 
  TreePine, 
  Users, 
  Gift, 
  Settings,
  MoreHorizontal 
} from "lucide-react";
import { db } from "@/lib/db";
import { LucideIcon } from "lucide-react";

// Use ISR for production build performance
// NOTE: Next.js only allows literal values - no env vars, imports, or functions
export const revalidate = 60

// Icon mapping for categories
const categoryIcons: Record<string, LucideIcon> = {
  "electronics": Smartphone,
  "computers": Laptop,
  "gaming": Gamepad2,
  "home-garden": Home,
  "clothing-accessories": Shirt,
  "beauty-health": Heart,
  "toys-kids": Baby,
  "books-media": Book,
  "food-drink": Coffee,
  "travel": Plane,
  "sports-outdoors": Dumbbell,
  "automotive": Car,
  "services": Wrench,
  "entertainment": Sparkles,
  "home-improvement": Hammer,
  "office-supplies": FileText,
  "groceries": ShoppingCart,
  "culture-leisure": Palette,
  "broadband-phone": Phone,
  "broadband-phone-contracts": Phone,
  "garden-do-it-yourself": TreePine,
  "family-kids": Users,
  "freebies": Gift,
  "services-contracts": Settings,
  "other": MoreHorizontal,
  "health-beauty": Heart,
  "home-living": Home,
  "fashion-accessories": Shirt,
};

// Featured categories (these will be highlighted)
const featuredSlugs = [
  "electronics", "computers", "gaming", "home-garden", "clothing-accessories", "beauty-health"
];

async function getCategoriesWithCounts() {
  try {
    const categories = await db.category.findMany({
      where: {
        active: true,
      },
      include: {
        deals: {
          where: {
            status: {
              in: ['APPROVED', 'ACTIVE'],
            },
          },
        },
      },
      orderBy: [
        { sortOrder: 'asc' },
        { name: 'asc' },
      ],
    });

    return categories.map((cat) => ({
      id: cat.id,
      name: cat.name,
      slug: cat.slug,
      description: cat.description || getDefaultDescription(cat.slug),
      icon: categoryIcons[cat.slug] || MoreHorizontal,
      dealCount: cat.deals.length || 0,
      featured: featuredSlugs.includes(cat.slug),
    }));
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

function getDefaultDescription(slug: string): string {
  const descriptions: Record<string, string> = {
    "electronics": "Smartphones, tablets, headphones, and gadgets",
    "computers": "Laptops, desktops, accessories, and software",
    "gaming": "Consoles, games, accessories, and peripherals",
    "home-garden": "Furniture, appliances, tools, and decor",
    "clothing-accessories": "Fashion, shoes, bags, and jewelry",
    "beauty-health": "Cosmetics, skincare, wellness, and fitness",
    "toys-kids": "Toys, games, baby items, and children's clothing",
    "books-media": "Books, movies, music, and digital content",
    "food-drink": "Groceries, restaurants, beverages, and snacks",
    "travel": "Flights, hotels, car rentals, and experiences",
    "sports-outdoors": "Fitness equipment, outdoor gear, and sportswear",
    "automotive": "Car parts, accessories, and maintenance",
    "services": "Professional services, subscriptions, and software",
    "entertainment": "Streaming, events, concerts, and shows",
    "home-improvement": "Tools, materials, and DIY supplies",
    "office-supplies": "Stationery, equipment, and workspace essentials",
    "groceries": "Food, household items, and everyday essentials",
    "culture-leisure": "Art, hobbies, crafts, and cultural activities",
    "broadband-phone-contracts": "Internet, mobile plans, and telecom services",
    "garden-do-it-yourself": "Gardening supplies, plants, and DIY projects",
    "family-kids": "Family activities, childcare, and parenting",
    "freebies": "Free samples, trials, and promotional offers",
    "services-contracts": "Utilities, insurance, and professional services",
    "health-beauty": "Health products, beauty items, and wellness",
    "home-living": "Home decor, furniture, and living essentials",
    "fashion-accessories": "Clothing, accessories, and fashion items",
  };
  return descriptions[slug] || "Great deals and discounts";
}

export const metadata = {
  title: "Browse Categories | NiceDeals",
  description: "Explore deals by category. Find the best discounts on electronics, fashion, home goods, and more across all your favorite stores.",
};

export default async function CategoriesPage() {
  const categories = await getCategoriesWithCounts();
  const featuredCategories = categories.filter(cat => cat.featured);
  const otherCategories = categories.filter(cat => !cat.featured);
  const totalDeals = categories.reduce((sum, cat) => sum + cat.dealCount, 0);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-serif font-bold text-gray-900 mb-4">
            Browse by Category
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-6">
            Discover amazing deals organized by category. From electronics to fashion, 
            home goods to travel - find exactly what you&rsquo;re looking for.
          </p>
          <div className="bg-white inline-block px-6 py-3 rounded-lg shadow-sm border border-gray-200">
            <span className="text-2xl font-bold text-orange-600">{totalDeals.toLocaleString()}</span>
            <span className="text-gray-600 ml-2">active deals across all categories</span>
          </div>
        </div>

        {/* Featured Categories */}
        {featuredCategories.length > 0 && (
        <section className="mb-16">
          <h2 className="text-2xl font-serif font-bold text-gray-900 mb-8 text-center">
            Popular Categories
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredCategories.map((category) => {
              const IconComponent = category.icon;
              return (
                <Link
                  key={category.slug}
                  href={`/categories/${category.slug}`}
                  className="group"
                >
                  <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                    <div className="flex items-center justify-between mb-4">
                      <div className="p-3 bg-orange-100 rounded-lg group-hover:bg-orange-200 transition-colors">
                        <IconComponent className="w-8 h-8 text-orange-600" />
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-gray-900">
                          {category.dealCount}
                        </div>
                        <div className="text-sm text-gray-500">deals</div>
                      </div>
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-orange-600 transition-colors">
                      {category.name}
                    </h3>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {category.description}
                    </p>
                  </div>
                </Link>
              );
            })}
          </div>
        </section>
        )}

        {/* All Categories */}
        {otherCategories.length > 0 && (
        <section>
          <h2 className="text-2xl font-serif font-bold text-gray-900 mb-8 text-center">
            All Categories
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {otherCategories.map((category) => {
              const IconComponent = category.icon;
              return (
                <Link
                  key={category.slug}
                  href={`/categories/${category.slug}`}
                  className="group"
                >
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200">
                    <div className="flex items-center gap-4">
                      <div className="p-2 bg-gray-100 rounded-lg group-hover:bg-orange-100 transition-colors">
                        <IconComponent className="w-6 h-6 text-gray-600 group-hover:text-orange-600 transition-colors" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-gray-900 group-hover:text-orange-600 transition-colors truncate">
                          {category.name}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {category.dealCount} deals
                        </p>
                      </div>
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        </section>
        )}

        {/* No deals message */}
        {totalDeals === 0 && (
          <section className="text-center py-16">
            <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 max-w-2xl mx-auto">
              <h2 className="text-2xl font-serif font-bold text-gray-900 mb-4">
                No Active Deals Yet
              </h2>
              <p className="text-gray-600 mb-6">
                We&rsquo;re working hard to bring you amazing deals across all categories. 
                Check back soon for the latest discounts and offers!
              </p>
              <Link
                href="/post"
                className="bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-700 transition-colors inline-block"
              >
                Submit a Deal
              </Link>
            </div>
          </section>
        )}

        {/* CTA Section */}
        <section className="mt-16 text-center">
          <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl p-8 text-white">
            <h2 className="text-3xl font-serif font-bold mb-4">
              Can&rsquo;t Find What You&rsquo;re Looking For?
            </h2>
            <p className="text-xl text-orange-100 mb-6 max-w-2xl mx-auto">
              Browse all deals or use our search to find specific products and brands.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/browse"
                className="bg-white text-orange-600 px-8 py-3 rounded-lg font-semibold hover:bg-orange-50 transition-colors"
              >
                Browse All Deals
              </Link>
              <Link
                href="/browse"
                className="border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-orange-600 transition-colors"
              >
                Advanced Search
              </Link>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
