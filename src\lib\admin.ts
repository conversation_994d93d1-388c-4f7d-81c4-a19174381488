import { db } from "@/lib/db"
import fs from 'fs'
import path from 'path'

export interface AdminStats {
  totalUsers: number
  totalDeals: number
  activeDeals: number
  pendingDeals: number
  totalComments: number
  totalCategories: number
  totalStores: number
  recentActivity: number
}

export async function getAdminStats(): Promise<AdminStats> {
  const [
    totalUsers,
    totalDeals,
    activeDeals,
    pendingDeals,
    totalComments,
    totalCategories,
    totalStores,
    recentActivity
  ] = await Promise.all([
    db.user.count(),
    db.deal.count(),
    db.deal.count({ where: { status: "ACTIVE" } }),
    db.deal.count({ where: { status: "PENDING" } }),
    db.comment.count(),
    db.category.count(),
    db.store.count(),
    db.deal.count({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        }
      }
    })
  ])

  return {
    totalUsers,
    totalDeals,
    activeDeals,
    pendingDeals,
    totalComments,
    totalCategories,
    totalStores,
    recentActivity
  }
}

/**
 * Consolidated image deletion function that can be used by all delete operations
 */
export async function deleteImageFiles(dealIds: number | number[]) {
  try {
    const dealIdsArray = Array.isArray(dealIds) ? dealIds : [dealIds]
    
    // Get all DealImage records for these deals
    const dealImages = await db.dealImage.findMany({
      where: {
        dealId: {
          in: dealIdsArray
        }
      }
    })

    // Delete DealImage files from filesystem
    for (const image of dealImages) {
      if (image.imageUrl) {
        const imagePath = path.join(process.cwd(), 'public', image.imageUrl)
        if (fs.existsSync(imagePath)) {
          fs.unlinkSync(imagePath)
          console.log(`Deleted image: ${imagePath}`)
        }
      }
      
      if (image.thumbnailUrl) {
        const thumbnailPath = path.join(process.cwd(), 'public', image.thumbnailUrl)
        if (fs.existsSync(thumbnailPath)) {
          fs.unlinkSync(thumbnailPath)
          console.log(`Deleted thumbnail: ${thumbnailPath}`)
        }
      }
    }

    // Get main deal images
    const deals = await db.deal.findMany({
      where: {
        id: {
          in: dealIdsArray
        }
      },
      select: {
        id: true,
        imageUrl: true,
        thumbnailUrl: true
      }
    })

    // Delete main deal images from filesystem
    for (const deal of deals) {
      if (deal.imageUrl) {
        const imagePath = path.join(process.cwd(), 'public', deal.imageUrl)
        if (fs.existsSync(imagePath)) {
          fs.unlinkSync(imagePath)
          console.log(`Deleted main image: ${imagePath}`)
        }
        
        // Also check for thumbnails in the thumbnails directory with the same filename
        const filename = path.basename(deal.imageUrl)
        const thumbnailPath = path.join(process.cwd(), 'public', 'uploads', 'deals', 'thumbnails', filename)
        if (fs.existsSync(thumbnailPath)) {
          fs.unlinkSync(thumbnailPath)
          console.log(`Deleted corresponding thumbnail: ${thumbnailPath}`)
        }
      }
      
      if (deal.thumbnailUrl && deal.thumbnailUrl !== deal.imageUrl) {
        const thumbnailPath = path.join(process.cwd(), 'public', deal.thumbnailUrl)
        if (fs.existsSync(thumbnailPath)) {
          fs.unlinkSync(thumbnailPath)
          console.log(`Deleted main thumbnail: ${thumbnailPath}`)
        }
      }
    }
    
    return {
      success: true,
      deletedImages: dealImages.length,
      deletedDeals: deals.length
    }
  } catch (error) {
    console.error("Error deleting image files:", error)
    // Don't throw - continue with database deletion even if file deletion fails
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Clean up orphaned image files that don't have corresponding database records
 */
export async function cleanupOrphanedImages() {
  try {
    const uploadsPath = path.join(process.cwd(), 'public', 'uploads', 'deals')
    const thumbnailsPath = path.join(uploadsPath, 'thumbnails')
    
    // Get all existing deal IDs from database
    const existingDeals = await db.deal.findMany({
      select: { id: true }
    })
    const existingDealIds = new Set(existingDeals.map(d => d.id))
    
    let deletedFiles = 0
    
    // Check main images
    if (fs.existsSync(uploadsPath)) {
      const mainFiles = fs.readdirSync(uploadsPath).filter(f => f.includes('_main_'))
      for (const file of mainFiles) {
        const dealIdMatch = file.match(/^(\d+)_main_/)
        if (dealIdMatch) {
          const dealId = parseInt(dealIdMatch[1])
          if (!existingDealIds.has(dealId)) {
            const filePath = path.join(uploadsPath, file)
            fs.unlinkSync(filePath)
            deletedFiles++
            console.log(`Deleted orphaned main image: ${file}`)
          }
        }
      }
    }
    
    // Check thumbnails
    if (fs.existsSync(thumbnailsPath)) {
      const thumbFiles = fs.readdirSync(thumbnailsPath)
      for (const file of thumbFiles) {
        const dealIdMatch = file.match(/^(\d+)_main_/)
        if (dealIdMatch) {
          const dealId = parseInt(dealIdMatch[1])
          if (!existingDealIds.has(dealId)) {
            const filePath = path.join(thumbnailsPath, file)
            fs.unlinkSync(filePath)
            deletedFiles++
            console.log(`Deleted orphaned thumbnail: ${file}`)
          }
        }
      }
    }
    
    return {
      success: true,
      deletedFiles,
      message: `Cleaned up ${deletedFiles} orphaned image files`
    }
  } catch (error) {
    console.error("Error cleaning up orphaned images:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
} 