import { NextResponse } from "next/server"
import { requireAdmin } from "@/lib/auth"
import { cleanupOrphanedImages } from "@/lib/admin"

export async function POST() {
  try {
    await requireAdmin()

    const result = await cleanupOrphanedImages()

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message,
        deletedFiles: result.deletedFiles
      })
    } else {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error("Error cleaning up orphaned images:", error)
    return NextResponse.json(
      { error: "Failed to clean up images" },
      { status: 500 }
    )
  }
} 