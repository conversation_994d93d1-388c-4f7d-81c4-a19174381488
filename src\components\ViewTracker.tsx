"use client"

import { useEffect } from "react";

interface ViewTrackerProps {
  dealId: number;
}

export default function ViewTracker({ dealId }: ViewTrackerProps) {
  useEffect(() => {
    // Track the view after component mounts (client-side only)
    const trackView = async () => {
      try {
        // Check if we've already tracked this view in this session
        const sessionKey = `deal_view_${dealId}`;
        const hasViewed = sessionStorage.getItem(sessionKey);
        
        if (hasViewed) {
          // Already viewed in this session, don't track again
          return;
        }

        const response = await fetch(`/api/deals/${dealId}/view`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (response.ok) {
          const result = await response.json();
          if (result.counted) {
            // Mark as viewed in this session
            sessionStorage.setItem(sessionKey, Date.now().toString());
          }
        } else {
          console.warn("Failed to track view:", response.statusText);
        }
      } catch (error) {
        console.warn("Error tracking view:", error);
      }
    };

    // Small delay to ensure the page has loaded
    const timeoutId = setTimeout(trackView, 1000);

    return () => clearTimeout(timeoutId);
  }, [dealId]);

  return null; // This component doesn't render anything
} 