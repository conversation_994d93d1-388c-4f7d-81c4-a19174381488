import { NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"
import { getServerSession } from "@/lib/auth"

// GET /api/deals - Fetch deals with optional filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get("page") || "1")
    const limit = parseInt(searchParams.get("limit") || "12")
    const search = searchParams.get("search") || ""
    const category = searchParams.get("category") || ""
    const store = searchParams.get("store") || ""
    const sortBy = searchParams.get("sort") || "recent"
    
    const skip = (page - 1) * limit

    // Build where clause
    const where: {
      status?: string;
      OR?: Array<{ title?: { contains: string; mode: "insensitive" } } | { description?: { contains: string; mode: "insensitive" } }>;
      category?: { slug?: string };
      store?: { slug?: string };
      niceAt?: { not: null };
    } = {
      status: "ACTIVE",
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ]
    }

    if (category) {
      where.category = { slug: category }
    }

    if (store) {
      where.store = { slug: store }
    }

    // Build orderBy clause
    let orderBy: 
      | { createdAt: "desc" }
      | { upvotes: "desc" }
      | { dealPrice: "asc" | "desc" }
      | { discountPercentage: "desc" }
      | { averageRating: "desc" }
      | { niceAt: "desc" } = { createdAt: "desc" } // default: recent

    switch (sortBy) {
      case "popular":
        orderBy = { upvotes: "desc" }
        break
      case "price-low":
        orderBy = { dealPrice: "asc" }
        break
      case "price-high":
        orderBy = { dealPrice: "desc" }
        break
      case "discount":
        orderBy = { discountPercentage: "desc" }
        break
      case "rating":
        orderBy = { averageRating: "desc" }
        break
      case "nice":
        where.niceAt = { not: null }
        orderBy = { niceAt: "desc" }
        break
    }

    // Fetch deals
    const [deals, total] = await Promise.all([
      db.deal.findMany({
        where,
        orderBy,
        skip,
        take: limit,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          store: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          _count: {
            select: {
              votes: true,
              comments: true,
              savedDeals: true,
            },
          },
        },
      }),
      db.deal.count({ where }),
    ])

    return NextResponse.json({
      deals,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error("Error fetching deals:", error)
    return NextResponse.json(
      { error: "Failed to fetch deals" },
      { status: 500 }
    )
  }
}

// POST /api/deals - Create a new deal (authenticated users only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      title,
      description,
      originalPrice,
      dealPrice,
      store,
      category,
      dealUrl,
      couponCode,
      expiresAt,
    } = body

    // Validate required fields
    if (!title || !description || !dealPrice || !store || !category || !dealUrl) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      )
    }

    // Calculate discount percentage
    let discountPercentage = null
    if (originalPrice && originalPrice > dealPrice) {
      discountPercentage = Math.round(((originalPrice - dealPrice) / originalPrice) * 100)
    }

    // Generate slug for the deal
    const slug = title.toLowerCase().replace(/[^a-z0-9]/g, "-")

    // Find or create store
    let storeRecord = await db.store.findFirst({
      where: { 
        name: store
      }
    })
    
    if (!storeRecord) {
      const storeSlug = store.toLowerCase().replace(/[^a-z0-9]/g, "-")
      storeRecord = await db.store.create({
        data: {
          name: store,
          slug: storeSlug,
          url: dealUrl, // Use the deal URL as the store URL initially
          active: true
        }
      })
    }

    // Find or create category
    let categoryRecord = await db.category.findFirst({
      where: { 
        name: category
      }
    })
    
    if (!categoryRecord) {
      const categorySlug = category.toLowerCase().replace(/[^a-z0-9]/g, "-")
      categoryRecord = await db.category.create({
        data: {
          name: category,
          slug: categorySlug,
          active: true
        }
      })
    }

    // Create deal
    const deal = await db.deal.create({
      data: {
        title,
        slug,
        description,
        originalPrice: originalPrice ? parseFloat(originalPrice) : null,
        dealPrice: parseFloat(dealPrice),
        discountPercentage,
        storeId: storeRecord.id,
        categoryId: categoryRecord.id,
        dealUrl,
        couponCode: couponCode || null,
        expiresAt: expiresAt ? new Date(expiresAt) : null,
        status: "PENDING", // All new deals start as pending
        authorId: parseInt(session.user.id, 10),
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    })

    return NextResponse.json(deal, { status: 201 })
  } catch (error) {
    console.error("Error creating deal:", error)
    return NextResponse.json(
      { error: "Failed to create deal" },
      { status: 500 }
    )
  }
}
