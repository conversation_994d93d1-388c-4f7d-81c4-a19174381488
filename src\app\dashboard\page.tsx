import { redirect } from "next/navigation"
import { requireAuth } from "@/lib/auth"
import { db } from "@/lib/db"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Heart, Package, MessageSquare } from "lucide-react"
import DashboardTabs from "./DashboardTabs"
import { Decimal } from "@prisma/client/runtime/library"

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'

// Transform database deal to DealCard format
function transformDealForCard(deal: {
  id: number;
  slug: string;
  title: string;
  description: string;
  originalPrice?: Decimal | number | null;
  dealPrice: Decimal | number;
  discountPercentage?: number | null;
  store?: { id: number; name: string; slug: string };
  category?: { id: number; name: string; slug: string };
  dealUrl: string;
  imageUrl?: string | null;
  thumbnailUrl?: string | null;
  upvotes: number;
  downvotes: number;
  averageRating?: number | null;
  ratingCount: number;
  viewCount: number;
  couponCode?: string | null;
  expiresAt?: Date | null;
  createdAt: Date;
  updatedAt: Date;
  status: string;

  author?: { id: number; name: string | null; image: string | null };
  authorId: number;
  _count?: { votes: number; comments: number; savedDeals: number };
}) {
  const originalPrice = deal.originalPrice ? Number(deal.originalPrice) : null;
  const dealPrice = Number(deal.dealPrice);
  
  // Calculate discount percentage only if we have valid prices
  let discountPercentage = 0;
  if (originalPrice && originalPrice > 0 && originalPrice > dealPrice) {
    // Use provided discount percentage or calculate it
    discountPercentage = deal.discountPercentage ? Number(deal.discountPercentage) : Math.round(((originalPrice - dealPrice) / originalPrice) * 100);
  }

  // Calculate isNew server-side to avoid hydration mismatch
  const isNew = (() => {
    if (!deal.createdAt) return false;
    const createdAt = new Date(deal.createdAt);
    const now = new Date();
    const diffInHours = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
    return diffInHours <= 24;
  })();
  
  return {
    id: deal.id,
    slug: deal.slug,
    title: deal.title,
    description: deal.description,
    originalPrice: originalPrice, // Keep as null if no original price
    dealPrice: dealPrice,
    discountPercentage: discountPercentage,
    discount: discountPercentage, // Will be 0 if no valid original price
    store: deal.store,
    storeSlug: deal.store?.slug || "",
    category: deal.category,
    categorySlug: deal.category?.slug || "",
    dealUrl: deal.dealUrl,
    imageUrl: deal.imageUrl || "",
    thumbnailUrl: deal.thumbnailUrl || "",
    upvotes: deal.upvotes || 0,
    downvotes: deal.downvotes || 0,
    averageRating: deal.averageRating ? Number(deal.averageRating) : 0,
    rating: deal.averageRating ? Number(deal.averageRating) : 0,
    ratingCount: deal.ratingCount || 0,
    viewCount: deal.viewCount || 0,
    couponCode: deal.couponCode || undefined,
    expiresAt: deal.expiresAt ? deal.expiresAt.toISOString() : undefined,
    createdDate: deal.createdAt ? new Date(deal.createdAt).toISOString() : new Date().toISOString(),
    createdAt: deal.createdAt ? new Date(deal.createdAt).toISOString() : new Date().toISOString(),
    updatedAt: deal.updatedAt ? new Date(deal.updatedAt).toISOString() : new Date().toISOString(),
    status: deal.status,
    author: deal.author,
    authorId: deal.authorId,
    _count: deal._count,
    isNew: isNew,
  }
}

async function getUserData(userId: string) {
  const userIdInt = parseInt(userId, 10)
  if (isNaN(userIdInt)) {
    throw new Error("Invalid user ID")
  }

  const [user, savedDeals, userDeals, userComments, notifications, userVotes] = await Promise.all([
    db.user.findUnique({
      where: { id: userIdInt },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        bio: true,
        location: true,
        reputation: true,
        role: true,
        createdAt: true,
        _count: {
          select: {
            deals: true,
            votes: true,
            comments: true,
            savedDeals: true,
          },
        },
      },
    }),
    db.savedDeal.findMany({
      where: { userId: userIdInt },
      include: {
        deal: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
            store: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            category: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            _count: {
              select: {
                votes: true,
                comments: true,
                savedDeals: true,
              },
            },
          },
        },
      },
      orderBy: { createdAt: "desc" },
      take: 10,
    }),
    db.deal.findMany({
      where: { authorId: userIdInt },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        store: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            votes: true,
            comments: true,
            savedDeals: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
      take: 10,
    }),
    db.comment.findMany({
      where: { userId: userIdInt },
      include: {
        deal: {
          select: {
            id: true,
            title: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
      take: 10,
    }),
    db.notification.findMany({
      where: { userId: userIdInt },
      orderBy: { createdAt: "desc" },
    }),
    db.vote.findMany({
      where: { userId: userIdInt },
      include: {
        deal: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
            store: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            category: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            _count: {
              select: {
                votes: true,
                comments: true,
                savedDeals: true,
              },
            },
          },
        },
      },
      orderBy: { createdAt: "desc" },
    }),
  ])

  if (!user) {
    throw new Error("User not found")
  }

  return {
    user: {
      id: user.id.toString(), // Convert to string for DashboardTabs
      name: user.name || undefined,
      email: user.email || "",
      role: user.role || "USER",
      newsletter: false, // Default value since newsletter field doesn't exist in this context
      _count: user._count,
    },
    savedDeals: savedDeals.map(savedDeal => ({
      ...savedDeal,
      deal: transformDealForCard(savedDeal.deal)
    })),
    userDeals: userDeals.map(deal => transformDealForCard(deal)),
    userComments,
    notifications,
    userVotes: userVotes.map(vote => ({
      ...vote,
      deal: transformDealForCard(vote.deal)
    })),
  }
}

export default async function Dashboard() {
  let session
  try {
    session = await requireAuth()
  } catch {
    redirect("/auth/signin")
  }

  const data = await getUserData(session.user.id)

  if (!data.user) {
    redirect("/auth/signin")
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Welcome back, {data.user.name || data.user.email}!
        </h1>
        <p className="text-gray-600">
          Manage your deals, comments, and preferences from your dashboard.
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Your Deals</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.user._count?.deals || 0}</div>
            <p className="text-xs text-muted-foreground">
              Deals you&apos;ve posted
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">My Saved Deals</CardTitle>
            <Heart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.user._count?.savedDeals || 0}</div>
            <p className="text-xs text-muted-foreground">
              Deals you&apos;ve saved
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Comments</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.user._count?.comments || 0}</div>
            <p className="text-xs text-muted-foreground">
              Comments you&apos;ve made
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Dashboard Tabs */}
      <DashboardTabs data={{
        user: data.user,
        savedDeals: data.savedDeals,
        userDeals: data.userDeals,
        userComments: data.userComments,
        notifications: data.notifications,
        userVotes: data.userVotes.map(vote => ({
          ...vote,
          type: vote.type as "UPVOTE" | "DOWNVOTE"
        }))
      }} />
    </div>
  )
}
