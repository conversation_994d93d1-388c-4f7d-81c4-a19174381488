"use client"

import { useState } from "react";
import { Copy } from "lucide-react";

export default function CouponCodeCopy({ code }: { code: string }) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 1500);
    } catch {
      setCopied(false);
    }
  };

  return (
    <button
      className={`bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center relative`}
      onClick={handleCopy}
      type="button"
    >
      <Copy className="w-4 h-4 mr-2" />
      {copied ? "Copied!" : "Copy"}
    </button>
  );
}
