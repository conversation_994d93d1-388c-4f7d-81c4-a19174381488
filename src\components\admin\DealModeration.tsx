"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Star, Eye, MessageSquare, Heart, ExternalLink, Trash2, Sparkles, CheckSquare, Square, Share2 } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { getSafeImageUrl } from "@/lib/utils"
import ShareModal from "@/components/ShareModal"
import SetOriginalPrice from "./SetOriginalPrice"
import { generateDealSlugWithId } from "@/lib/slug"
import { generateShareText } from "@/lib/utils";

interface Deal {
  id: number
  title: string
  description: string
  originalPrice?: number | string | null
  dealPrice: number | string
  discountPercentage?: number
  store: {
    id: number
    name: string
    slug: string
  }
  category: {
    id: number
    name: string
    slug: string
  }
  dealUrl: string
  imageUrl?: string
  featured: boolean
  upvotes: number
  downvotes: number
  averageRating: number | string
  viewCount: number
  createdAt: string
  author: {
    id: number
    name: string
    image?: string
  }
  _count: {
    votes: number
    comments: number
    savedDeals: number
  }
}

export default function DealModeration() {
  const router = useRouter()
  const [deals, setDeals] = useState<Deal[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [actionLoading, setActionLoading] = useState<number | null>(null)
  const [selectedDeals, setSelectedDeals] = useState<Set<number>>(new Set())
  const [bulkDeleteLoading, setBulkDeleteLoading] = useState(false)

  // Helper function to safely convert price to number
  const safeNumber = (value: number | string | null | undefined): number => {
    if (typeof value === 'number') return value
    if (typeof value === 'string') return parseFloat(value) || 0
    return 0
  }

  // Helper function to calculate discount percentage
  const calculateDiscountPercentage = (dealPrice: number, originalPrice: number): number => {
    if (originalPrice === 0) return 0; // Avoid division by zero
    return ((originalPrice - dealPrice) / originalPrice) * 100;
  };

  useEffect(() => {
    fetchDeals()
  }, [])

  async function fetchDeals() {
    try {
      const response = await fetch("/api/admin/deals")
      if (response.ok) {
        const data = await response.json()
        setDeals(data.deals)
      }
    } catch (error) {
      console.error("Error fetching deals:", error)
    } finally {
      setLoading(false)
    }
  }

  async function handleFeatureToggle(dealId: number, featured: boolean) {
    setActionLoading(dealId)
    try {
      const response = await fetch("/api/admin/feature-deal", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          dealId,
          featured: !featured,
        }),
      })

      if (response.ok) {
        setDeals(deals.map(deal => 
          deal.id === dealId 
            ? { ...deal, featured: !featured }
            : deal
        ))
      }
    } catch (error) {
      console.error("Error toggling feature status:", error)
    } finally {
      setActionLoading(null)
    }
  }

  async function handleDeleteDeal(dealId: number) {
    if (!confirm("Are you sure you want to delete this deal?")) return

    setActionLoading(dealId)
    try {
      const response = await fetch("/api/admin/delete-deal", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ dealId }),
      })

      if (response.ok) {
        setDeals(deals.filter(deal => deal.id !== dealId))
      }
    } catch (error) {
      console.error("Error deleting deal:", error)
    } finally {
      setActionLoading(null)
    }
  }

  async function handleBulkDelete() {
    if (selectedDeals.size === 0) {
      alert("Please select deals to delete")
      return
    }

    if (!confirm(`Are you sure you want to delete ${selectedDeals.size} selected deals? This will also delete all related comments, votes, images, and saved deals. This action cannot be undone.`)) {
      return
    }

    setBulkDeleteLoading(true)
    try {
      const response = await fetch("/api/admin/bulk-delete-deals", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ dealIds: Array.from(selectedDeals) }),
      })

      if (response.ok) {
        const result = await response.json()
        setDeals(deals.filter(deal => !selectedDeals.has(deal.id)))
        setSelectedDeals(new Set())
        alert(`Successfully deleted ${result.deletedCount} deals`)
      } else {
        const error = await response.json()
        alert(`Error: ${error.error}`)
      }
    } catch (error) {
      console.error("Error bulk deleting deals:", error)
      alert("Error deleting deals")
    } finally {
      setBulkDeleteLoading(false)
    }
  }

  function handleSelectDeal(dealId: number, checked: boolean) {
    const newSelectedDeals = new Set(selectedDeals)
    if (checked) {
      newSelectedDeals.add(dealId)
    } else {
      newSelectedDeals.delete(dealId)
    }
    setSelectedDeals(newSelectedDeals)
  }

  function handleSelectAll() {
    if (selectedDeals.size === filteredDeals.length) {
      // Deselect all
      setSelectedDeals(new Set())
    } else {
      // Select all filtered deals
      setSelectedDeals(new Set(filteredDeals.map(deal => deal.id)))
    }
  }

  const filteredDeals = deals.filter(deal =>
    deal.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    deal.store.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    deal.category.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Search and Bulk Actions */}
      <div className="flex gap-4 items-center">
        <Input
          placeholder="Search deals..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="flex-1"
        />
        <Button
          variant="outline"
          onClick={handleSelectAll}
          disabled={filteredDeals.length === 0}
        >
          {selectedDeals.size === filteredDeals.length && filteredDeals.length > 0 ? (
            <>
              <Square className="w-4 h-4 mr-2" />
              Deselect All
            </>
          ) : (
            <>
              <CheckSquare className="w-4 h-4 mr-2" />
              Select All
            </>
          )}
        </Button>
        {selectedDeals.size > 0 && (
          <Button
            variant="destructive"
            onClick={handleBulkDelete}
            disabled={bulkDeleteLoading}
            className="min-w-[160px]"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            {bulkDeleteLoading ? "Deleting..." : `Delete Selected (${selectedDeals.size})`}
          </Button>
        )}
      </div>

      {/* Deals List */}
      <div className="space-y-4">
        {filteredDeals.map((deal) => (
          <Card key={deal.id} className="overflow-hidden">
            <CardContent className="p-6">
              <div className="flex gap-4">
                {/* Checkbox */}
                <div className="flex-shrink-0 flex items-start pt-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSelectDeal(deal.id, !selectedDeals.has(deal.id))}
                    className="h-8 w-8 p-0"
                  >
                    {selectedDeals.has(deal.id) ? (
                      <CheckSquare className="w-5 h-5 text-orange-600" />
                    ) : (
                      <Square className="w-5 h-5 text-gray-400" />
                    )}
                  </Button>
                </div>
                {/* Deal Image */}
                <div className="flex-shrink-0 w-20 h-20 bg-gray-100 rounded-lg overflow-hidden">
                  <img
                    src={getSafeImageUrl(deal.imageUrl)}
                    alt={deal.title}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Deal Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {deal.title}
                        </h3>
                        {deal.featured && (
                          <Badge className="bg-yellow-500">
                            <Sparkles className="w-3 h-3 mr-1" />
                            Featured
                          </Badge>
                        )}
                      </div>

                      {/* Deal Meta */}
                      <div className="flex flex-wrap items-center gap-2 mb-2">
                        <Badge variant="secondary">{deal.store.name}</Badge>
                        <Badge variant="outline">{deal.category.name}</Badge>
                        <span className="text-sm text-gray-500">
                          by {deal.author.name}
                        </span>
                        <span className="text-sm text-gray-500">
                          {formatDistanceToNow(new Date(deal.createdAt))} ago
                        </span>
                      </div>

                      {/* Price */}
                      <div className="flex items-center justify-between gap-2 mb-2">
                        <div className="flex items-center gap-2">
                          <span className="text-xl font-bold text-orange-600">
                            £{safeNumber(deal.dealPrice).toFixed(2)}
                          </span>
                          {deal.originalPrice && (
                            <>
                              <span className="text-sm text-gray-500 line-through">
                                £{safeNumber(deal.originalPrice).toFixed(2)}
                              </span>
                              <span className="text-sm text-green-600 font-semibold">
                                ({calculateDiscountPercentage(safeNumber(deal.dealPrice), safeNumber(deal.originalPrice))}% off)
                              </span>
                            </>
                          )}
                        </div>
                        {!deal.originalPrice && (
                          <SetOriginalPrice
                            dealId={deal.id}
                            hasOriginalPrice={!!deal.originalPrice}
                            onSuccess={fetchDeals}
                          />
                        )}
                      </div>

                      {/* Stats */}
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Eye className="w-4 h-4" />
                          {deal.viewCount}
                        </div>
                        <div className="flex items-center gap-1">
                          <MessageSquare className="w-4 h-4" />
                          {deal._count.comments}
                        </div>
                        <div className="flex items-center gap-1">
                          <Heart className="w-4 h-4" />
                          {deal._count.savedDeals}
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="w-4 h-4" />
                          {safeNumber(deal.averageRating) > 0 ? safeNumber(deal.averageRating).toFixed(1) : "N/A"}
                        </div>
                        <div className="text-green-600">
                          ↑{deal.upvotes}
                        </div>
                        <div className="text-red-600">
                          ↓{deal.downvotes}
                        </div>
                      </div>

                      {/* Deal ID */}
                      <div className="mt-2 text-xs text-gray-500 font-mono">
                        ID: {deal.id}
                      </div>
                    </div>

                    {/* Actions */}
<div className="flex flex-col gap-2">
  <Button
    size="sm"
    variant={deal.featured ? "default" : "outline"}
    onClick={() => handleFeatureToggle(deal.id, deal.featured)}
    disabled={actionLoading === deal.id}
    className={deal.featured ? "bg-yellow-500 hover:bg-yellow-600" : ""}
  >
    <Sparkles className="w-4 h-4 mr-1" />
    {deal.featured ? "Unfeature" : "Feature"}
  </Button>
  <Button
    size="sm"
    variant="outline"
    onClick={() => window.open(`/deals/${generateDealSlugWithId(deal.title, deal.id)}`, "_blank")}
  >
    <ExternalLink className="w-4 h-4 mr-1" />
    View Live Deal
  </Button>  
  
  
  {(() => {
    const { shareText, shareUrl } = generateShareText(deal);
    return (
      <ShareModal title={shareText} url={shareUrl}>
        <Button size="sm" variant="outline" className="w-full">
          <Share2 className="w-4 h-4 mr-1" />
          Share
        </Button>
      </ShareModal>
    );
  })()}
  <Button
    size="sm"
    variant="outline"
    onClick={() => window.open(`/api/deals/${deal.id}/redirect`, "_blank")}
  >
    <ExternalLink className="w-4 h-4 mr-1" />
    View Store
  </Button>
  <Button
    size="sm"
    variant="secondary"
    onClick={() => router.push(`/admin/deals/${deal.id}/edit`)}
  >
    Edit
  </Button>
  <Button
    size="sm"
    variant="destructive"
    onClick={() => handleDeleteDeal(deal.id)}
    disabled={actionLoading === deal.id}
  >
    <Trash2 className="w-4 h-4 mr-1" />
    Delete
  </Button>
</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredDeals.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-500">No deals found.</p>
          </div>
        )}
      </div>
    </div>
  )
}
