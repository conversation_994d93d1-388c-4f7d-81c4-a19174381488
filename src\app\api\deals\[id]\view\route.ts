import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { headers } from "next/headers";

// Efficient view tracking - one record per IP per deal
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = parseInt(id, 10);

    if (isNaN(dealId)) {
      return NextResponse.json(
        { error: "Invalid deal ID" },
        { status: 400 }
      );
    }

    // Get client IP for rate limiting
    const headersList = await headers();
    const forwardedFor = headersList.get("x-forwarded-for") || "";
    const realIp = headersList.get("x-real-ip") || "";
    const clientIp = forwardedFor.split(',')[0].trim() || realIp || 'unknown';
    
    // Check if this IP has viewed this deal recently (within 1 hour)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    
    const existingView = await db.$queryRaw`
      SELECT last_viewed FROM deal_ip_views 
      WHERE deal_id = ${dealId} AND client_ip = ${clientIp}
      LIMIT 1
    `;

    // If this IP viewed recently, don't count again
    if (existingView && Array.isArray(existingView) && existingView.length > 0) {
      const lastViewed = new Date(existingView[0].last_viewed);
      if (lastViewed > oneHourAgo) {
        return NextResponse.json({ 
          success: true, 
          counted: false, 
          reason: "Recent view from this IP" 
        });
      }
    }

    // Count this view and update/insert the IP record
    await db.$transaction(async (tx) => {
      // Insert or update the IP's last view time (only one record per IP per deal)
      await tx.$executeRaw`
        INSERT OR REPLACE INTO deal_ip_views (deal_id, client_ip, last_viewed)
        VALUES (${dealId}, ${clientIp}, ${new Date()})
      `;
      
      // Increment the view count
      await tx.deal.update({
        where: { id: dealId },
        data: {
          viewCount: {
            increment: 1
          }
        }
      });
    });

    return NextResponse.json({ success: true, counted: true });

  } catch (error) {
    console.error("Error tracking view:", error);
    return NextResponse.json(
      { error: "Failed to track view" },
      { status: 500 }
    );
  }
} 