"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Users, Package, Clock, TrendingUp, MessageSquare, Store, FolderOpen, Mail, ChevronDown, ChevronRight, LucideIcon, Download } from "lucide-react"
import Link from "next/link"
import { useState } from "react"
import { AdminStats } from "@/lib/admin"

interface AdminLayoutProps {
  children: React.ReactNode
  stats: AdminStats
  currentPage: string
}

interface NavItem {
  key: string
  label: string
  icon: LucideIcon
  href?: string
  subItems?: NavItem[]
}

const adminNavItems: NavItem[] = [
  { key: "dashboard", label: "Dashboard", icon: TrendingUp, href: "/admin" },
  { 
    key: "deals", 
    label: "Deals", 
    icon: Package, 
    href: "/admin/deals",
    subItems: [
      { key: "pending", label: "Pending", icon: Clock, href: "/admin/pending" }
    ]
  },
  { key: "users", label: "Users", icon: Users, href: "/admin/users" },
  { key: "categories", label: "Categories", icon: FolderOpen, href: "/admin/categories" },
  { key: "stores", label: "Stores", icon: Store, href: "/admin/stores" },
  { key: "comments", label: "Comments", icon: MessageSquare, href: "/admin/comments" },
  { key: "emails", label: "Emails", icon: Mail, href: "/admin/emails" },
  { key: "scrapers", label: "Scrapers", icon: Download, href: "/admin/scrapers" },
]

function NavItemComponent({ item, currentPage, stats, level = 0 }: { 
  item: NavItem
  currentPage: string
  stats: AdminStats
  level?: number 
}) {
  const [isExpanded, setIsExpanded] = useState(
    currentPage === item.key || 
    (item.subItems && item.subItems.some(subItem => currentPage === subItem.key)) ||
    item.key === "deals" // Always expand deals section by default
  )
  
  const Icon = item.icon
  const isActive = currentPage === item.key
  const hasSubItems = item.subItems && item.subItems.length > 0
  const isParentOfActive = item.subItems && item.subItems.some(subItem => currentPage === subItem.key)

  const baseClasses = `flex items-center gap-3 py-3 text-sm font-medium transition-colors ${
    level > 0 ? 'pl-12 pr-6' : 'px-6'
  }`
  
  const activeClasses = isActive || isParentOfActive
    ? "bg-orange-50 text-orange-600 border-r-2 border-orange-600"
    : "text-gray-700 hover:bg-gray-50 hover:text-orange-600"

  if (hasSubItems) {
    return (
      <div>
        <div className={`${baseClasses} ${activeClasses} cursor-pointer`}>
          {item.href ? (
            <Link href={item.href} className="flex items-center gap-3 flex-1">
              <Icon className="h-5 w-5" />
              {item.label}
            </Link>
          ) : (
            <div className="flex items-center gap-3 flex-1">
              <Icon className="h-5 w-5" />
              {item.label}
            </div>
          )}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1 hover:bg-gray-200 rounded"
          >
            {isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </button>
        </div>
        
        {isExpanded && item.subItems && (
          <div className="bg-gray-50">
            {item.subItems.map((subItem) => (
              <NavItemComponent
                key={subItem.key}
                item={subItem}
                currentPage={currentPage}
                stats={stats}
                level={level + 1}
              />
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <Link
      href={item.href!}
      className={`${baseClasses} ${activeClasses}`}
    >
      <Icon className="h-5 w-5" />
      {item.label}
      {item.key === "pending" && stats.pendingDeals > 0 && (
        <span className="ml-auto bg-orange-500 text-white text-xs rounded-full px-2 py-1">
          {stats.pendingDeals}
        </span>
      )}
    </Link>
  )
}

export default function AdminLayout({ children, stats, currentPage }: AdminLayoutProps) {
  const getCurrentPageTitle = () => {
    for (const item of adminNavItems) {
      if (item.key === currentPage) return item.label
      if (item.subItems) {
        const subItem = item.subItems.find(sub => sub.key === currentPage)
        if (subItem) return subItem.label
      }
    }
    return 'Dashboard'
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex">
        {/* Left Sidebar */}
        <div className="w-64 bg-white shadow-sm min-h-screen">
          <div className="p-6 border-b border-gray-200">
            <h1 className="text-xl font-bold text-gray-900">Admin Dashboard</h1>
            <p className="text-sm text-gray-600 mt-1">NiceDeals Admin</p>
          </div>

          {/* Navigation Links */}
          <nav className="py-6">
            {adminNavItems.map((item) => (
              <NavItemComponent
                key={item.key}
                item={item}
                currentPage={currentPage}
                stats={stats}
              />
            ))}
          </nav>

          {/* Compact Stats in Sidebar */}
          <div className="px-6 py-4 border-t border-gray-200">
            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Quick Stats</h3>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Users</span>
                <span className="font-medium">{stats.totalUsers}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Total Deals</span>
                <span className="font-medium">{stats.totalDeals}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Active</span>
                <span className="font-medium text-green-600">{stats.activeDeals}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Today</span>
                <span className="font-medium text-blue-600">{stats.recentActivity}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1">
          <div className="p-8">
            {/* Header */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                {getCurrentPageTitle()}
              </h2>
              <p className="text-gray-600">Manage deals, users, and platform content</p>
            </div>

            {/* Statistics Cards - Only show on dashboard */}
            {currentPage === "dashboard" && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Users</p>
                        <p className="text-2xl font-bold">{stats.totalUsers}</p>
                      </div>
                      <Users className="h-8 w-8 text-orange-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Deals</p>
                        <p className="text-2xl font-bold">{stats.totalDeals}</p>
                        <p className="text-xs text-green-600">{stats.activeDeals} active</p>
                      </div>
                      <Package className="h-8 w-8 text-orange-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Pending</p>
                        <p className="text-2xl font-bold text-orange-600">{stats.pendingDeals}</p>
                        <p className="text-xs text-gray-500">Need approval</p>
                      </div>
                      <Clock className="h-8 w-8 text-orange-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">24h Activity</p>
                        <p className="text-2xl font-bold">{stats.recentActivity}</p>
                        <p className="text-xs text-blue-600">New deals</p>
                      </div>
                      <TrendingUp className="h-8 w-8 text-orange-500" />
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Page Content */}
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}
