#!/bin/bash

# NiceDeals v2 Production Deployment Script
# Run this on your Ubuntu server as root or with sudo

set -e  # Exit on any error

# Configuration
DOMAIN="www.nicedeals.app"
APP_NAME="nicedeals"
APP_DIR="/home/<USER>/$APP_NAME"
PM2_APP_NAME="nicedeals"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Starting NiceDeals v2 Production Deployment${NC}"

# Auto-detect deployment type
FIRST_TIME_SETUP=false
PM2_APP_EXISTS=false

# Check if PM2 app exists (handle case where PM2 isn't installed yet)
if command -v pm2 &> /dev/null; then
    if pm2 list | grep -q "$PM2_APP_NAME"; then
        PM2_APP_EXISTS=true
    fi
fi

if [ ! -f "/etc/nginx/sites-available/$APP_NAME" ] || [ "$PM2_APP_EXISTS" = false ] || [ ! -f "$APP_DIR/prisma/nicedeals.db" ]; then
    FIRST_TIME_SETUP=true
    echo -e "${YELLOW}[DETECTED] First time setup${NC}"
else
    echo -e "${GREEN}[DETECTED] Update/re-deployment${NC}"
fi

# Function to print colored output
print_status() {
    echo -e "${GREEN}[OK] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

print_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root (use sudo)"
   exit 1
fi

# Check if Node.js is installed
print_status "Checking Node.js installation..."
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if PM2 is installed, install if needed
print_status "Checking PM2 installation..."
if ! command -v pm2 &> /dev/null; then
    print_status "Installing PM2..."
    npm install -g pm2
else
    print_status "PM2 already installed"
fi

# Check if app directory exists
if [ ! -d "$APP_DIR" ]; then
    print_error "App directory $APP_DIR does not exist. Please git clone the repository first."
    exit 1
fi

print_status "Using existing application directory..."
cd $APP_DIR

# Check if this is a git repository
if [ ! -d ".git" ]; then
    print_error "Not a git repository. Please git clone the repository to $APP_DIR first."
    exit 1
fi

# Check if deploy script will be updated
print_status "Checking for deploy script updates..."
SCRIPT_HASH_BEFORE=""
if [ -f "deploy.sh" ]; then
    SCRIPT_HASH_BEFORE=$(md5sum deploy.sh | cut -d' ' -f1)
fi

# Pull latest code
print_status "Pulling latest code from repository..."
git fetch origin

# Stash any local changes temporarily
if ! git diff-index --quiet HEAD --; then
    print_status "Stashing local changes..."
    git stash push -m "Deploy stash $(date)"
fi

# Pull the latest changes
git pull origin master

# We intentionally don't restore the stash as those were likely
# temporary files or documentation that shouldn't override repo changes

# Check if deploy script was updated
if [ -f "deploy.sh" ] && [ ! -z "$SCRIPT_HASH_BEFORE" ]; then
    SCRIPT_HASH_AFTER=$(md5sum deploy.sh | cut -d' ' -f1)
    if [ "$SCRIPT_HASH_BEFORE" != "$SCRIPT_HASH_AFTER" ]; then
        print_warning "Deploy script has been updated!"
        print_warning "Stopping current deployment to use the latest version."
        echo ""
        echo "Please run the deployment again to use the updated script:"
        echo "  cd /home/<USER>/nicedeals"
        echo "  sudo ./deploy.sh"
        echo ""
        exit 0
    fi
fi

print_status "Deploy script is up to date, continuing..."

# Install dependencies
print_status "Installing Node.js dependencies..."
npm ci --production=false

# Handle database setup
DB_FILE="$APP_DIR/prisma/dev.db"
PROD_DB_FILE="$APP_DIR/prisma/nicedeals.db"

if [ "$FIRST_TIME_SETUP" = true ]; then
    if [ ! -f "$PROD_DB_FILE" ]; then
        print_status "First time setup: Setting up production database..."
        if [ -f "$DB_FILE" ]; then
            cp "$DB_FILE" "$PROD_DB_FILE"
            print_status "Database copied from dev to production"
        else
            print_warning "Dev database not found, creating empty production database..."
            touch "$PROD_DB_FILE"
        fi
    fi
else
    # Update mode - backup existing database
    if [ -f "$PROD_DB_FILE" ]; then
        print_status "Backing up production database..."
        cp "$PROD_DB_FILE" "$PROD_DB_FILE.backup.$(date +%Y%m%d_%H%M%S)"
        print_status "Database backed up"
    fi
fi

# Check for production environment file
print_status "Checking production environment file..."
if [ ! -f ".env" ]; then
    print_error ".env file not found. Please ensure .env contains production configuration."
    exit 1
fi

print_status "Using production environment configuration from .env"

# Clean up development environment files (if they exist)
print_status "Cleaning up development environment files..."
if [ -f ".env.dev" ]; then
    rm .env.dev
    print_status "Removed .env.dev from production server"
fi

# Build the application
print_status "Building Next.js application..."
npm run build

# Set up PM2 configuration
print_status "Setting up PM2 configuration..."
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: '$PM2_APP_NAME',
    script: 'npm',
    args: 'start',
    cwd: '$APP_DIR',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: 'production',
      PORT: 3010,
      DEBUG: ''
    },
    env_file: '.env',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    max_restarts: 10,
    min_uptime: '10s',
    max_memory_restart: '1G'
  }]
};
EOF

# Set up Nginx configuration
print_status "Setting up Nginx configuration..."
cat > /etc/nginx/sites-available/$APP_NAME << EOF
server {
    listen 80;
    server_name $DOMAIN nicedeals.app;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/javascript application/xml+rss application/json;

    # Rate limiting zones defined in main nginx.conf

    # Static files caching
    location /_next/static/ {
        alias $APP_DIR/.next/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /images/ {
        alias $APP_DIR/public/images/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /uploads/ {
        alias $APP_DIR/public/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API routes
    location /api/ {
        proxy_pass http://127.0.0.1:3010;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 86400;
    }

    # Main application
    location / {
        proxy_pass http://127.0.0.1:3010;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 86400;
    }

    # Security: Block access to sensitive files
    location ~ /\. {
        deny all;
    }

    location ~ /(prisma|\.env) {
        deny all;
    }
}
EOF

# Enable Nginx site
print_status "Enabling Nginx site..."
ln -sf /etc/nginx/sites-available/$APP_NAME /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
print_status "Testing Nginx configuration..."
nginx -t

# Set proper permissions
print_status "Setting file permissions..."
chown -R gazza:gazza $APP_DIR
# Set directory permissions (755) excluding large/unnecessary dirs, using xargs for efficiency
find $APP_DIR -type d \( -path $APP_DIR/node_modules -prune -o -path $APP_DIR/.next -prune -o -path $APP_DIR/public/uploads -prune \) -o -print0 | xargs -0 chmod 755
# Set file permissions (644) excluding large/unnecessary dirs, using xargs for efficiency
find $APP_DIR -type f ! -path "$APP_DIR/node_modules/*" ! -path "$APP_DIR/.next/*" ! -path "$APP_DIR/public/uploads/*" | xargs chmod 644
# Make scripts and binaries executable
chmod +x $APP_DIR/deploy.sh
# Restore execute permissions for node_modules binaries
if [ -d "$APP_DIR/node_modules/.bin" ]; then
    chmod +x $APP_DIR/node_modules/.bin/*
fi
# Ensure database has correct permissions
chmod 644 $APP_DIR/prisma/nicedeals.db

# Ensure PM2 directory exists for user gazza
mkdir -p /home/<USER>/.pm2
chown -R gazza:gazza /home/<USER>/.pm2

# Stop existing PM2 processes (if any) - check both root and user PM2
print_status "Managing PM2 processes..."
sudo -u gazza pm2 stop $PM2_APP_NAME 2>/dev/null || true
sudo -u gazza pm2 delete $PM2_APP_NAME 2>/dev/null || true
pm2 stop $PM2_APP_NAME 2>/dev/null || true
pm2 delete $PM2_APP_NAME 2>/dev/null || true

# Start application with PM2 as user gazza
print_status "Starting application with PM2 as user gazza..."
cd $APP_DIR
sudo -u gazza pm2 start ecosystem.config.js
sudo -u gazza pm2 save
sudo -u gazza pm2 startup

# Restart Nginx
print_status "Restarting Nginx..."
systemctl restart nginx
systemctl enable nginx

print_status "Deployment completed successfully!"
echo ""
print_status "Your NiceDeals app is now running at: https://$DOMAIN"
print_status "To deploy updates in the future, simply run: sudo ./deploy.sh"
echo ""
print_warning "IMPORTANT NEXT STEPS:"
echo "1. Configure your domain DNS to point to this server"
echo "2. Set up Cloudflare SSL (Flexible mode) for your domain"
echo "3. Test the application thoroughly"
echo ""
print_status "Useful commands:"
echo "  - Check PM2 status: pm2 status (as user gazza)"
echo "  - View PM2 logs: pm2 logs $PM2_APP_NAME (as user gazza)"
echo "  - Check Nginx status: systemctl status nginx"
echo "  - Deploy updates: cd /home/<USER>/nicedeals && sudo ./deploy.sh" 