"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { But<PERSON> } from "@/components/ui/button"
import { ThumbsUp, ThumbsDown } from "lucide-react"
import { useRouter } from "next/navigation"

interface VoteButtonsProps {
  dealId: number
  initialUpvotes: number
  initialDownvotes: number
  className?: string
  variant?: "default" | "orange-bar"
  onVoteChange?: (upvotes: number, downvotes: number) => void
  showScore?: boolean
}

export default function VoteButtons({ 
  dealId, 
  initialUpvotes, 
  initialDownvotes, 
  className = "",
  variant = "default",
  onVoteChange,
  showScore
}: VoteButtonsProps) {
  const { data: session } = useSession()
  const [upvotes, setUpvotes] = useState(initialUpvotes)
  const [downvotes, setDownvotes] = useState(initialDownvotes)
  const [userVote, setUserVote] = useState<"UPVOTE" | "DOWNVOTE" | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  // Fetch user's current vote
  useEffect(() => {
    if (session?.user) {
      fetch(`/api/deals/${dealId}/vote`)
        .then(res => res.json())
        .then(data => {
          if (data.userVote) {
            setUserVote(data.userVote)
          }
        })
        .catch(console.error)
    }
  }, [dealId, session])

  const handleVote = async (type: "UPVOTE" | "DOWNVOTE") => {
    if (!session?.user) {
      router.push(`/auth/signin?callbackUrl=${window.location.pathname}`)
      return
    }

    // Check if user is trying to vote the same way they already voted
    if (userVote === type) {
      const voteType = type === "UPVOTE" ? "upvote" : "downvote"
      const confirmed = confirm(`You have already ${type === "UPVOTE" ? "upvoted" : "downvoted"} this deal. Do you want to remove your ${voteType}?`)
      
      if (!confirmed) {
        return
      }
    }

    // Check if user is trying to vote opposite to their current vote
    if (userVote && userVote !== type) {
      const currentVote = userVote === "UPVOTE" ? "upvoted" : "downvoted"
      const newVote = type === "UPVOTE" ? "upvote" : "downvote"
      alert(`You cannot ${newVote} if you have already ${currentVote}`)
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch(`/api/deals/${dealId}/vote`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ type }),
      })

      const data = await response.json()

      if (response.ok) {
        setUpvotes(data.deal.upvotes)
        setDownvotes(data.deal.downvotes)
        setUserVote(data.userVote)
        if (typeof onVoteChange === "function") {
          onVoteChange(data.deal.upvotes, data.deal.downvotes)
        }
      } else {
        console.error("Vote failed:", data.message)
      }
    } catch (error) {
      console.error("Vote error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const netVotes = upvotes - downvotes

  if (variant === "orange-bar") {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <button
          onClick={() => handleVote("UPVOTE")}
          disabled={isLoading}
          className={`flex items-center gap-2 p-2 rounded-lg border transition-colors ${
            userVote === "UPVOTE"
              ? "bg-green-600 text-white border-green-600"
              : "bg-white border-gray-300 text-gray-700 hover:border-green-500 hover:text-green-600"
          }`}
        >
          <ThumbsUp className={`h-4 w-4 ${userVote === "UPVOTE" ? "" : "fill-none"}`} />
          
        </button>
        
        {/** Show score between buttons if showScore is true */}
        {typeof showScore === "undefined" || showScore ? (
          <span className={`font-bold px-2 text-lg ${
            netVotes > 0 ? "text-green-600" : 
            netVotes < 0 ? "text-red-600" : 
            "text-gray-600"
          }`}>
            {netVotes > 0 ? `+${netVotes}` : netVotes === 0 ? "0" : netVotes}
          </span>
        ) : null}
        
        <button
          onClick={() => handleVote("DOWNVOTE")}
          disabled={isLoading}
          className={`flex items-center gap-2 p-2 rounded-lg border transition-colors ${
            userVote === "DOWNVOTE"
              ? "bg-red-600 text-white border-red-600"
              : "bg-white border-gray-300 text-gray-700 hover:border-red-500 hover:text-red-600"
          }`}
        >
          <ThumbsDown className={`h-4 w-4 ${userVote === "DOWNVOTE" ? "" : "fill-none"}`} />
          
        </button>
      </div>
    )
  }

  return (
    <div className={`flex flex-col items-center space-y-1 ${className}`}>
      <Button
        variant={userVote === "UPVOTE" ? "default" : "outline"}
        size="sm"
        onClick={() => handleVote("UPVOTE")}
        disabled={isLoading}
        className={`h-8 w-8 p-0 ${
          userVote === "UPVOTE" 
            ? "bg-green-600 hover:bg-green-700 text-white border-green-600" 
            : "hover:bg-green-50 hover:border-green-600 hover:text-green-600"
        }`}
      >
        <ThumbsUp className={`h-4 w-4 ${userVote === "UPVOTE" ? "" : "fill-none"}`} />
      </Button>
      
      <span className={`text-sm font-medium min-w-[2rem] text-center ${
        netVotes > 0 ? "text-green-600" : 
        netVotes < 0 ? "text-red-600" : 
        "text-gray-600"
      }`}>
        {netVotes > 0 ? `+${netVotes}` : netVotes === 0 ? "0" : netVotes}
      </span>
      
      <Button
        variant={userVote === "DOWNVOTE" ? "destructive" : "outline"}
        size="sm"
        onClick={() => handleVote("DOWNVOTE")}
        disabled={isLoading}
        className={`h-8 w-8 p-0 ${
          userVote === "DOWNVOTE" 
            ? "bg-red-600 hover:bg-red-700 text-white border-red-600" 
            : "hover:bg-red-50 hover:border-red-600 hover:text-red-600"
        }`}
      >
        <ThumbsDown className={`h-4 w-4 ${userVote === "DOWNVOTE" ? "" : "fill-none"}`} />
      </Button>
    </div>
  )
}
