import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "@/lib/auth"
import { db } from "@/lib/db"
import { validateImageUrl } from "@/lib/image-utils"
import { generateSlug } from "@/lib/slug"
import { addAffiliateTag } from "@/lib/utils"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession()
    
    if (!session?.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      )
    }

    const contentType = request.headers.get("content-type")
    let data: any
    let imageData: any = {}

    if (contentType?.includes("multipart/form-data")) {
      // Handle form data with potential file uploads
      const formData = await request.formData()
      
      // Extract basic form fields
      data = {
        title: formData.get("title") as string,
        description: formData.get("description") as string,
        originalPrice: formData.get("originalPrice") as string,
        dealPrice: formData.get("dealPrice") as string,
        category: formData.get("category") as string,
        store: formData.get("store") as string,
        dealUrl: formData.get("dealUrl") as string,
        couponCode: formData.get("couponCode") as string,
        expiresAt: formData.get("expiresAt") as string,
      }

      // Extract image data
      imageData = {
        imageUrl: formData.get("imageUrl") as string,
        imageUrls: formData.getAll("imageUrls") as string[],
        mainImageFile: formData.get("mainImage") as File,
        galleryImageFiles: formData.getAll("galleryImages") as File[]
      }
    } else {
      // Handle JSON data
      data = await request.json()
      imageData = {
        imageUrl: data.imageUrl,
        imageUrls: data.imageUrls || []
      }
    }

    const {
      title,
      description,
      originalPrice,
      dealPrice,
      category,
      store,
      dealUrl,
      couponCode,
      expiresAt
    } = data

    // Validate required fields
    if (!title || !description || !dealPrice || !category || !store || !dealUrl) {
      return NextResponse.json(
        { message: "Missing required fields" },
        { status: 400 }
      )
    }

    // Convert category and store to integers
    const categoryId = parseInt(category, 10)
    const storeId = parseInt(store, 10)

    if (isNaN(categoryId) || isNaN(storeId)) {
      return NextResponse.json(
        { message: "Invalid category or store ID" },
        { status: 400 }
      )
    }

    // Validate URL format
    try {
      new URL(dealUrl)
    } catch {
      return NextResponse.json(
        { message: "Invalid deal URL format" },
        { status: 400 }
      )
    }

    // Calculate discount percentage if original price is provided
    let discountPercentage = null
    if (originalPrice && originalPrice > dealPrice) {
      discountPercentage = Math.round(((originalPrice - dealPrice) / originalPrice) * 100)
    }

    // Generate slug for the deal
    const slug = generateSlug(title)

    // Add affiliate tag to Amazon URLs
    const finalDealUrl = addAffiliateTag(dealUrl.trim(), storeId)

    // Process and validate images
    const processedImages: Array<{
      url: string
      thumbnailUrl?: string
      isMain: boolean
      altText?: string
    }> = []
    
    let mainImageUrl: string | null = null
    let thumbnailUrl: string | null = null

    // Handle main image URL
    if (imageData.imageUrl && imageData.imageUrl.trim()) {
      const validation = await validateImageUrl(imageData.imageUrl.trim())
      if (validation.valid) {
        mainImageUrl = imageData.imageUrl.trim()
        thumbnailUrl = mainImageUrl // Simple approach - same URL for now
        if (mainImageUrl) {
          processedImages.push({
            url: mainImageUrl,
            thumbnailUrl: thumbnailUrl || undefined,
            isMain: true,
            altText: title
          })
        }
      }
    }

    // Handle additional image URLs
    if (imageData.imageUrls && Array.isArray(imageData.imageUrls)) {
      for (const url of imageData.imageUrls) {
        if (url && url.trim()) {
          const validation = await validateImageUrl(url.trim())
          if (validation.valid) {
            processedImages.push({
              url: url.trim(),
              thumbnailUrl: url.trim(),
              isMain: false,
              altText: title
            })
          }
        }
      }
    }

    // Create the deal
    const deal = await db.deal.create({
      data: {
        slug,
        title: title.trim(),
        description: description.trim(),
        originalPrice: originalPrice ? parseFloat(originalPrice) : null,
        dealPrice: parseFloat(dealPrice),
        discountPercentage,
        storeId,
        categoryId,
        dealUrl: finalDealUrl,
        imageUrl: mainImageUrl ?? undefined,
        thumbnailUrl: thumbnailUrl ?? undefined,
        couponCode: couponCode?.trim() || null,
        expiresAt: expiresAt ? new Date(expiresAt) : null,
        status: "PENDING", // All deals start as pending for review
        featured: false,
        authorId: parseInt(session.user.id as string, 10),
      },
      select: {
        id: true,
        title: true,
        status: true,
        createdAt: true,
      }
    })

    // Create image records if we have processed images
    if (processedImages.length > 0) {
      const imageRecords = processedImages.map((img, index) => ({
        dealId: deal.id,
        imageUrl: img.url,
        thumbnailUrl: img.thumbnailUrl || img.url,
        altText: img.altText || title,
        sortOrder: index,
        isMain: img.isMain,
        createdAt: new Date(),
        updatedAt: new Date()
      }))

      // Note: This would require the DealImage model to be created
      // For now, we'll just store the main image in the deal record
      try {
        // await db.dealImage.createMany({ data: imageRecords })
        console.log(`Would create ${imageRecords.length} image records for deal ${deal.id}`)
      } catch (error) {
        console.warn("Failed to create image records:", error)
        // Continue anyway - deal is still created
      }
    }

    return NextResponse.json({
      success: true,
      message: "Deal submitted successfully! It will be reviewed by our team.",
      deal,
      imagesProcessed: processedImages.length
    }, { status: 201 })

  } catch (error) {
    console.error("Deal submission error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
