import AdminLayout from "@/components/admin/AdminLayout";
import { getAdminStats } from "@/lib/admin";
import EditDealForm from "@/components/admin/EditDealForm";

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'

interface AdminEditDealPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function AdminEditDealPage({ params }: AdminEditDealPageProps) {
  const { id } = await params;
  const stats = await getAdminStats();

  return (
    <AdminLayout stats={stats} currentPage="deals">
      <EditDealForm dealId={id} />
    </AdminLayout>
  );
}
