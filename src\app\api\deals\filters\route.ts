import { NextResponse } from "next/server"
import { db } from "@/lib/db"
import { getCachedData, setCachedData, CACHE_KEYS, CACHE_TTL } from "@/lib/cache"

export async function GET() {
  try {
    // Check cache first
    const cacheKey = `${CACHE_KEYS.DEALS_BROWSE}:filters`
    const cachedData = getCachedData(cacheKey)
    
    if (cachedData) {
      return NextResponse.json(cachedData)
    }

    // Get unique categories that have active deals
    const categoriesWithDeals = await db.category.findMany({
      where: {
        deals: {
          some: {
            status: 'ACTIVE',
          },
        },
      },
      include: {
        _count: {
          select: {
            deals: {
              where: {
                status: 'ACTIVE',
              },
            },
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    })

    // Get unique stores that have active deals
    const storesWithDeals = await db.store.findMany({
      where: {
        deals: {
          some: {
            status: 'ACTIVE',
          },
        },
      },
      include: {
        _count: {
          select: {
            deals: {
              where: {
                status: 'ACTIVE',
              },
            },
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    })

    // Format the data
    const categories = [
      "All Categories",
      ...categoriesWithDeals.map(item => item.name)
    ]

    const stores = [
      "All Stores", 
      ...storesWithDeals.map(item => item.name)
    ]

    // Get price range info for better filtering
    const priceStats = await db.deal.aggregate({
      where: {
        status: 'ACTIVE',
      },
      _min: {
        dealPrice: true,
      },
      _max: {
        dealPrice: true,
      },
    })

    const filterData = {
      categories,
      stores,
      categoriesWithCounts: categoriesWithDeals.map(item => ({
        name: item.name,
        count: item._count.deals,
      })),
      storesWithCounts: storesWithDeals.map(item => ({
        name: item.name,
        count: item._count.deals,
      })),
      priceRange: {
        min: priceStats._min.dealPrice || 0,
        max: priceStats._max.dealPrice || 1000,
      },
    }

    // Cache for 5 minutes
    setCachedData(cacheKey, filterData, CACHE_TTL.MEDIUM)

    return NextResponse.json(filterData)
  } catch (error) {
    console.error("Error fetching filter data:", error)
    return NextResponse.json(
      { error: "Failed to fetch filter data" },
      { status: 500 }
    )
  }
}
