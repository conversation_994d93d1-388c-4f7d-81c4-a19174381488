"use client"

import { use<PERSON><PERSON><PERSON>, useSearchPara<PERSON> } from "next/navigation"
import { useEffect, useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { User, Heart, Package, MessageSquare, Edit, TrendingUp, ThumbsUp, ThumbsDown } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import DealCard from "@/components/DealCard"
import Link from "next/link"

// TypeScript interfaces for proper typing
interface DealAuthor {
  id: number
  name: string | null
  image?: string | null
}

interface DealStore {
  id: number
  name: string
  slug: string
}

interface DealCategory {
  id: number
  name: string
  slug: string
}

interface DealCounts {
  votes: number
  comments: number
  savedDeals: number
}

interface DealData {
  id: number
  slug: string
  title: string
  description: string
  originalPrice: number | null
  dealPrice: number
  discount: number
  store?: DealStore
  category?: DealCategory
  dealUrl: string
  imageUrl: string
  upvotes: number
  downvotes: number
  couponCode?: string
  expiresAt?: string
  createdDate: string
  isNew?: boolean
  author?: DealAuthor
  _count?: DealCounts
}

interface SavedDeal {
  id: string
  dealId: number
  userId: number
  createdAt: Date
  deal: DealData
}

interface UserComment {
  id: string
  content: string
  createdAt: Date
  deal: {
    id: number
    title: string
  }
}

interface UserNotification {
  id: string
  type: string
  value: string
  active: boolean
  userId: number
  createdAt: Date
}

interface UserVote {
  id: string
  type: 'UPVOTE' | 'DOWNVOTE'
  dealId: number
  userId: number
  createdAt: Date
  deal: DealData
}

interface DashboardUser {
  id: string
  name?: string
  email: string
  role: string
  newsletter?: boolean
}

interface DashboardTabsProps {
  data: {
    user: DashboardUser
    savedDeals: SavedDeal[]
    userDeals: DealData[]
    userComments: UserComment[]
    notifications: UserNotification[]
    userVotes: UserVote[]
  }
}

export default function DashboardTabs({ data }: DashboardTabsProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || "overview")
  const [voteFilter, setVoteFilter] = useState<'ALL' | 'UPVOTE' | 'DOWNVOTE'>('ALL')

  // Sync tab with URL parameter changes
  useEffect(() => {
    const tab = searchParams.get('tab') || "overview"
    setActiveTab(tab)
  }, [searchParams])

  const handleTabChange = (value: string) => {
    setActiveTab(value)
    const params = new URLSearchParams(searchParams.toString())
    if (value === "overview") {
      params.delete('tab')
    } else {
      params.set('tab', value)
    }
    const newUrl = params.toString() ? `/dashboard?${params.toString()}` : '/dashboard'
    router.push(newUrl)
  }

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-4">
      <TabsList className="grid w-full grid-cols-6 h-12 bg-gray-100 p-1">
        <TabsTrigger 
          value="overview"
          className="data-[state=active]:bg-orange-500 data-[state=active]:text-white data-[state=active]:shadow-sm data-[state=active]:font-semibold transition-all duration-200"
        >
          Overview
        </TabsTrigger>
        <TabsTrigger 
          value="saved"
          className="data-[state=active]:bg-orange-500 data-[state=active]:text-white data-[state=active]:shadow-sm data-[state=active]:font-semibold transition-all duration-200"
        >
          My Saved Deals
        </TabsTrigger>
        <TabsTrigger 
          value="my-deals"
          className="data-[state=active]:bg-orange-500 data-[state=active]:text-white data-[state=active]:shadow-sm data-[state=active]:font-semibold transition-all duration-200"
        >
          My Deals
        </TabsTrigger>
        <TabsTrigger 
          value="comments"
          className="data-[state=active]:bg-orange-500 data-[state=active]:text-white data-[state=active]:shadow-sm data-[state=active]:font-semibold transition-all duration-200"
        >
          My Comments
        </TabsTrigger>
        <TabsTrigger 
          value="my-votes"
          className="data-[state=active]:bg-orange-500 data-[state=active]:text-white data-[state=active]:shadow-sm data-[state=active]:font-semibold transition-all duration-200"
        >
          My Votes
        </TabsTrigger>
        <TabsTrigger 
          value="settings"
          className="data-[state=active]:bg-orange-500 data-[state=active]:text-white data-[state=active]:shadow-sm data-[state=active]:font-semibold transition-all duration-200"
        >
          Settings
        </TabsTrigger>
      </TabsList>

      <TabsContent value="overview" className="space-y-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
              <CardDescription>
                Your account details and preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center">
                  <User className="w-6 h-6 text-orange-600" />
                </div>
                <div>
                  <p className="font-medium">{data.user.name || "No name set"}</p>
                  <p className="text-sm text-gray-500">{data.user.email}</p>
                  <p className="text-sm text-gray-500">Role: {data.user.role}</p>
                </div>
              </div>
              <Button variant="outline" className="w-full">
                <Edit className="w-4 h-4 mr-2" />
                Edit Profile
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Common tasks and shortcuts
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" asChild className="w-full justify-start">
                <Link href="/post">
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Post New Deal
                </Link>
              </Button>
              <Button variant="outline" asChild className="w-full justify-start">
                <Link href="/browse">
                  <Package className="w-4 h-4 mr-2" />
                  Browse All Deals
                </Link>
              </Button>
              <Button variant="outline" asChild className="w-full justify-start">
                <Link href="/categories">
                  <MessageSquare className="w-4 h-4 mr-2" />
                  Explore Categories
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <TabsContent value="saved" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Quick Stats
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">{data.savedDeals.length}</div>
                <div className="text-sm text-gray-600">Total Saved</div>
              </div>
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {data.savedDeals.filter(sd => sd.deal.expiresAt && new Date(sd.deal.expiresAt) > new Date()).length}
                </div>
                <div className="text-sm text-gray-600">Still Active</div>
              </div>

            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <Heart className="h-6 w-6 text-orange-500" />
              Your Saved Deals
            </CardTitle>
            <CardDescription>
              {data.savedDeals.length > 0 
                ? `You have ${data.savedDeals.length} saved deal${data.savedDeals.length === 1 ? '' : 's'}`
                : "Start saving deals you're interested in!"
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            {data.savedDeals.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {data.savedDeals.map((savedDeal) => (
                  <DealCard key={savedDeal.deal.id} deal={savedDeal.deal} />
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <Heart className="mx-auto h-16 w-16 text-gray-300 mb-6" />
                <h3 className="text-xl font-semibold text-gray-900 mb-3">No saved deals yet</h3>
                <p className="text-gray-500 mb-6 max-w-md mx-auto">
                  Start exploring and save deals that catch your eye.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button asChild size="lg">
                    <Link href="/browse">
                      <Package className="w-4 h-4 mr-2" />
                      Browse All Deals
                    </Link>
                  </Button>
                  <Button variant="outline" asChild size="lg">
                    <Link href="/categories">
                      Explore Categories
                    </Link>
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {data.savedDeals.length > 0 && (
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="outline" asChild>
              <Link href="/browse">
                <Package className="w-4 h-4 mr-2" />
                Find More Deals
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/categories">
                Explore Categories
              </Link>
            </Button>
          </div>
        )}
      </TabsContent>

      <TabsContent value="my-deals" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>My Deals</CardTitle>
            <CardDescription>
              Deals posted to the community
            </CardDescription>
          </CardHeader>
          <CardContent>
            {data.userDeals.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {data.userDeals.map((deal) => (
                  <DealCard key={deal.id} deal={deal} />
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Deals Posted</h3>
                <p className="text-gray-500 mb-4">Share great deals with the community!</p>
                <Button asChild>
                  <Link href="/post">Post Your First Deal</Link>
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="comments" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Recent Comments</CardTitle>
            <CardDescription>
              Your latest activity on deals
            </CardDescription>
          </CardHeader>
          <CardContent>
            {data.userComments.length > 0 ? (
              <div className="space-y-4">
                {data.userComments.map((comment) => (
                  <div key={comment.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <Link
                        href={`/deals/${comment.deal.id}`}
                        className="font-medium text-orange-600 hover:underline"
                      >
                        {comment.deal.title}
                      </Link>
                      <span className="text-sm text-gray-500">
                        {formatDistanceToNow(new Date(comment.createdAt))} ago
                      </span>
                    </div>
                    <p className="text-gray-700">{comment.content}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <MessageSquare className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No comments yet</h3>
                <p className="text-gray-500">Join the conversation on deals!</p>
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="my-votes" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ThumbsUp className="h-5 w-5" />
              Vote Stats
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {data.userVotes.filter(vote => vote.type === 'UPVOTE').length}
                </div>
                <div className="text-sm text-gray-600">Upvotes</div>
              </div>
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {data.userVotes.filter(vote => vote.type === 'DOWNVOTE').length}
                </div>
                <div className="text-sm text-gray-600">Downvotes</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <ThumbsUp className="h-6 w-6 text-orange-500" />
              Your Votes
            </CardTitle>
            <CardDescription>
              {data.userVotes.length > 0 
                ? `You have voted on ${data.userVotes.length} deal${data.userVotes.length === 1 ? '' : 's'}`
                : "Start voting on deals to see them here!"
              }
              {voteFilter !== 'ALL' && data.userVotes.length > 0 && (
                <span className="block text-sm text-orange-600 mt-1">
                  Showing {voteFilter === 'UPVOTE' ? 'upvoted' : 'downvoted'} deals only
                </span>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {data.userVotes.length > 0 ? (
              <div className="space-y-4">
                <div className="flex gap-4 mb-4">
                  <Button 
                    variant={voteFilter === 'ALL' ? 'default' : 'outline'} 
                    size="sm"
                    onClick={() => setVoteFilter('ALL')}
                  >
                    All Votes ({data.userVotes.length})
                  </Button>
                  <Button 
                    variant={voteFilter === 'UPVOTE' ? 'default' : 'outline'} 
                    size="sm"
                    onClick={() => setVoteFilter('UPVOTE')}
                  >
                    Upvotes ({data.userVotes.filter(vote => vote.type === 'UPVOTE').length})
                  </Button>
                  <Button 
                    variant={voteFilter === 'DOWNVOTE' ? 'default' : 'outline'} 
                    size="sm"
                    onClick={() => setVoteFilter('DOWNVOTE')}
                  >
                    Downvotes ({data.userVotes.filter(vote => vote.type === 'DOWNVOTE').length})
                  </Button>
                </div>
                {(() => {
                  const filteredVotes = data.userVotes.filter(vote => voteFilter === 'ALL' || vote.type === voteFilter);
                  
                  if (filteredVotes.length === 0) {
                                         return (
                       <div className="text-center py-12">
                         {voteFilter === 'DOWNVOTE' ? (
                           <ThumbsDown className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                         ) : (
                           <ThumbsUp className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                         )}
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          {voteFilter === 'UPVOTE' ? 'No Upvotes Found' : 
                           voteFilter === 'DOWNVOTE' ? 'No Downvotes Found' : 
                           'No Votes Found'}
                        </h3>
                        <p className="text-gray-500 mb-4">
                          {voteFilter === 'UPVOTE' ? "You haven't upvoted any deals yet." : 
                           voteFilter === 'DOWNVOTE' ? "You haven't downvoted any deals yet." : 
                           "Start voting on deals to see them here."}
                        </p>
                        <Button asChild variant="outline">
                          <Link href="/browse">
                            <Package className="w-4 h-4 mr-2" />
                            Browse Deals
                          </Link>
                        </Button>
                      </div>
                    );
                  }
                  
                  return (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {filteredVotes.map((vote) => (
                        <div key={`${vote.dealId}-${vote.type}`} className="relative">
                          <DealCard deal={vote.deal} />
                          <div className={`absolute top-2 right-2 px-2 py-1 rounded text-xs font-medium ${
                            vote.type === 'UPVOTE' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {vote.type === 'UPVOTE' ? '↑ Upvoted' : '↓ Downvoted'}
                          </div>
                        </div>
                      ))}
                    </div>
                  );
                })()}
              </div>
            ) : (
              <div className="text-center py-16">
                <ThumbsUp className="mx-auto h-16 w-16 text-gray-300 mb-6" />
                <h3 className="text-xl font-semibold text-gray-900 mb-3">No votes yet</h3>
                <p className="text-gray-500 mb-6 max-w-md mx-auto">
                  Start voting on deals to see your voting history here.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button asChild size="lg">
                    <Link href="/browse">
                      <Package className="w-4 h-4 mr-2" />
                      Browse All Deals
                    </Link>
                  </Button>
                  <Button variant="outline" asChild size="lg">
                    <Link href="/categories">
                      Explore Categories
                    </Link>
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="settings" className="space-y-4">
        <div className="grid gap-6">
          {/* Profile Information */}
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>
                Update your personal details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ProfileForm user={data.user} />
            </CardContent>
          </Card>

          {/* Newsletter Preferences */}
          <Card>
            <CardHeader>
              <CardTitle>Newsletter Preferences</CardTitle>
              <CardDescription>
                Manage your email subscriptions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <NewsletterForm user={data.user} />
            </CardContent>
          </Card>

          {/* Account Security */}
          <Card>
            <CardHeader>
              <CardTitle>Account Security</CardTitle>
              <CardDescription>
                Password and security settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PasswordResetForm />
            </CardContent>
          </Card>
        </div>
      </TabsContent>
    </Tabs>
  )
}

// Profile Form Component
function ProfileForm({ user }: { user: DashboardUser }) {
  const [name, setName] = useState(user.name || '')
  const [newEmail, setNewEmail] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isEmailSubmitting, setIsEmailSubmitting] = useState(false)
  const [message, setMessage] = useState('')
  const [emailMessage, setEmailMessage] = useState('')

  // Validation functions
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const isEmailFormValid = (): boolean => {
    return newEmail.trim().length > 0 && isValidEmail(newEmail.trim()) && newEmail.trim().toLowerCase() !== user.email.toLowerCase()
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setMessage('')

    try {
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name })
      })

      if (response.ok) {
        setMessage('Profile updated successfully!')
        // Refresh the page to show updated data
        window.location.reload()
      } else {
        setMessage('Failed to update profile')
      }
    } catch {
      setMessage('An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleEmailChange = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!isEmailFormValid()) {
      setEmailMessage('Please enter a valid email address that is different from your current email')
      return
    }

    setIsEmailSubmitting(true)
    setEmailMessage('')

    try {
      const response = await fetch('/api/user/email/request', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ newEmail: newEmail.trim() })
      })

      const data = await response.json()

      if (response.ok) {
        setEmailMessage(data.message)
        setNewEmail('')
      } else {
        setEmailMessage(data.error || 'Failed to request email change')
      }
    } catch {
      setEmailMessage('An error occurred')
    } finally {
      setIsEmailSubmitting(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Display Name Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            Display Name
          </label>
          <input
            id="name"
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Enter your display name"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
          />
        </div>
        <div className="flex items-center gap-4">
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </Button>
          {message && (
            <span className={`text-sm ${message.includes('success') ? 'text-green-600' : 'text-red-600'}`}>
              {message}
            </span>
          )}
        </div>
      </form>

      {/* Email Change Form */}
      <div className="border-t pt-6">
        <form onSubmit={handleEmailChange} className="space-y-4">
          <div>
            <label htmlFor="currentEmail" className="block text-sm font-medium text-gray-700 mb-1">
              Current Email
            </label>
            <input
              id="currentEmail"
              type="email"
              value={user.email}
              disabled
              className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
            />
          </div>
          <div>
            <label htmlFor="newEmail" className="block text-sm font-medium text-gray-700 mb-1">
              New Email Address
            </label>
            <input
              id="newEmail"
              type="email"
              value={newEmail}
              onChange={(e) => {
                setNewEmail(e.target.value)
                // Clear any previous error messages when user starts typing
                if (emailMessage && emailMessage.includes('Please enter')) {
                  setEmailMessage('')
                }
              }}
              placeholder="Enter your new email address"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              required
            />
          </div>
          <div className="flex items-center gap-4">
            <Button 
              type="submit" 
              disabled={isEmailSubmitting || !isEmailFormValid()}
              className={`${!isEmailFormValid() ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isEmailSubmitting ? 'Sending...' : 'Request Email Change'}
            </Button>
            {emailMessage && (
              <span className={`text-sm ${emailMessage.includes('sent') ? 'text-green-600' : 'text-red-600'}`}>
                {emailMessage}
              </span>
            )}
          </div>
          <p className="text-xs text-gray-500">
            We&apos;ll send a confirmation email to your new address. Your current email will remain active until you confirm the change.
          </p>
        </form>
      </div>
    </div>
  )
}

// Newsletter Form Component
function NewsletterForm({ user }: { user: DashboardUser }) {
  const [isSubscribed, setIsSubscribed] = useState(user.newsletter || false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [message, setMessage] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setMessage('')

    try {
      const response = await fetch('/api/user/newsletter', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ subscribed: isSubscribed })
      })

      if (response.ok) {
        setMessage('Newsletter preferences updated!')
      } else {
        setMessage('Failed to update preferences')
      }
    } catch {
      setMessage('An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="newsletter"
          checked={isSubscribed}
          onChange={(e) => setIsSubscribed(e.target.checked)}
          className="rounded border-gray-300"
        />
        <label htmlFor="newsletter" className="text-sm font-medium text-gray-700">
          Subscribe to our daily NiceDeals newsletter
        </label>
      </div>
      <div className="flex items-center gap-4">
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Saving...' : 'Save Preferences'}
        </Button>
        {message && (
          <span className={`text-sm ${message.includes('updated') ? 'text-green-600' : 'text-red-600'}`}>
            {message}
          </span>
        )}
      </div>
    </form>
  )
}

// Password Reset Form Component
function PasswordResetForm() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [message, setMessage] = useState('')

  const handlePasswordReset = async () => {
    setIsSubmitting(true)
    setMessage('')

    try {
      // Get the current user's email from session
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: window.location.href }) // We'll get email from session on backend
      })

      if (response.ok) {
        setMessage('Password reset email sent! Check your inbox.')
      } else {
        setMessage('Failed to send reset email')
      }
    } catch {
      setMessage('An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="space-y-4">
      <p className="text-sm text-gray-600">
        Click the button below to receive a password reset email.
      </p>
      <div className="flex items-center gap-4">
        <Button 
          onClick={handlePasswordReset} 
          disabled={isSubmitting}
          variant="outline"
        >
          {isSubmitting ? 'Sending...' : 'Change Password'}
        </Button>
        {message && (
          <span className={`text-sm ${message.includes('sent') ? 'text-green-600' : 'text-red-600'}`}>
            {message}
          </span>
        )}
      </div>
    </div>
  )
} 