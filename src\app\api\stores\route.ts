import { NextResponse } from "next/server"
import { db } from "@/lib/db"

// GET /api/stores - Fetch all stores
export async function GET() {
  try {
    const stores = await db.store.findMany({
      where: { active: true },
      orderBy: { sortOrder: "asc" },
    })

    return NextResponse.json(stores)
  } catch (error) {
    console.error("Error fetching stores:", error)
    return NextResponse.json(
      { error: "Failed to fetch stores" },
      { status: 500 }
    )
  }
}
