import { redirect } from "next/navigation"
import { requireAdmin } from "@/lib/auth"
import { db } from "@/lib/db"
import AdminLayout from "@/components/admin/AdminLayout"
import { getAdminStats } from "@/lib/admin"
import StoreEditForm from "@/components/admin/StoreEditForm"

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'

async function getStore(id: string) {
  const storeId = parseInt(id, 10)
  if (isNaN(storeId)) {
    throw new Error('Invalid store ID')
  }
  
  const store = await db.store.findUnique({
    where: { id: storeId }
  })
  
  if (!store) {
    throw new Error('Store not found')
  }
  
  return {
    id: store.id,
    name: store.name,
    slug: store.slug,
    url: store.url,
    logoUrl: store.logoUrl || undefined,
    description: store.description || undefined,
    active: store.active,
    featured: store.featured,
    dealCount: store.dealCount,
    averageDiscount: store.averageDiscount,
  }
}

export default async function EditStorePage({ params }: { params: Promise<{ id: string }> }) {
  try {
    await requireAdmin()
  } catch {
    redirect("/auth/signin")
  }

  try {
    const { id } = await params
    const [stats, store] = await Promise.all([
      getAdminStats(),
      getStore(id)
    ])

    return (
      <AdminLayout stats={stats} currentPage="stores">
        <div className="w-full">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Edit Store</h2>
            <p className="text-gray-600">Update store information and settings</p>
          </div>
          <StoreEditForm store={store} />
        </div>
      </AdminLayout>
    )
  } catch {
    redirect("/admin/stores")
  }
}
