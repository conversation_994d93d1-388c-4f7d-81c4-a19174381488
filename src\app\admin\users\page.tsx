import { redirect } from "next/navigation"
import { requireAdmin } from "@/lib/auth"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import AdminLayout from "@/components/admin/AdminLayout"
import { getAdminStats } from "@/lib/admin"
import UserManagement from "@/components/admin/UserManagement"

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'

export default async function AdminUsersPage() {
  try {
    await requireAdmin()
  } catch {
    redirect("/auth/signin")
  }

  const stats = await getAdminStats()

  return (
    <AdminLayout stats={stats} currentPage="users">
      <Card>
        <CardHeader>
          <CardTitle>User Management</CardTitle>
          <CardDescription>
            View user statistics and manage user roles
          </CardDescription>
        </CardHeader>
        <CardContent>
          <UserManagement />
        </CardContent>
      </Card>
    </AdminLayout>
  )
}
