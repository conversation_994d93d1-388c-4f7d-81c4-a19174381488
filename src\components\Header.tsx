"use client"

import Link from "next/link";
import Image from "next/image";
import { useState, useRef, useEffect } from "react";
import { useSession, signOut } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { User, LogOut, Settings, ChevronDown, Heart, Package, MessageSquare, ThumbsUp, Menu, X } from "lucide-react";

export default function Header() {
  const { data: session, status, update } = useSession();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Listen for authentication events and update session
  useEffect(() => {
    const handleAuthEvent = () => {
      // Simple immediate session update
          update();
    };

    // Listen for our custom auth events and storage changes for cross-tab sync
    window.addEventListener('authStateChanged', handleAuthEvent);
    window.addEventListener('storage', (e) => {
      if (e.key?.includes('nextauth')) {
        update();
      }
    });
    
    return () => {
      window.removeEventListener('authStateChanged', handleAuthEvent);
      window.removeEventListener('storage', handleAuthEvent);
    };
  }, [update]);

  // Reset logout state when session changes
  useEffect(() => {
    if (status === "unauthenticated" && isLoggingOut) {
      const timer = setTimeout(() => setIsLoggingOut(false), 1000);
      return () => clearTimeout(timer);
    }
  }, [status, isLoggingOut]);

  // Get user initials from email
  const getUserInitial = (email: string | null | undefined) => {
    if (!email) return 'U';
    return email.charAt(0).toUpperCase();
  };

  return (
    <header style={{ backgroundColor: 'white' }} className="text-gray-800 shadow-sm">
      {/* Main Header */}
      <div className="container mx-auto flex h-16 items-center justify-between px-4">
        <div className="flex items-center gap-2">
          {/* Hamburger for mobile */}
          <button
            className="md:hidden p-2 rounded-lg hover:bg-black/5 transition-colors"
            aria-label="Open menu"
            onClick={() => setMobileMenuOpen(true)}
          >
            <Menu className="h-8 w-8 text-gray-800" />
          </button>

          <Link href="/" className="flex items-center">
            <Image
              src="/nicedeals-logo.png"
              alt="NiceDeals Logo"
              width={230}
              height={40}
              className="h-10 w-auto"
              priority
              unoptimized
            />
          </Link>
        </div>
        
        {/* Desktop nav */}
        <nav className="hidden md:flex items-center space-x-6 text-base font-medium text-gray-800">
          <Link href="/browse?sort=nice" className="hover:text-orange-600 transition-colors">
            Nice Deals
          </Link>
          <Link href="/browse" className="hover:text-orange-600 transition-colors">
            Browse Deals
          </Link>
          <Link href="/categories" className="hover:text-orange-600 transition-colors">
            Categories
          </Link>
          <Link href="/stores" className="hover:text-orange-600 transition-colors">
            Stores
          </Link>
          <Link href="/post" className="hover:text-orange-600 transition-colors">
            Post Deal
          </Link>
        </nav>
        
        <div className="flex items-center space-x-4">
          {status === "loading" || isLoggingOut ? (
            <div className="w-8 h-8 rounded-full bg-gray-300 animate-pulse" />
          ) : session?.user && !isLoggingOut ? (
            <div className="relative" ref={dropdownRef}>
              <button
                onClick={() => setDropdownOpen(!dropdownOpen)}
                className="flex items-center space-x-2 p-2 rounded-lg hover:bg-black/5 transition-colors"
              >
                <div className="w-8 h-8 rounded-full bg-orange-500 flex items-center justify-center text-white font-semibold text-sm">
                  {getUserInitial(session.user.email)}
                </div>
                <ChevronDown className={`w-4 h-4 text-gray-800 transition-transform ${dropdownOpen ? 'rotate-180' : ''}`} />
              </button>

              {dropdownOpen && (
                <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-sm border border-gray-200 py-2 z-50">
                  <div className="px-4 py-3 border-b border-gray-100">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 rounded-full bg-orange-500 flex items-center justify-center text-white font-semibold">
                        {getUserInitial(session.user.email)}
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {session.user.email}
                        </p>
                        <p className="text-xs text-gray-500">
                          Signed in
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="py-1">
                    <Link
                      href="/dashboard"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                      onClick={() => setDropdownOpen(false)}
                    >
                      <Settings className="w-4 h-4 mr-3" />
                      Dashboard
                    </Link>
                    <Link
                      href="/dashboard?tab=saved"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                      onClick={() => setDropdownOpen(false)}
                    >
                      <Heart className="w-4 h-4 mr-3" />
                      My Saved Deals
                    </Link>
                    <Link
                      href="/dashboard?tab=my-deals"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                      onClick={() => setDropdownOpen(false)}
                    >
                      <Package className="w-4 h-4 mr-3" />
                      My Deals
                    </Link>
                    <Link
                      href="/dashboard?tab=comments"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                      onClick={() => setDropdownOpen(false)}
                    >
                      <MessageSquare className="w-4 h-4 mr-3" />
                      My Comments
                    </Link>
                    
                    <Link
                      href="/dashboard?tab=my-votes"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                      onClick={() => setDropdownOpen(false)}
                    >
                      <ThumbsUp className="w-4 h-4 mr-3" />
                      My Votes
                    </Link>
                    <Link
                      href="/dashboard?tab=settings"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                      onClick={() => setDropdownOpen(false)}
                    >
                      <Settings className="w-4 h-4 mr-3" />
                      Settings
                    </Link>
                    {session.user.role === "ADMIN" && (
                      <Link
                        href="/admin"
                        className="flex items-center px-4 py-2 text-sm text-orange-600 hover:bg-orange-50 transition-colors font-medium"
                        onClick={() => setDropdownOpen(false)}
                      >
                        <User className="w-4 h-4 mr-3" />
                        Admin Panel
                      </Link>
                    )}
                    <button
                      onClick={async () => {
                        setDropdownOpen(false);
                        setIsLoggingOut(true); // Immediately hide user interface
                        try {
                          await signOut({ callbackUrl: "/", redirect: true });
                        } catch {
                          setIsLoggingOut(false); // Reset if signOut fails
                        }
                      }}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                    >
                      <LogOut className="w-4 h-4 mr-3" />
                      Sign Out
                    </button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center space-x-3">
              <Button asChild className="bg-sky-500 hover:bg-sky-600 text-white">
                <Link href="/auth/signin">
                  Sign in
                </Link>
              </Button>
              <Button asChild>
                <Link href="/auth/register">
                  Join Free
                </Link>
              </Button>
            </div>
          )}
        </div>
      </div>
      
      {/* Subheader Bar */}
      <div style={{ backgroundColor: '#f9f5ec' }} >
        <div className="container mx-auto px-4 py-2">
          <div className="text-center text-sm text-gray-700">
            Love a bargain? 
            <button 
              onClick={() => {
                document.querySelector('footer')?.scrollIntoView({ 
                  behavior: 'smooth',
                  block: 'start'
                });
              }}
              className="underline ml-1 transition-colors cursor-pointer"
            >
              Sign up for our daily NiceDeals newsletter
            </button>
            {' '}picking out the hottest deals in the UK everyday.
          </div>
        </div>
      </div>
      
      {/* Mobile sidebar overlay */}
      {mobileMenuOpen && (
        <div className="fixed inset-0 z-50 flex">
          {/* Sidebar */}
          <div className="w-64 bg-white h-full shadow-sm p-6 flex flex-col gap-6 animate-slide-in-left">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-gray-800">Menu</h2>
              <button
                className="p-2 rounded hover:bg-gray-100"
                aria-label="Close menu"
                onClick={() => setMobileMenuOpen(false)}
              >
                <X className="w-6 h-6 text-gray-700" />
              </button>
            </div>
            <Link href="/browse?sort=nice" className="text-lg text-gray-700 hover:text-orange-600" onClick={() => setMobileMenuOpen(false)}>
              Nice Deals
            </Link>
            <Link href="/browse" className="text-lg text-gray-700 hover:text-orange-600" onClick={() => setMobileMenuOpen(false)}>
              Browse Deals
            </Link>
            <Link href="/categories" className="text-lg text-gray-700 hover:text-orange-600" onClick={() => setMobileMenuOpen(false)}>
              Categories
            </Link>
            <Link href="/stores" className="text-lg text-gray-700 hover:text-orange-600" onClick={() => setMobileMenuOpen(false)}>
              Stores
            </Link>
            <Link href="/post" className="text-lg text-gray-700 hover:text-orange-600" onClick={() => setMobileMenuOpen(false)}>
              Post Deal
            </Link>
          </div>
          {/* Overlay to close */}
          <div
            className="flex-1 bg-black/30"
            onClick={() => setMobileMenuOpen(false)}
            aria-label="Close menu overlay"
          />
        </div>
      )}
    </header>
  );
}
