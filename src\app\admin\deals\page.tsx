import { redirect } from "next/navigation"
import { requireAdmin } from "@/lib/auth"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import AdminLayout from "@/components/admin/AdminLayout"
import { getAdminStats } from "@/lib/admin"
import DealModeration from "@/components/admin/DealModeration"

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'

export default async function AdminDealsPage() {
  try {
    await requireAdmin()
  } catch {
    redirect("/auth/signin")
  }

  const stats = await getAdminStats()

  return (
    <AdminLayout stats={stats} currentPage="deals">
      <Card>
        <CardHeader>
          <CardTitle>Deal Moderation</CardTitle>
          <CardDescription>
            Manage active deals, feature deals, and handle reports
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DealModeration />
        </CardContent>
      </Card>
    </AdminLayout>
  )
}
