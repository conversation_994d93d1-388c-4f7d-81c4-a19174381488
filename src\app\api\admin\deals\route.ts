import { NextRequest, NextResponse } from "next/server"
import { requireAdmin } from "@/lib/auth"
import { db } from "@/lib/db"
import { deleteImageFiles } from "@/lib/admin"
import { addAffiliateTag } from "@/lib/utils"
import { generateDealSlugWithId } from "@/lib/slug"

export async function PUT(request: NextRequest) {
  try {
    await requireAdmin();

    const { searchParams } = new URL(request.url);
    const dealId = searchParams.get('id');
    
    if (!dealId) {
      return NextResponse.json({ error: "Deal id is required" }, { status: 400 });
    }

    const dealIdNum = parseInt(dealId, 10);
    if (isNaN(dealIdNum)) {
      return NextResponse.json({ error: "Invalid deal ID" }, { status: 400 });
    }

    const requestData = await request.json();
    console.log('Raw request data:', JSON.stringify(requestData, null, 2));
    
    // Convert price fields to numbers if they exist
    const updateData: Record<string, unknown> = { ...requestData };
    if (updateData.price !== undefined && typeof updateData.price === 'string') {
      updateData.dealPrice = parseFloat(updateData.price);
      delete updateData.price;
    }
    if (updateData.originalPrice !== undefined) {
      if (updateData.originalPrice === "" || updateData.originalPrice === null) {
        updateData.originalPrice = null;
      } else if (typeof updateData.originalPrice === 'string') {
        updateData.originalPrice = parseFloat(updateData.originalPrice);
      }
    }
    if (updateData.url !== undefined && typeof updateData.url === 'string') {
      updateData.dealUrl = updateData.url;
      delete updateData.url;
    }
    if (updateData.coupon !== undefined) {
      if (updateData.coupon === "" || updateData.coupon === null) {
        updateData.couponCode = null;
      } else if (typeof updateData.coupon === 'string') {
        updateData.couponCode = updateData.coupon;
      }
      delete updateData.coupon;
    }
    
    // Handle image URL field mapping
    if (updateData.imageUrl !== undefined) {
      // Keep as is - matches schema
    }
    
    // Clean up any fields that shouldn't be in the update
    delete updateData.author;
    delete updateData._count;
    delete updateData.id; // Don't allow ID changes
    
    // Ensure coupon field is properly handled (should already be converted to couponCode above)
    if (updateData.coupon !== undefined) {
      delete updateData.coupon;
    }
    
    // Handle store and category relationships
    if (updateData.storeId) {
      const storeId = parseInt(updateData.storeId.toString(), 10);
      if (isNaN(storeId)) {
        return NextResponse.json({ error: "Invalid store ID" }, { status: 400 });
      }
      // Verify the store exists
      const store = await db.store.findUnique({
        where: { id: storeId }
      });
      if (!store) {
        return NextResponse.json({ error: "Store not found" }, { status: 400 });
      }
      // Use connect syntax for relationship
      updateData.store = { connect: { id: storeId } };
      delete updateData.storeId;
    }
    
    if (updateData.categoryId) {
      const categoryId = parseInt(updateData.categoryId.toString(), 10);
      if (isNaN(categoryId)) {
        return NextResponse.json({ error: "Invalid category ID" }, { status: 400 });
      }
      // Verify the category exists
      const category = await db.category.findUnique({
        where: { id: categoryId }
      });
      if (!category) {
        return NextResponse.json({ error: "Category not found" }, { status: 400 });
      }
      // Use connect syntax for relationship
      updateData.category = { connect: { id: categoryId } };
      delete updateData.categoryId;
    }

    // Add affiliate tag to Amazon URLs if dealUrl is being updated
    if (updateData.dealUrl && updateData.storeId) {
      updateData.dealUrl = addAffiliateTag(updateData.dealUrl as string, updateData.storeId as number);
    } else if (updateData.dealUrl && !updateData.storeId) {
      // If only URL is being updated, get the current store ID
      const currentDeal = await db.deal.findUnique({
        where: { id: dealIdNum },
        select: { storeId: true }
      });
      if (currentDeal?.storeId) {
        updateData.dealUrl = addAffiliateTag(updateData.dealUrl as string, currentDeal.storeId);
      }
    }

    // Regenerate slug if title is being updated
    if (updateData.title && typeof updateData.title === 'string') {
      // Get current deal to check if title changed
      const currentDeal = await db.deal.findUnique({
        where: { id: dealIdNum },
        select: { title: true }
      });
      
      if (currentDeal && currentDeal.title !== updateData.title) {
        // Title changed, regenerate slug with new title and deal ID
        updateData.slug = generateDealSlugWithId(updateData.title, dealIdNum);
        console.log(`Regenerating slug for deal ${dealIdNum}: "${updateData.slug}" (title changed)`);
      }
    }

    // Status is already in correct format from form
    
    // Debug logging
    console.log('Update data being sent to Prisma:', JSON.stringify(updateData, null, 2));

    const updatedDeal = await db.deal.update({
      where: { id: dealIdNum },
      data: updateData,
    });

    return NextResponse.json({ deal: updatedDeal });
  } catch (error) {
    console.error("Error updating deal:", error);
    return NextResponse.json(
      { error: "Failed to update deal" },
      { status: 500 }
    );
  }
}



export async function DELETE(request: NextRequest) {
  try {
    await requireAdmin();

    const { searchParams } = new URL(request.url);
    const dealId = searchParams.get('id');
    
    if (!dealId) {
      return NextResponse.json({ error: "Deal id is required" }, { status: 400 });
    }

    const dealIdNum = parseInt(dealId, 10);
    if (isNaN(dealIdNum)) {
      return NextResponse.json({ error: "Invalid deal ID" }, { status: 400 });
    }

    // Delete image files first
    await deleteImageFiles(dealIdNum);

    // Delete the deal (cascading deletes will handle related records)
    await db.deal.delete({
      where: { id: dealIdNum }
    });

    return NextResponse.json({ 
      success: true,
      message: "Deal and all related data deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting deal:", error);
    return NextResponse.json(
      { error: "Failed to delete deal" },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const dealId = searchParams.get('id')

    if (dealId) {
      const dealIdNum = parseInt(dealId, 10)
      
      if (isNaN(dealIdNum)) {
        return NextResponse.json({ error: "Invalid deal ID" }, { status: 400 })
      }

      // Fetch specific deal by ID
      const deal = await db.deal.findUnique({
        where: {
          id: dealIdNum,
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          store: true,
          category: true,
          _count: {
            select: {
              votes: true,
              comments: true,
              savedDeals: true,
            },
          },
        },
      })

      if (!deal) {
        return NextResponse.json({ error: "Deal not found" }, { status: 404 })
      }

      const response = NextResponse.json({ deal })
      
      // Prevent caching for admin endpoints to ensure fresh data
      response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0')
      response.headers.set('Pragma', 'no-cache')
      response.headers.set('Expires', '0')
      
      return response
    } else {
      // Fetch all active deals
      const deals = await db.deal.findMany({
        where: {
          status: "ACTIVE",
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          store: true,
          category: true,
          _count: {
            select: {
              votes: true,
              comments: true,
              savedDeals: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      })

      const response = NextResponse.json({ deals })
      
      // Prevent caching for admin endpoints to ensure fresh data
      response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0')
      response.headers.set('Pragma', 'no-cache')
      response.headers.set('Expires', '0')
      
      return response
    }
  } catch (error) {
    console.error("Error fetching deals:", error)
    return NextResponse.json(
      { error: "Failed to fetch deals" },
      { status: 500 }
    )
  }
}
