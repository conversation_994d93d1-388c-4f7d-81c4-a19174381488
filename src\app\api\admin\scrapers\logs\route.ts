import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";

export async function GET() {
  try {
    // Check authentication and admin role
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await db.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Fetch recent scraper logs
    const logs = await db.scrapeLog.findMany({
      orderBy: {
        createdAt: 'desc'
      },
      take: 20,
      select: {
        id: true,
        source: true,
        status: true,
        dealsFound: true,
        dealsAdded: true,
        dealsSkipped: true,
        error: true,
        dataFile: true,
        startedAt: true,
        completedAt: true,
        createdAt: true,
      }
    });

    return NextResponse.json({
      success: true,
      data: { logs }
    });

  } catch (error) {
    console.error('[SCRAPER LOGS API] Error:', error);
    return NextResponse.json({
      success: false,
      error: "Failed to fetch scraper logs",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
} 