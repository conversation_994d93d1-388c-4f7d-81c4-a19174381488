import { NextRequest, NextResponse } from "next/server"
import { requireAdmin } from "@/lib/auth"
import { db } from "@/lib/db"
import { emailService } from "@/lib/email"

export async function POST(request: NextRequest) {
  try {
    await requireAdmin()

    const { dealId, action, reason } = await request.json()

    if (!dealId || !action) {
      return NextResponse.json(
        { error: "Missing dealId or action" },
        { status: 400 }
      )
    }

    if (action === "reject" && !reason) {
      return NextResponse.json(
        { error: "Reason is required for rejection" },
        { status: 400 }
      )
    }

    let updateData: { status: string } = { status: "" }

    switch (action) {
      case "approve":
        updateData = { status: "ACTIVE" }
        break
      case "reject":
        updateData = { status: "REJECTED" }
        break
      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        )
    }

    const deal = await db.deal.update({
      where: { id: dealId },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    // Send email notification to user about deal status
    try {
      if (deal.author?.email) {
        const dealUrl = `${process.env.NEXT_PUBLIC_APP_URL}/deals/${deal.id}`
        
        if (action === "approve") {
          await emailService.sendDealApprovalNotification(
            deal.author.email,
            deal.title,
            dealUrl
          )
        } else if (action === "reject") {
          await emailService.sendDealRejectionNotification(
            deal.author.email,
            deal.title,
            reason
          )
        }
      }
    } catch (emailError) {
      console.error("Failed to send email notification:", emailError)
      // Don't fail the moderation if email fails, just log it
    }

    return NextResponse.json({ deal })
  } catch (error) {
    console.error("Error moderating deal:", error)
    return NextResponse.json(
      { error: "Failed to moderate deal" },
      { status: 500 }
    )
  }
}
