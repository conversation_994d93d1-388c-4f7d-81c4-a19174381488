"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Eye, EyeOff, Trash2, Flag, MessageSquare, User } from "lucide-react"
import { formatDistanceToNow } from "date-fns"

interface CommentData {
  id: string
  content: string
  status: string
  flaggedCount: number
  createdAt: string
  user: {
    id: number
    name: string
    image?: string
  }
  deal: {
    id: number
    title: string
  }
  parentComment?: {
    id: string
    content: string
  }
}

export default function CommentModeration() {
  const [comments, setComments] = useState<CommentData[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<"all" | "flagged" | "hidden">("all")
  const [searchTerm, setSearchTerm] = useState("")
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  useEffect(() => {
    fetchComments()
  }, [filter])

  async function fetchComments() {
    try {
      const response = await fetch(`/api/admin/comments?filter=${filter}`)
      if (response.ok) {
        const data = await response.json()
        setComments(data.comments)
      }
    } catch (error) {
      console.error("Error fetching comments:", error)
    } finally {
      setLoading(false)
    }
  }

  async function handleCommentAction(commentId: string, action: "hide" | "show" | "delete") {
    setActionLoading(commentId)
    try {
      const response = await fetch("/api/admin/moderate-comment", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          commentId,
          action,
        }),
      })

      if (response.ok) {
        if (action === "delete") {
          setComments(comments.filter(comment => comment.id !== commentId))
        } else {
          const newStatus = action === "hide" ? "HIDDEN" : "APPROVED"
          setComments(comments.map(comment => 
            comment.id === commentId 
              ? { ...comment, status: newStatus }
              : comment
          ))
        }
      }
    } catch (error) {
      console.error("Error moderating comment:", error)
    } finally {
      setActionLoading(null)
    }
  }

  const filteredComments = comments.filter(comment =>
    comment.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
    comment.user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    comment.deal.title.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex gap-4 items-center">
        <div className="flex gap-2">
          <Button
            size="sm"
            variant={filter === "all" ? "default" : "outline"}
            onClick={() => setFilter("all")}
          >
            All Comments
          </Button>
          <Button
            size="sm"
            variant={filter === "flagged" ? "default" : "outline"}
            onClick={() => setFilter("flagged")}
          >
            Flagged
          </Button>
          <Button
            size="sm"
            variant={filter === "hidden" ? "default" : "outline"}
            onClick={() => setFilter("hidden")}
          >
            Hidden
          </Button>
        </div>
        
        <Input
          placeholder="Search comments..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="flex-1 max-w-md"
        />
      </div>

      {/* Comments List */}
      <div className="space-y-4">
        {filteredComments.map((comment) => (
          <Card key={comment.id} className={comment.status === "HIDDEN" ? "opacity-60" : ""}>
            <CardContent className="p-4">
              <div className="space-y-3">
                {/* Comment Header */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      {comment.user.image ? (
                        <img
                          src={comment.user.image}
                          alt={comment.user.name}
                          className="w-8 h-8 rounded-full"
                        />
                      ) : (
                        <User className="w-4 h-4 text-gray-500" />
                      )}
                    </div>
                    <div>
                      <span className="font-medium text-sm">{comment.user.name}</span>
                      <span className="text-xs text-gray-500 ml-2">
                        {formatDistanceToNow(new Date(comment.createdAt))} ago
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {comment.flaggedCount > 0 && (
                      <Badge variant="destructive" className="flex items-center gap-1">
                        <Flag className="w-3 h-3" />
                        {comment.flaggedCount} flags
                      </Badge>
                    )}
                    <Badge variant={
                      comment.status === "APPROVED" ? "default" : 
                      comment.status === "HIDDEN" ? "secondary" : "outline"
                    }>
                      {comment.status}
                    </Badge>
                  </div>
                </div>

                {/* Deal Context */}
                <div className="text-sm text-gray-600">
                  <span className="flex items-center gap-1">
                    <MessageSquare className="w-3 h-3" />
                    Comment on: <span className="font-medium">{comment.deal.title}</span>
                  </span>
                </div>

                {/* Parent Comment (if reply) */}
                {comment.parentComment && (
                  <div className="bg-gray-50 p-2 rounded text-sm border-l-2 border-gray-300">
                    <span className="text-gray-500">Replying to:</span>
                    <p className="text-gray-700 mt-1 line-clamp-2">
                      {comment.parentComment.content}
                    </p>
                  </div>
                )}

                {/* Comment Content */}
                <div className="bg-white border rounded p-3">
                  <p className="text-gray-900">{comment.content}</p>
                </div>

                {/* Actions */}
                <div className="flex gap-2 pt-2">
                  {comment.status !== "HIDDEN" ? (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleCommentAction(comment.id, "hide")}
                      disabled={actionLoading === comment.id}
                    >
                      <EyeOff className="w-4 h-4 mr-1" />
                      Hide
                    </Button>
                  ) : (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleCommentAction(comment.id, "show")}
                      disabled={actionLoading === comment.id}
                    >
                      <Eye className="w-4 h-4 mr-1" />
                      Show
                    </Button>
                  )}
                  
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleCommentAction(comment.id, "delete")}
                    disabled={actionLoading === comment.id}
                  >
                    <Trash2 className="w-4 h-4 mr-1" />
                    Delete
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredComments.length === 0 && (
          <div className="text-center py-8">
            <MessageSquare className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No comments found</h3>
            <p className="text-gray-500">
              {filter === "flagged" 
                ? "No flagged comments to review." 
                : filter === "hidden"
                ? "No hidden comments."
                : "No comments match your search."
              }
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
