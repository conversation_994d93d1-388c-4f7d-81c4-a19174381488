import { redirect } from "next/navigation"
import { requireAdmin } from "@/lib/auth"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import AdminLayout from "@/components/admin/AdminLayout"
import { getAdminStats } from "@/lib/admin"
import CommentModeration from "@/components/admin/CommentModeration"

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'

export default async function AdminCommentsPage() {
  try {
    await requireAdmin()
  } catch {
    redirect("/auth/signin")
  }

  const stats = await getAdminStats()

  return (
    <AdminLayout stats={stats} currentPage="comments">
      <Card>
        <CardHeader>
          <CardTitle>Comment Moderation</CardTitle>
          <CardDescription>
            Moderate flagged comments and manage community discussions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CommentModeration />
        </CardContent>
      </Card>
    </AdminLayout>
  )
}
