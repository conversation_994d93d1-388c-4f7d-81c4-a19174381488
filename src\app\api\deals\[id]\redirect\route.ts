import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { getDealAffiliateUrl } from "@/lib/utils";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const dealId = parseInt(id, 10);

    if (isNaN(dealId)) {
      return NextResponse.json({ error: "Invalid deal ID" }, { status: 400 });
    }

    // Get the deal from database
    const deal = await db.deal.findUnique({
      where: { id: dealId },
      select: {
        dealUrl: true,
        storeId: true,
        title: true,
      },
    });

    if (!deal) {
      return NextResponse.json({ error: "Deal not found" }, { status: 404 });
    }

    // Generate affiliate URL dynamically
    const affiliateUrl = getDealAffiliateUrl({
      dealUrl: deal.dealUrl,
      storeId: deal.storeId,
    });

    // Log for debugging (remove in production)
    console.log(`[AFFILIATE REDIRECT] Deal ${dealId}: ${deal.dealUrl} -> ${affiliateUrl}`);

    // Redirect to the affiliate URL
    return NextResponse.redirect(affiliateUrl, { status: 302 });
  } catch (error) {
    console.error("Error redirecting to deal:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
