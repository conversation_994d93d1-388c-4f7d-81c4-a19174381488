#!/usr/bin/env node
/* eslint-disable */

/**
 * Migration script to update existing deal slugs to include deal ID at the end
 * This prepares the database for SEO-friendly URLs like: /deals/title-slug-105
 * 
 * Usage: node scripts/migrate-slugs.js
 */

const { PrismaClient } = require('@prisma/client');

// Load environment variables if dotenv is available
try {
  require('dotenv').config();
  console.log('📄 Environment variables loaded');
} catch {
  console.log('ℹ️  Using system environment variables');
}

// Log which database will be used
console.log(`🗄️  Database: ${process.env.DATABASE_URL || 'default from schema.prisma'}`);

// Use the same database configuration as the application
const prisma = new PrismaClient();

function generateSlug(text) {
  return text
    .toLowerCase()
    .trim()
    // Replace spaces and special characters with hyphens
    .replace(/[\s\W-]+/g, '-')
    // Remove multiple consecutive hyphens
    .replace(/-+/g, '-')
    // Remove leading/trailing hyphens
    .replace(/^-+|-+$/g, '')
    // Limit length
    .substring(0, 100);
}

function generateDealSlugWithId(title, dealId) {
  const baseSlug = generateSlug(title);
  return `${baseSlug}-${dealId}`;
}

function extractDealIdFromSlug(slug) {
  const lastHyphenIndex = slug.lastIndexOf('-');
  if (lastHyphenIndex === -1) return null;
  
  const idPart = slug.substring(lastHyphenIndex + 1);
  const id = parseInt(idPart, 10);
  
  return isNaN(id) ? null : id;
}

function isNewFormatSlug(slug) {
  const dealId = extractDealIdFromSlug(slug);
  return dealId !== null && dealId > 0;
}

async function migrateSlugs() {
  console.log('🚀 Starting slug migration...');
  
  try {
    // Get all deals with their current slugs
    const deals = await prisma.deal.findMany({
      select: {
        id: true,
        title: true,
        slug: true
      },
      orderBy: {
        id: 'asc'
      }
    });

    console.log(`📊 Found ${deals.length} deals to process`);
    
    let updatedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    const errors = [];

    for (const deal of deals) {
      try {
        // Check if slug is already in new format
        if (isNewFormatSlug(deal.slug)) {
          // Verify the ID in the slug matches the actual deal ID
          const extractedId = extractDealIdFromSlug(deal.slug);
          if (extractedId === deal.id) {
            console.log(`✅ Deal ${deal.id}: Slug already in correct format: "${deal.slug}"`);
            skippedCount++;
            continue;
          } else {
            console.log(`⚠️  Deal ${deal.id}: Slug has ID mismatch, regenerating...`);
          }
        }

        // Generate new slug with deal ID
        const newSlug = generateDealSlugWithId(deal.title, deal.id);
        
        // Check for slug conflicts
        const existingDeal = await prisma.deal.findFirst({
          where: {
            slug: newSlug,
            id: {
              not: deal.id
            }
          }
        });

        if (existingDeal) {
          console.log(`⚠️  Deal ${deal.id}: Slug conflict detected for "${newSlug}", using fallback...`);
          // Add a suffix to make it unique
          const fallbackSlug = `${newSlug}-alt`;
          
          // Update with fallback slug
          await prisma.deal.update({
            where: { id: deal.id },
            data: { slug: fallbackSlug }
          });
          
          console.log(`✅ Deal ${deal.id}: "${deal.slug}" → "${fallbackSlug}"`);
          updatedCount++;
        } else {
          // Update with new slug
          await prisma.deal.update({
            where: { id: deal.id },
            data: { slug: newSlug }
          });
          
          console.log(`✅ Deal ${deal.id}: "${deal.slug}" → "${newSlug}"`);
          updatedCount++;
        }

      } catch (error) {
        console.error(`❌ Error updating deal ${deal.id}:`, error.message);
        errors.push({ dealId: deal.id, error: error.message });
        errorCount++;
      }
    }

    console.log('\n📈 Migration Summary:');
    console.log(`   Total deals: ${deals.length}`);
    console.log(`   ✅ Updated: ${updatedCount}`);
    console.log(`   ⏭️  Skipped (already correct): ${skippedCount}`);
    console.log(`   ❌ Errors: ${errorCount}`);

    if (errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      errors.forEach(({ dealId, error }) => {
        console.log(`   Deal ${dealId}: ${error}`);
      });
    }

    console.log('\n🎉 Slug migration completed!');
    
    // Test a few random slugs to verify they work
    console.log('\n🔍 Testing updated slugs...');
    const sampleDeals = deals.slice(0, 3);
    for (const deal of sampleDeals) {
      const updatedDeal = await prisma.deal.findUnique({
        where: { id: deal.id },
        select: { slug: true }
      });
      
      const extractedId = extractDealIdFromSlug(updatedDeal.slug);
      if (extractedId === deal.id) {
        console.log(`✅ Deal ${deal.id}: Slug "${updatedDeal.slug}" → ID ${extractedId} ✓`);
      } else {
        console.log(`❌ Deal ${deal.id}: Slug "${updatedDeal.slug}" → ID ${extractedId} ✗`);
      }
    }

  } catch (error) {
    console.error('💥 Fatal error during migration:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
if (require.main === module) {
  migrateSlugs()
    .then(() => {
      console.log('✨ Migration script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateSlugs }; 