import Link from "next/link";
import { notFound } from "next/navigation";
import {
  ExternalLink,
  Clock,
  ChevronRight
} from "lucide-react";
import { getDealWithRelations } from "@/lib/db";
import { getSafeImageUrl, getLongDealUrl } from "@/lib/utils";
import { generateDealStructuredData, safeJsonLd } from "@/lib/structured-data";
import Comments from "@/components/Comments";
import DealScoreLive from "@/components/DealScoreLive";
import SaveButton from "@/components/SaveButton";
import CouponCodeCopy from "@/components/CouponCodeCopy";
import ShareButtons from "@/components/ShareButtons";
import ViewTracker from "@/components/ViewTracker";
import SetOriginalPrice from "@/components/admin/SetOriginalPrice";
import { NDHotBadgeIcon } from "@/components/icons/NiceDealsIcons";
import { extractDealIdFromSlug } from "@/lib/slug";
import { generateShareText } from "@/lib/utils";

// Use ISR for production build performance
// NOTE: Next.js only allows literal values - no env vars, imports, or functions
export const revalidate = 60

interface DealPageProps {
  params: Promise<{
    slug: string;
  }>;
}

async function getDeal(slug: string) {
  let dealId: number | null = null;
  
  // Check if this is a pure numeric ID (short format)
  if (/^\d+$/.test(slug)) {
    dealId = parseInt(slug, 10);
  } else {
    // Extract ID from slug format (long format)
    dealId = extractDealIdFromSlug(slug);
  }
  
  if (!dealId) {
    return null;
  }

  return getDealWithRelations(dealId);
}

export async function generateMetadata({ params }: DealPageProps) {
  const { slug } = await params;
  const result = await getDeal(slug);
  
  if (!result) {
    return {
      title: "Deal Not Found | NiceDeals",
    };
  }

  const { deal } = result;
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3010";
  
  // Always use the long format as canonical URL, regardless of how the page was accessed
  const canonicalUrl = getLongDealUrl(deal.title, deal.id);
  
  // Ensure image URLs are absolute
  const getAbsoluteImageUrl = (imageUrl: string | null) => {
    if (!imageUrl) return `${baseUrl}/api/placeholder/600/400`;
    if (imageUrl.startsWith('http')) return imageUrl;
    return `${baseUrl}${imageUrl}`;
  };

  // Rich description for meta tags (OpenGraph, etc.)
  const richDescription = `Grab a deal on ${deal.title}${deal.store ? ` from ${deal.store.name}` : ''} today. Visit Nicedeals and comment and vote and find other great UK deals.`;

  return {
    title: `${deal.title} - £${Number(deal.dealPrice).toFixed(2)} ${deal.discountPercentage ? `(${deal.discountPercentage}% off)` : ''} | NiceDeals`,
    description: richDescription,
    // This is the canonical version (SEO-friendly slug)
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      type: 'website',
      url: canonicalUrl,
      title: deal.title,
      description: richDescription,
      images: [getAbsoluteImageUrl(deal.imageUrl)],
      siteName: 'Nicedeals',
      locale: 'en_GB',
    },
    twitter: {
      card: 'summary',
      site: '@NicedealsApp',
      title: deal.title,
      description: richDescription,
      images: [getAbsoluteImageUrl(deal.imageUrl)],
    },
    facebook: {
      appId: '1156897059246722',
    },
    other: {
      // Open Graph price information
      'og:price:amount': String(deal.dealPrice),
      'og:price:currency': 'GBP',
    },
  };
}

export default async function DealPage({ params }: DealPageProps) {
  const { slug } = await params;
  const result = await getDeal(slug);

  if (!result) {
    notFound();
  }

  const { deal, relatedDeals } = result;

  if (!deal) {
    notFound();
  }

  const isExpiringSoon = deal.expiresAt && new Date(deal.expiresAt) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

  const { shareText, shareUrl } = generateShareText(deal);

  const timeAgo = (date: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return "Just now";
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Generate structured data for SEO
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3010";
  const structuredData = generateDealStructuredData({
    id: deal.id.toString(),
    title: deal.title,
    description: deal.description,
    dealPrice: Number(deal.dealPrice),
    originalPrice: deal.originalPrice ? Number(deal.originalPrice) : undefined,
    store: deal.store?.name || '',
    category: deal.category?.name || '',
    dealUrl: deal.dealUrl,
    imageUrl: deal.imageUrl ? (deal.imageUrl.startsWith('http') ? deal.imageUrl : `${baseUrl}${deal.imageUrl}`) : undefined,
    averageRating: deal.averageRating || undefined,
    ratingCount: deal.ratingCount || undefined,
    createdAt: deal.createdAt
  }, baseUrl);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        {/* Structured Data for SEO */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: safeJsonLd(structuredData)
          }}
        />

        {/* Track this view */}
        <ViewTracker dealId={deal.id} />
      <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-[56px]">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-6">
          <Link href="/" className="hover:text-orange-600">Home</Link>
          <ChevronRight className="w-4 h-4" />
          <Link href="/browse" className="hover:text-orange-600">Browse Deals</Link>
          <ChevronRight className="w-4 h-4" />
          <Link href={`/categories/${deal.category?.slug || ''}`} className="hover:text-orange-600">{deal.category?.name}</Link>
          <ChevronRight className="w-4 h-4" />
          <span className="text-gray-900 truncate">{deal.title}</span>
        </nav>

        {/* Deal Score Bar - Full Width */}
        <DealScoreLive
          dealId={deal.id}
          initialUpvotes={deal.upvotes}
          initialDownvotes={deal.downvotes || 0}
        />
        {/* Main Deal Card - Full Width */}
        <div className="bg-white rounded-xl shadow-sm overflow-hidden mb-8">
          <div className="grid md:grid-cols-2 gap-6 p-6">
                {/* Image */}
                <div className="relative">
                  <div className="aspect-square bg-gray-200 rounded-lg overflow-hidden">
                    <img
                      src={getSafeImageUrl(deal.imageUrl)}
                      alt={deal.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  {/* Discount Badge - Top Left */}
                  {(() => {
                    const originalPrice = deal.originalPrice ? Number(deal.originalPrice) : 0;
                    const dealPrice = Number(deal.dealPrice);
                    const discountPercentage = deal.discountPercentage || 
                      (originalPrice > 0 && originalPrice > dealPrice ? 
                        Math.round(((originalPrice - dealPrice) / originalPrice) * 100) : 0);
                    
                    return discountPercentage > 0 && (
                      <div className="absolute top-4 left-4 z-10">
                        <div className="bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded-xl animate-pulse-subtle">
                          -{discountPercentage}% OFF
                        </div>
                      </div>
                    );
                  })()}
                  
                  {/* Nice Badge - Top Right */}
                  {(() => {
                    const dealScore = deal.upvotes - (deal.downvotes || 0);
                    const isNice = dealScore >= (Number(process.env.NEXT_PUBLIC_NICE_THRESHOLD) || 5);
                    
                    return isNice && (
                      <div className="absolute top-4 right-4 z-10">
                        <div className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-xl flex items-center gap-1">
                          <NDHotBadgeIcon className="w-3 h-3 text-white" />
                          NICE
                        </div>
                      </div>
                    );
                  })()}
                  
                  {/* NEW Badge - Bottom Left */}
                  {(() => {
                    const isNew = () => {
                      if (!deal.createdAt) return false;
                      const createdAt = new Date(deal.createdAt);
                      const now = new Date();
                      const diffInHours = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
                      return diffInHours <= 24;
                    };
                    
                    return isNew() && (
                      <div className="absolute bottom-4 left-4 z-10">
                        <div className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-xl">
                          NEW
                        </div>
                      </div>
                    );
                  })()}
                  
                  {/* Featured Badge - Keep as fallback if not new */}
                  {deal.featured && !(() => {
                    const isNew = () => {
                      if (!deal.createdAt) return false;
                      const createdAt = new Date(deal.createdAt);
                      const now = new Date();
                      const diffInHours = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
                      return diffInHours <= 24;
                    };
                    return isNew();
                  })() && (
                    <div className="absolute bottom-4 left-4 bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                      Featured
                    </div>
                  )}
                  
                  {/* Expiring Soon Badge - Bottom Right */}
                  {isExpiringSoon && (
                    <div className="absolute bottom-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      Expires Soon
                    </div>
                  )}
                </div>

                {/* Deal Info */}
                <div>
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h1 className="text-4xl font-bold text-gray-800 mb-2 tracking-tighter">
                        {deal.title}
                      </h1>
                      <div className="flex items-center space-x-4 mb-4">
                        <Link 
                          href={`/categories/${deal.category?.slug || ''}`}
                          className="inline-flex items-center text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-full hover:bg-gray-200 transition-colors"
                        >
                          {deal.category?.name}
                        </Link>
                        <Link 
                          href={`/stores/${deal.store?.slug || ''}`}
                          className="inline-flex items-center text-sm bg-blue-100 text-blue-700 px-3 py-1 rounded-full hover:bg-blue-200 transition-colors"
                        >
                          {deal.store?.name}
                        </Link>
                      </div>
                    </div>
                  </div>

                  {/* Pricing */}
                  <div className="mb-6">
                    <div className="flex items-baseline space-x-3 mb-2">
                      <span className="text-4xl font-bold text-green-600">
                        £{Number(deal.dealPrice).toFixed(2)}
                      </span>
                      {deal.originalPrice && Number(deal.originalPrice) > Number(deal.dealPrice) && Number(deal.originalPrice) > 0 && (
                        <span className="text-2xl text-gray-500 line-through">
                          £{Number(deal.originalPrice).toFixed(2)}
                        </span>
                      )}
                      {!deal.originalPrice && (
                        <SetOriginalPrice
                          dealId={deal.id}
                          hasOriginalPrice={!!deal.originalPrice}
                        />
                      )}
                    </div>
                    {deal.originalPrice && Number(deal.originalPrice) > Number(deal.dealPrice) && Number(deal.originalPrice) > 0 && (
                      <div className="text-green-600 font-semibold text-lg">
                        Save £{(Number(deal.originalPrice) - Number(deal.dealPrice)).toFixed(2)} {deal.discountPercentage && `(${deal.discountPercentage}% off)`}
                      </div>
                    )}
                    
                  </div>

                  {/* Coupon Code */}
                  {deal.couponCode && (
                    <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm text-orange-700 font-medium mb-1">Coupon Code</div>
                          <code className="text-lg font-mono font-bold text-orange-800">
                            {deal.couponCode}
                          </code>
                        </div>
                        {/* Coupon copy button moved to client component */}
                        <CouponCodeCopy code={deal.couponCode} />
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex space-x-3 mb-6">
                    <Link
                      href={`/api/deals/${deal.id}/redirect`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-orange-600 hover:to-orange-700 transition-all duration-200 flex items-center justify-center shadow-sm"
                    >
                      <ExternalLink className="w-5 h-5 mr-2" />
                      <span className="hidden xl:inline">Grab This Deal</span>
                      <span className="xl:hidden">Grab Deal</span>
                    </Link>
                    <SaveButton
                      dealId={deal.id}
                      variant="button"
                    />
                  </div>

                  {/* Share Buttons */}
                  <div className="mt-4 flex flex-col items-end">
                    <ShareButtons title={shareText} url={shareUrl} />
                  </div>

                  {/* Stats */}
                  {/* The stats grid is now rendered by DealScoreLive for live updates */}
                </div>
              </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
          {/* Left Column - Description and Comments */}
          <div className="lg:col-span-2 space-y-8">
            {/* Description Section */}
            <div className="bg-white rounded-xl shadow-sm overflow-hidden mb-2">
              <div className="border-b border-gray-200">
                <div className="px-6 py-4">
                  <h3 className="text-lg font-semibold text-gray-900">Description</h3>
                </div>
              </div>
              <div className="p-6">
                <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                  {deal.description}
                </p>
                
                {/* Deal Information */}
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Deal Information</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Posted:</span>
                      <span className="font-medium">{timeAgo(deal.createdAt)}</span>
                    </div>
                    {deal.expiresAt && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Expires:</span>
                        <span className="font-medium text-red-600">{formatDate(deal.expiresAt)}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-gray-600">Views:</span>
                      <span className="font-medium">{(deal.viewCount || 0).toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Store:</span>
                      <span className="font-medium">{deal.store?.name}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Comments Section */}
            <Comments dealId={deal.id} />
          </div>

          {/* Right Column - Related Deals */}
          <div>
            {relatedDeals.length > 0 && (
              <div className="bg-white rounded-xl shadow-sm p-6 h-fit">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 pb-4 border-b border-gray-200">Related Deals</h3>
                <div className="space-y-4">
                  {relatedDeals.map((relatedDeal) => {
                    const score = relatedDeal.upvotes - (relatedDeal.downvotes || 0);
                    return (
                      <Link 
                        key={relatedDeal.id} 
                        href={`/deals/${relatedDeal.slug}`}
                        className="block group"
                      >
                        <div className="flex space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                          <div className="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                            <img
                              src={getSafeImageUrl(relatedDeal.imageUrl)}
                              alt={relatedDeal.title}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-gray-900 group-hover:text-orange-600 transition-colors line-clamp-2 text-sm">
                              {relatedDeal.title}
                            </h4>
                            <div className="flex items-center space-x-2 mt-1">
                              <span className="text-green-600 font-bold">£{relatedDeal.dealPrice.toFixed(2)}</span>
                              {relatedDeal.originalPrice && Number(relatedDeal.originalPrice) > Number(relatedDeal.dealPrice) && Number(relatedDeal.originalPrice) > 0 && (
                                <span className="text-gray-500 line-through text-sm">£{relatedDeal.originalPrice.toFixed(2)}</span>
                              )}
                            </div>
                            <div className="flex items-center text-xs text-gray-600 mt-1">
                              <NDHotBadgeIcon className="w-3 h-3 mr-1 text-orange-500" />
                              <span>{score > 0 ? '+' : ''}{score}</span>
                            </div>
                          </div>
                        </div>
                      </Link>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 