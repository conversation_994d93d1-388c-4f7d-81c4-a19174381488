/* eslint-disable */
"use client"

import * as Dialog from "@radix-ui/react-dialog"
import {
  FacebookIcon,
  TwitterShareButton,
  TwitterIcon,
  WhatsappShareButton,
  WhatsappIcon,
  TelegramShareButton,
  TelegramIcon,
  LinkedinShareButton,
  LinkedinIcon,
  RedditShareButton,
  RedditIcon,
  EmailShareButton,
  EmailIcon,
} from "next-share"
import { X, Copy } from "lucide-react"
import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { getShortDealUrl, isShortDealFormat, getLongDealUrl } from "@/lib/utils"
import { extractDealIdFromSlug } from "@/lib/slug"

interface ShareModalProps {
  /** Page URL to share. If omitted it falls back to window.location.href (client-side). */
  url?: string
  /** Title / caption for the share */
  title: string
  /** The element that opens the dialog when clicked */
  children: React.ReactNode
}

export default function ShareModal({ url, title, children }: ShareModalProps) {
  const { data: session } = useSession()
  const [shareUrl, setShareUrl] = useState(url || "")
  const [facebookUrl, setFacebookUrl] = useState(url || "")
  const [copySuccess, setCopySuccess] = useState(false)

  // Set up URLs for sharing - short for most platforms, long SEO-friendly for Facebook
  useEffect(() => {
    const setupUrls = (baseUrl: string, pathname: string) => {
      const dealMatch = pathname.match(/^\/deals\/(.+)$/);
      if (dealMatch) {
        const dealParam = dealMatch[1];
        let dealId: number | null = null;
        let dealTitle = "";

        if (isShortDealFormat(dealParam)) {
          // Short format - we have the ID but need title for Facebook long URL
          dealId = parseInt(dealParam, 10);
          setShareUrl(getShortDealUrl(dealId));
          // For Facebook, we need the title to generate the long URL
          // Extract just the deal title from the share text (remove "Nicedeals UK presents" prefix)
          const cleanTitle = title.replace(/^Nicedeals UK presents\s+/, '').split(' on ')[0];
          setFacebookUrl(getLongDealUrl(cleanTitle, dealId));
          return;
        } else {
          // Long format - extract ID and title from slug
          dealId = extractDealIdFromSlug(dealParam);
          if (dealId) {
            // Extract title from slug (remove ID at end)
            const lastHyphenIndex = dealParam.lastIndexOf('-');
            dealTitle = lastHyphenIndex > 0 ? dealParam.substring(0, lastHyphenIndex) : dealParam;

            setShareUrl(getShortDealUrl(dealId));
            setFacebookUrl(getLongDealUrl(dealTitle, dealId));
            return;
          }
        }
      }

      // Fallback - use the same URL for both
      setShareUrl(baseUrl);
      setFacebookUrl(baseUrl);
    };

    if (!url && typeof window !== "undefined") {
      const currentUrl = window.location.href;
      const pathname = window.location.pathname;
      setupUrls(currentUrl, pathname);
    } else if (url) {
      try {
        const urlObj = new URL(url);
        setupUrls(url, urlObj.pathname);
      } catch {
        // Invalid URL, use as-is
        setShareUrl(url);
        setFacebookUrl(url);
      }
    }
  }, [url, title])

  // Copy to clipboard function
  const handleCopyPromoText = async () => {
    try {
      await navigator.clipboard.writeText(title)
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (err) {
      console.error('Failed to copy text: ', err)
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = title
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      setCopySuccess(true)
      setTimeout(() => setCopySuccess(false), 2000)
    }
  }

  const iconSize = 48
  const isAdmin = session?.user?.id === "1"

  return (
    <Dialog.Root>
      <Dialog.Trigger asChild>{children}</Dialog.Trigger>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50" />
        <Dialog.Content className="fixed left-1/2 top-1/2 z-[51] w-[90vw] max-w-md -translate-x-1/2 -translate-y-1/2 rounded-xl bg-white p-6 shadow-sm focus:outline-none">
          <div className="flex items-center justify-between mb-4">
            <Dialog.Title className="text-lg font-semibold text-gray-900">
              Share this deal
            </Dialog.Title>
            <Dialog.Close asChild>
              <button aria-label="Close" className="text-gray-500 hover:text-gray-700">
                <X className="w-5 h-5" />
              </button>
            </Dialog.Close>
          </div>

          {/* Share buttons grid */}
          <div className="grid grid-cols-3 gap-4 place-items-center">
            <button
              onClick={() => {
                const shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(facebookUrl)}`;
                window.open(shareUrl, 'facebook-share', 'width=580,height=296');
              }}
              className="flex items-center justify-center"
            >
              <FacebookIcon size={iconSize} round />
            </button>
            <TwitterShareButton url={shareUrl} title={title}>
              <TwitterIcon size={iconSize} round />
            </TwitterShareButton>
            <WhatsappShareButton url={shareUrl} title={title} separator=" – ">
              <WhatsappIcon size={iconSize} round />
            </WhatsappShareButton>
            <TelegramShareButton url={shareUrl} title={title}>
              <TelegramIcon size={iconSize} round />
            </TelegramShareButton>
            <LinkedinShareButton url={shareUrl} title={title} summary={title} source="NiceDeals">
              <LinkedinIcon size={iconSize} round />
            </LinkedinShareButton>
            <RedditShareButton url={shareUrl} title={title}>
              <RedditIcon size={iconSize} round />
            </RedditShareButton>
            <EmailShareButton url={shareUrl} subject={title} body={title + " – " + shareUrl}>
              <EmailIcon size={iconSize} round />
            </EmailShareButton>
          </div>

          {/* Admin Copy Button */}
          {isAdmin && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <button
                onClick={handleCopyPromoText}
                className={`w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-medium transition-colors ${
                  copySuccess 
                    ? 'bg-green-100 text-green-700 border border-green-200' 
                    : 'bg-blue-100 text-blue-700 border border-blue-200 hover:bg-blue-200'
                }`}
              >
                <Copy className="w-4 h-4" />
                <span>{copySuccess ? 'Copied!' : 'Copy Promo Text (Admin)'}</span>
              </button>
              <p className="text-xs text-gray-500 mt-2 text-center">
                Copy this text to paste into Facebook posts manually
              </p>
            </div>
          )}

          {/* Native share fallback */}
          {typeof navigator !== "undefined" && (navigator as any).share && (
            <button
              onClick={() => (navigator as any).share({ title, url: shareUrl })}
              className="mt-6 w-full rounded-lg bg-orange-600 py-2 text-center font-medium text-white hover:bg-orange-700 transition-colors"
            >
              More… (device share)
            </button>
          )}
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  )
} 