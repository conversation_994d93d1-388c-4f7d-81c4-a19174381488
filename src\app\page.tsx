import Link from "next/link";
import { Heart, TrendingUp, Users, ThumbsUpIcon, Clock } from "lucide-react";
import { NDFireIcon } from "@/components/icons/NiceDealsIcons";
import { db } from "@/lib/db";
import { formatDate, getSafeImageUrl } from "@/lib/utils";
import HeroCarousel from "@/components/HeroCarousel";
import SaveButton from "@/components/SaveButton";
import DealCard from "@/components/DealCard";
import { Decimal } from "@prisma/client/runtime/library";
import type { Metadata } from "next";

// ISR for both dev and production (Next.js limitation)
export const revalidate = 60

async function getHomePageData() {
  const [
    niceDeals,
    recentDeals,
    totalDeals,
    totalUsers,
    totalVotes
  ] = await Promise.all([
    // Nice deals (most recent that crossed NICE_THRESHOLD)
    db.deal.findMany({
      where: {
        status: "ACTIVE",
        niceAt: { not: null }
      },
      take: 12,
      orderBy: { niceAt: "desc" },
      select: {
        id: true,
        slug: true,
        title: true,
        description: true,
        originalPrice: true,
        dealPrice: true,
        discountPercentage: true,
        dealUrl: true,
        imageUrl: true,
        upvotes: true,
        downvotes: true,
        couponCode: true,
        expiresAt: true,
        createdAt: true,
        featured: true,
        store: { select: { id: true, name: true, slug: true } },
        category: { select: { id: true, name: true, slug: true } }
      }
    }),
    // Recent deals
    db.deal.findMany({
      where: { status: "ACTIVE" },
      take: 5,
      orderBy: { createdAt: "desc" },
      select: {
        id: true,
        slug: true,
        title: true,
        dealPrice: true,
        originalPrice: true,
        discountPercentage: true,
        imageUrl: true,
        thumbnailUrl: true,
        upvotes: true,
        createdAt: true,
        author: { select: { name: true, image: true } },
        store: { select: { name: true } },
        category: { select: { name: true } }
      }
    }),
    // Stats
    db.deal.count({ where: { status: "ACTIVE" } }),
    db.user.count(),
    db.vote.count()
  ]);

  return {
    niceDeals,
    recentDeals,
    stats: [
      { label: "Active Deals", value: totalDeals.toLocaleString(), icon: TrendingUp },
      { label: "Community Votes", value: totalVotes.toLocaleString(), icon: Heart },
      { label: "Registered Users", value: totalUsers.toLocaleString(), icon: Users },
    ]
  };
}

// Transform homepage deal data to match DealCard component interface
function transformDealForCard(deal: {
  id: number;
  slug: string;
  title: string;
  description?: string | null;
  originalPrice?: Decimal | null;
  dealPrice: Decimal;
  discountPercentage?: number | null;
  dealUrl?: string | null;
  imageUrl?: string | null;
  upvotes: number;
  downvotes?: number | null;
  couponCode?: string | null;
  expiresAt?: Date | null;
  createdAt: Date;
  store: { id: number; name: string; slug: string };
  category: { id: number; name: string; slug: string };
}) {
  const originalPrice = deal.originalPrice ? Number(deal.originalPrice) : null;
  const dealPrice = Number(deal.dealPrice);
  
  // Calculate discount percentage only if we have valid prices
  let discountPercentage = 0;
  if (originalPrice && originalPrice > 0 && originalPrice > dealPrice) {
    // Use provided discount percentage or calculate it
    discountPercentage = deal.discountPercentage || Math.round(((originalPrice - dealPrice) / originalPrice) * 100);
  }
  
  // Calculate isNew server-side to avoid hydration mismatch
  const isNew = () => {
    if (!deal.createdAt) return false;
    const createdAt = new Date(deal.createdAt);
    const now = new Date();
    const diffInHours = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
    return diffInHours <= 24;
  };
  
  return {
    id: deal.id,
    slug: deal.slug,
    title: deal.title,
    description: deal.description || "",
    originalPrice: originalPrice, // Keep as null if no original price
    dealPrice: dealPrice,
    discount: discountPercentage, // Will be 0 if no valid original price
    store: {
      id: deal.store.id,
      name: deal.store.name,
      slug: deal.store.slug
    },
    category: {
      id: deal.category.id,
      name: deal.category.name,
      slug: deal.category.slug
    },
    dealUrl: deal.dealUrl || "#",
    imageUrl: deal.imageUrl || "/placeholder.svg",
    upvotes: deal.upvotes || 0,
    downvotes: deal.downvotes || 0,
    couponCode: deal.couponCode || undefined,
    expiresAt: deal.expiresAt?.toISOString() || undefined,
    createdDate: deal.createdAt?.toISOString() || new Date().toISOString(),
    isNew: isNew() // Add server-side calculated isNew flag
  };
}

export default async function Home() {
  const { niceDeals, recentDeals, stats } = await getHomePageData();
  
  // Environment variables to control homepage sections
  const showStats = process.env.SHOW_HOMEPAGE_STATS === "Y";
  
  return (
    <div className="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
      {/* Hero Carousel */}
      <HeroCarousel />

      {/* Statistics Section */}
      {showStats && (
        <section className="py-16 ">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-3 gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-orange-100 rounded-full mb-4">
                    <stat.icon className="w-8 h-8 text-orange-600" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}



      {/* Nice Deals Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-left mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-gray-900 mb-4">
              <div className="flex items-center gap-2">
                <div className="bg-gradient-to-r from-deal-orange to-deal-orange-dark p-2 rounded-lg">
                  <NDFireIcon className="w-6 h-6 text-white" />
                </div>
                <span>Nice Deals</span>
              </div>
            </h2>
            <p className="text-l text-gray-600">
              The most recent deals that have been voted Nice!
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {niceDeals.map((deal) => {
              return <DealCard key={deal.id} deal={transformDealForCard(deal)} />
            })}
          </div>
          
          <div className="text-center mt-8">
            <Link
              href="/browse?sort=nice"
              className="bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-700 transition-colors inline-block"
            >
              View All Nice Deals
            </Link>
          </div>
        </div>
      </section>

      {/* Recent Deals Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-left mb-12">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-gray-900 mb-4">
              <div className="flex items-center gap-2">
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-2 rounded-lg">
                  <Clock className="w-6 h-6 text-white" />
                </div>
                <span>Latest Deals</span>
              </div>
            </h2>
            <p className="text-xl text-gray-600">
              Fresh deals just posted by our community
            </p>
          </div>
          
          <div className="space-y-4">
            {recentDeals.map((deal) => (
              <Link key={deal.id} href={`/deals/${deal.slug}`} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-sm transition-shadow block">
                <div className="flex flex-col md:flex-row md:items-center justify-between">
                  <div className="w-full md:max-w-[100px] max-h-[100px] mb-4 md:mb-0 md:mr-6 flex-shrink-0 flex items-center justify-center">
                    <img 
                      src={getSafeImageUrl(deal.thumbnailUrl || deal.imageUrl)} 
                      alt={deal.title}
                      className="max-w-[100px] max-h-[100px] w-auto h-auto object-contain"
                    />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-semibold text-md text-gray-900 flex-1 mr-4">
                        {deal.title}
                      </h3>
                      <div className="flex flex-col space-y-1 mr-4">
                        <span className="text-sm text-gray-600 bg-orange-100 px-2 py-1 rounded">{deal.category.name}</span>
                        <span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">{deal.store.name}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center">
                        <span className="text-lg font-bold text-green-600">£{deal.dealPrice.toFixed(2)}</span>
                        {deal.originalPrice && Number(deal.originalPrice) > 0 && (
                          <span className="text-gray-500 line-through ml-2">£{deal.originalPrice.toFixed(2)}</span>
                        )}
                        {(() => {
                          const originalPrice = deal.originalPrice ? Number(deal.originalPrice) : 0;
                          const dealPrice = Number(deal.dealPrice);
                          const discountPercentage = deal.discountPercentage || 
                            (originalPrice > 0 && originalPrice > dealPrice ? 
                              Math.round(((originalPrice - dealPrice) / originalPrice) * 100) : 0);
                          
                          return discountPercentage > 0 && (
                            <span className="text-green-600 font-medium ml-2">-{discountPercentage}%</span>
                          );
                        })()}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4 mt-4 md:mt-0">
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <div className="flex items-center">
                        <ThumbsUpIcon className="w-4 h-4 text-green-500 mr-1" />
                        <span>{deal.upvotes}</span>
                      </div>
                      <span>{formatDate(deal.createdAt)}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <SaveButton dealId={deal.id} />
                      <button className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-6 py-2 rounded-md hover:from-orange-600 hover:to-orange-700 transition-all duration-200 flex items-center gap-2 shadow-sm">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                        View Deal
                      </button>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
          
          <div className="text-center mt-8">
            <Link
              href="/browse?sort=recent"
              className="border border-orange-600 text-orange-600 px-8 py-3 rounded-lg font-semibold hover:bg-orange-50 transition-colors inline-block"
            >
              View All Recent Deals
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}

export const metadata: Metadata = {
  title: "NiceDeals | Best Community-Powered UK Deals",
  description: "Discover and share the hottest UK bargains, voucher codes and money-saving tips.",
  openGraph: {
    title: "NiceDeals | Best UK Deals",
    description: "Community-driven platform for finding and sharing the best bargains and discounts in the UK.",
    images: ["/nicedeals-logo.png"],
  },
  twitter: {
    card: "summary",
    title: "NiceDeals | Best UK Deals",
    description: "Find and share the hottest UK bargains and voucher codes.",
    images: ["/nicedeals-logo.png"],
  },
};
