"use client"

import Link from "next/link";
import { MessageSquare } from "lucide-react";
import VoteButtons from "./VoteButtons";
import SaveButton from "./SaveButton";
import { getSafeImageUrl } from "@/lib/utils";
import { NDHotBadgeIcon } from "@/components/icons/NiceDealsIcons";

interface Deal {
  id: number;
  slug: string;
  title: string;
  description: string;
  originalPrice: number | null;
  dealPrice: number;
  discount: number;
  store?: {
    id: number;
    name: string;
    slug: string;
  };
  category?: {
    id: number;
    name: string;
    slug: string;
  };
  dealUrl: string;
  imageUrl: string;
  upvotes: number;
  downvotes: number;
  couponCode?: string;
  expiresAt?: string;
  createdDate: string;
  isNew?: boolean; // Add optional isNew prop
}

interface DealCardProps {
  deal: Deal;
}

export default function DealCard({ deal }: DealCardProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', { 
      day: 'numeric', 
      month: 'short', 
      year: 'numeric' 
    });
  };

  // Calculate deal score (upvotes - downvotes)
  const dealScore = deal.upvotes - deal.downvotes;
  const isNice = dealScore >= (Number(process.env.NEXT_PUBLIC_NICE_THRESHOLD) || 5); // Use env variable instead of hardcoded 10

  // For now, let's assume comment count is 0 - this should come from the deal data
  const commentCount = 0;

  // Use server-provided isNew flag or fallback to client-side calculation for backward compatibility
  const isNew = deal.isNew !== undefined ? deal.isNew : (() => {
    if (!deal.createdDate) return false;
    const createdAt = new Date(deal.createdDate);
    const now = new Date();
    const diffInHours = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
    return diffInHours <= 24;
  })();

  // Both layouts now use the same v1-style card design
  return (
    <div className="animate-slide-up rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1 relative">
      {/* Image Section */}
      <div className="relative h-44 bg-gray-100 overflow-hidden">
        <Link href={`/deals/${deal.slug}`}>
          <img 
            src={getSafeImageUrl(deal.imageUrl)} 
            alt={deal.title} 
            className="w-full h-full object-contain hover:scale-105 transition-transform duration-300"
          />
        </Link>
        
        {/* Discount Badge - Top Left */}
        {deal.discount && deal.discount > 0 && (
          <div className="absolute top-3 left-3 z-10">
            <div className="bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded-xl animate-pulse-subtle">
              {deal.discount}% OFF
            </div>
          </div>
        )}
        
        {/* Nice Badge - Top Right */}
        {isNice && (
          <div className="absolute top-3 right-3 z-10">
            <div className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-xl flex items-center gap-1">
              <NDHotBadgeIcon className="w-3 h-3 text-white" />
              NICE
            </div>
          </div>
        )}
        
        {/* NEW Badge - Bottom Left */}
        {isNew && (
          <div className="absolute bottom-3 left-3 z-10">
            <div className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-xl">
              NEW
            </div>
          </div>
        )}
        
        {/* Save Button - Bottom Right */}
        <div className="absolute bottom-3 right-3">
          <SaveButton dealId={deal.id} />
        </div>
      </div>

      {/* Content Section */}
      <div className="p-3">
        {/* Category and Date Row */}
        <div className="flex gap-1.5 mb-1.5 text-xs">
          {deal.category && (
            <Link
              href={`/categories/${deal.category.slug}`}
              className="text-blue-600 bg-blue-100 px-2 py-0.5 rounded-sm hover:bg-blue-200 transition-colors"
            >
              {deal.category.name}
            </Link>
          )}
          <span className="text-gray-500 px-2 py-0.5">
            {formatDate(deal.createdDate)}
          </span>
        </div>

        {/* Title */}
        <Link className="block mb-2" href={`/deals/${deal.slug}`}>
          <h2 className="font-medium text-gray-900 line-clamp-2 hover:text-orange-500 transition-colors duration-200 h-12 flex items-start">
            {deal.title}
          </h2>
        </Link>

        {/* Store */}
        {deal.store && (
          <div className="text-sm text-gray-500 mb-2">
            from{' '}
            <Link 
              href={`/stores/${deal.store.slug}`}
              className="text-orange-500 hover:underline"
            >
              {deal.store.name}
            </Link>
          </div>
        )}

        {/* Price Section with Vote Buttons */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-baseline">
            <span className="text-orange-500 text-xl font-bold mr-2">
              £{Number(deal.dealPrice).toFixed(2)}
            </span>
            {deal.originalPrice && Number(deal.originalPrice) > Number(deal.dealPrice) && Number(deal.originalPrice) > 0 && (
              <span className="text-gray-400 text-sm line-through">
                £{Number(deal.originalPrice).toFixed(2)}
              </span>
            )}
          </div>
          
          {/* Vote Buttons - Right aligned */}
          <div className="ml-auto">
            <VoteButtons 
              dealId={deal.id}
              initialUpvotes={deal.upvotes}
              initialDownvotes={deal.downvotes}
              variant="orange-bar"
              className="flex-row space-y-0 space-x-1 scale-75"
              showScore={false}
            />
          </div>
        </div>

        {/* Bottom Section */}
        <div className="flex items-center justify-between text-sm border-t border-gray-100 pt-2">
          {/* Left: Deal Score and Comment Count */}
          <div className="flex items-center gap-2">
            {/* Deal Score with Fire Icon */}
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="h-4 w-4 text-yellow-500">
                <path strokeLinecap="round" strokeLinejoin="round" d="M15.362 5.214A8.252 8.252 0 0 1 12 21 8.25 8.25 0 0 1 6.038 7.047 8.287 8.287 0 0 0 9 9.601a8.983 8.983 0 0 1 3.361-6.867 8.21 8.21 0 0 0 3 2.48Z" />
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 18a3.75 3.75 0 0 0 .495-7.468 5.99 5.99 0 0 0-1.925 3.547 5.975 5.975 0 0 1-2.133-1.001A3.75 3.75 0 0 0 12 18Z" />
              </svg>
              <span className="font-medium text-yellow-500 ml-1">{dealScore}°</span>
            </div>
            
            {/* Comment Count */}
            <div className="flex items-center text-gray-500">
              <MessageSquare className="h-4 w-4 mr-1" />
              <span>{commentCount}</span>
            </div>
          </div>

          {/* Right: View Deal Link */}
          <div className="flex items-center">
            <Link 
              className="text-orange-500 hover:underline text-sm font-medium flex items-center" 
              href={`/deals/${deal.slug}`}
            >
              View Deal
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="h-3.5 w-3.5 ml-1">
                <path strokeLinecap="round" strokeLinejoin="round" d="M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244" />
              </svg>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
