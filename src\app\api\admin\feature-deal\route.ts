import { NextRequest, NextResponse } from "next/server"
import { requireAdmin } from "@/lib/auth"
import { db } from "@/lib/db"

export async function POST(request: NextRequest) {
  try {
    await requireAdmin()

    const { dealId, featured } = await request.json()

    const deal = await db.deal.update({
      where: { id: dealId },
      data: { featured },
    })

    return NextResponse.json({ deal })
  } catch (error) {
    console.error("Error updating deal feature status:", error)
    return NextResponse.json(
      { error: "Failed to update deal" },
      { status: 500 }
    )
  }
}
