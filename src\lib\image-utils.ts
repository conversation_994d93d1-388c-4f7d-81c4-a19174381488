import { NextRequest } from 'next/server'

export interface ImageUploadResult {
  success: boolean
  url?: string
  thumbnailUrl?: string
  error?: string
}

export interface ProcessedImage {
  originalUrl: string
  thumbnailUrl: string
  width: number
  height: number
  size: number
}

/**
 * Validates image file type and size
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
  const maxSize = 5 * 1024 * 1024 // 5MB

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Invalid file type. Only JPEG, PNG, and WebP are allowed.'
    }
  }

  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'File size too large. Maximum size is 5MB.'
    }
  }

  return { valid: true }
}

/**
 * Validates image URL
 */
export async function validateImageUrl(url: string): Promise<{ valid: boolean; error?: string }> {
  try {
    const response = await fetch(url, { method: 'HEAD' })
    
    if (!response.ok) {
      return { valid: false, error: 'URL is not accessible' }
    }

    const contentType = response.headers.get('content-type')
    if (!contentType || !contentType.startsWith('image/')) {
      return { valid: false, error: 'URL does not point to an image' }
    }

    const contentLength = response.headers.get('content-length')
    if (contentLength && parseInt(contentLength) > 5 * 1024 * 1024) {
      return { valid: false, error: 'Image is too large (max 5MB)' }
    }

    return { valid: true }
  } catch (error) {
    return { valid: false, error: 'Failed to validate URL' }
  }
}

/**
 * Generates a unique filename for uploaded images
 */
export function generateImageFilename(originalName: string, dealId: string, type: 'main' | 'thumbnail' | 'gallery' = 'main'): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  const extension = originalName.split('.').pop()?.toLowerCase() || 'jpg'
  
  return `deals/${dealId}/${type}_${timestamp}_${random}.${extension}`
}

/**
 * Creates a thumbnail URL based on the main image URL
 * This is a simple approach - in production you'd want actual image resizing
 */
export function generateThumbnailUrl(mainImageUrl: string): string {
  if (mainImageUrl.includes('/deals/')) {
    return mainImageUrl.replace(/\.(jpg|jpeg|png|webp)$/, '_thumb.$1')
  }
  return mainImageUrl
}

/**
 * Extracts image dimensions from a file (requires canvas API in browser)
 */
export function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    img.onload = () => {
      resolve({ width: img.naturalWidth, height: img.naturalHeight })
    }
    
    img.onerror = () => {
      reject(new Error('Failed to load image'))
    }
    
    img.src = URL.createObjectURL(file)
  })
}

/**
 * Processes multiple image uploads from FormData
 */
export async function processImageUploads(formData: FormData): Promise<{
  mainImage?: ProcessedImage
  galleryImages: ProcessedImage[]
  errors: string[]
}> {
  const errors: string[] = []
  const galleryImages: ProcessedImage[] = []
  let mainImage: ProcessedImage | undefined

  // Process main image
  const mainImageFile = formData.get('mainImage') as File
  if (mainImageFile && mainImageFile.size > 0) {
    const validation = validateImageFile(mainImageFile)
    if (!validation.valid) {
      errors.push(`Main image: ${validation.error}`)
    } else {
      // In a real implementation, you'd upload to cloud storage here
      const url = `/api/images/placeholder` // Placeholder for now
      mainImage = {
        originalUrl: url,
        thumbnailUrl: generateThumbnailUrl(url),
        width: 800, // Would get from actual image
        height: 600, // Would get from actual image
        size: mainImageFile.size
      }
    }
  }

  // Process gallery images
  const galleryFiles = formData.getAll('galleryImages') as File[]
  for (let i = 0; i < galleryFiles.length; i++) {
    const file = galleryFiles[i]
    if (file && file.size > 0) {
      const validation = validateImageFile(file)
      if (!validation.valid) {
        errors.push(`Gallery image ${i + 1}: ${validation.error}`)
      } else {
        const url = `/api/images/placeholder` // Placeholder for now
        galleryImages.push({
          originalUrl: url,
          thumbnailUrl: generateThumbnailUrl(url),
          width: 800, // Would get from actual image
          height: 600, // Would get from actual image
          size: file.size
        })
      }
    }
  }

  return { mainImage, galleryImages, errors }
}

/**
 * Processes image URL submissions
 */
export async function processImageUrls(urls: string[]): Promise<{
  validUrls: string[]
  errors: string[]
}> {
  const validUrls: string[] = []
  const errors: string[] = []

  for (const url of urls) {
    if (url.trim()) {
      const validation = await validateImageUrl(url.trim())
      if (validation.valid) {
        validUrls.push(url.trim())
      } else {
        errors.push(`Invalid URL "${url}": ${validation.error}`)
      }
    }
  }

  return { validUrls, errors }
}

/**
 * Downloads a remote image, saves as .webp, resizes for main and thumbnail, and returns URLs.
 * Mirrors v1 localizeRemoteImage logic.
 */
import path from 'path'
import fs from 'fs/promises'
import sharp from 'sharp'
import fetch from 'node-fetch'
import { v4 as uuidv4 } from 'uuid'

// Configure Sharp for better memory management
sharp.cache({ memory: 50 }); // Limit cache to 50MB
sharp.concurrency(1); // Process one image at a time to reduce memory usage

export async function localiseRemoteImage(imageUrl: string): Promise<{ imageUrl: string, thumbnailUrl: string }> {
  try {
    if (!imageUrl || typeof imageUrl !== 'string' || !imageUrl.startsWith('http')) {
      return { imageUrl, thumbnailUrl: imageUrl }
    }

    // Download image with timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000)
    
    const response = await fetch(imageUrl, { 
      signal: controller.signal,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Fetch-Dest': 'image',
        'Sec-Fetch-Mode': 'no-cors',
        'Sec-Fetch-Site': 'cross-site',
        'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"'
      }
    })
    clearTimeout(timeoutId)
    if (!response.ok) throw new Error('Failed to download image')
    const buffer = Buffer.from(await response.arrayBuffer())

    // Prepare directories
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'deals')
    const thumbnailsDir = path.join(uploadsDir, 'thumbnails')
    await fs.mkdir(uploadsDir, { recursive: true })
    await fs.mkdir(thumbnailsDir, { recursive: true })

    // Unique filename
    const imageId = uuidv4()
    const fileName = `${imageId}.webp`
    const filePath = path.join(uploadsDir, fileName)
    const thumbnailPath = path.join(thumbnailsDir, fileName)

    // Save main image (800x800 max) with memory management
    const mainProcessor = sharp(buffer, { limitInputPixels: 268402689 }) // ~16k x 16k max
      .resize(800, 800, { fit: 'inside', withoutEnlargement: true })
      .webp({ quality: 80, effort: 1 }) // Lower effort for faster processing

    await mainProcessor.toFile(filePath)
    mainProcessor.destroy() // Clean up resources

    // Save thumbnail (200x200 max) with memory management
    const thumbProcessor = sharp(buffer, { limitInputPixels: 268402689 })
      .resize(200, 200, { fit: 'inside', withoutEnlargement: true })
      .webp({ quality: 70, effort: 1 })

    await thumbProcessor.toFile(thumbnailPath)
    thumbProcessor.destroy() // Clean up resources

    // Return URLs
    return {
      imageUrl: `/uploads/deals/${fileName}`,
      thumbnailUrl: `/uploads/deals/thumbnails/${fileName}`
    }
  } catch (error) {
    return { imageUrl, thumbnailUrl: imageUrl }
  }
}
