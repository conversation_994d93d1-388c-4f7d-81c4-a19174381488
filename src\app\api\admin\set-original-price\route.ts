import { NextRequest, NextResponse } from "next/server"
import { requireAdmin } from "@/lib/auth"
import { db } from "@/lib/db"

export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    await requireAdmin()
    
    const { dealId, originalPrice } = await request.json()
    
    if (!dealId || originalPrice === undefined) {
      return NextResponse.json(
        { error: "Missing dealId or originalPrice" },
        { status: 400 }
      )
    }
    
    const dealIdInt = parseInt(dealId, 10)
    if (isNaN(dealIdInt)) {
      return NextResponse.json(
        { error: "Invalid dealId" },
        { status: 400 }
      )
    }
    
    const originalPriceFloat = parseFloat(originalPrice)
    if (isNaN(originalPriceFloat) || originalPriceFloat < 0) {
      return NextResponse.json(
        { error: "Invalid originalPrice" },
        { status: 400 }
      )
    }
    
    // Check if deal exists
    const deal = await db.deal.findUnique({
      where: { id: dealIdInt },
      select: { id: true, dealPrice: true }
    })
    
    if (!deal) {
      return NextResponse.json(
        { error: "Deal not found" },
        { status: 404 }
      )
    }
    
    // Calculate discount percentage
    const dealPrice = Number(deal.dealPrice)
    let discountPercentage = null
    if (originalPriceFloat > dealPrice) {
      discountPercentage = Math.round(((originalPriceFloat - dealPrice) / originalPriceFloat) * 100)
    }
    
    // Update the deal
    await db.deal.update({
      where: { id: dealIdInt },
      data: {
        originalPrice: originalPriceFloat,
        discountPercentage: discountPercentage
      }
    })
    
    return NextResponse.json({
      success: true,
      originalPrice: originalPriceFloat,
      discountPercentage: discountPercentage
    })
    
  } catch (error) {
    console.error("Error setting original price:", error)
    return NextResponse.json(
      { error: "Failed to set original price" },
      { status: 500 }
    )
  }
} 