import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { generateDealSlugWithId } from "@/lib/slug";
import { Decimal } from "@prisma/client/runtime/library";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date | string) {
  const d = new Date(date)
  return d.toLocaleDateString('en-GB', {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  })
}

/**
 * Returns realistic browser headers for HTTP requests to appear more human-like
 */
export function getRealisticHeaders(): Record<string, string> {
  return {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.9',
    'Accept-Encoding': 'gzip, deflate, br',
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache',
    'Sec-Fetch-Dest': 'image',
    'Sec-Fetch-Mode': 'no-cors',
    'Sec-Fetch-Site': 'cross-site',
    'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
    'Sec-Ch-Ua-Mobile': '?0',
    'Sec-Ch-Ua-Platform': '"Windows"'
  }
}

/**
 * Returns a safe image URL for display. Only shows images that are local (/uploads/).
 * Remote images are replaced with a placeholder.
 */
export function getSafeImageUrl(imageUrl?: string | null): string {
  // If no image URL provided, return placeholder
  if (!imageUrl) {
    return "/placeholder.svg"
  }
  
  // Only allow local images that start with /uploads/
  if (imageUrl.startsWith("/uploads/")) {
    return imageUrl
  }
  
  // All other images (remote URLs) get replaced with placeholder
  return "/placeholder.svg"
}

/**
 * Adds affiliate tags to Amazon and eBay URLs if the store matches
 * @param url - The deal URL
 * @param storeId - The store ID from the database
 * @returns Modified URL with affiliate tag if Amazon or eBay, otherwise original URL
 * @deprecated Use getDealAffiliateUrl instead for dynamic affiliate URL generation
 */
export function addAffiliateTag(url: string, storeId: number): string {
  try {
    // Parse the URL
    const urlObj = new URL(url);

    // Check if this is an Amazon deal (storeId === 57)
    if (storeId === 57) {
      // Check if it's actually an Amazon URL to be safe
      if (urlObj.hostname.includes('amazon') || urlObj.hostname.includes('amzn')) {
        // Add the affiliate tag
        urlObj.searchParams.set('tag', 'bargn0c-21');
        return urlObj.toString();
      }
    }

    // Check if this is an eBay deal (storeId === 58)
    if (storeId === 58) {
      // Check if it's actually an eBay URL to be safe
      if (urlObj.hostname.includes('ebay')) {
        // Add eBay affiliate parameters for UK
        urlObj.searchParams.set('mkevt', '1');
        urlObj.searchParams.set('mkcid', '1');
        urlObj.searchParams.set('mkrid', '710-53481-19255-0');
        urlObj.searchParams.set('campid', '5338762847');
        return urlObj.toString();
      }
    }
  } catch (error) {
    // If URL parsing fails, return original URL
    console.warn('Failed to parse URL for affiliate tag:', error);
    return url;
  }

  // Return original URL for non-affiliate stores or if processing failed
  return url;
}

/**
 * Dynamically generates affiliate URL for a deal when displaying to users
 * @param deal - Deal object with dealUrl and storeId
 * @returns URL with affiliate parameters added if applicable
 */
export function getDealAffiliateUrl(deal: { dealUrl: string; storeId: number }): string {
  try {
    // Parse the URL
    const urlObj = new URL(deal.dealUrl);

    // Check if this is an Amazon deal (storeId === 57)
    if (deal.storeId === 57) {
      // Check if it's actually an Amazon URL to be safe
      if (urlObj.hostname.includes('amazon') || urlObj.hostname.includes('amzn')) {
        // Add the affiliate tag
        urlObj.searchParams.set('tag', 'bargn0c-21');
        return urlObj.toString();
      }
    }

    // Check if this is an eBay deal (storeId === 58)
    if (deal.storeId === 58) {
      // Check if it's actually an eBay URL to be safe
      if (urlObj.hostname.includes('ebay')) {
        // Add eBay affiliate parameters for UK
        urlObj.searchParams.set('mkevt', '1');
        urlObj.searchParams.set('mkcid', '1');
        urlObj.searchParams.set('mkrid', '710-53481-19255-0');
        urlObj.searchParams.set('campid', '5338762847');
        return urlObj.toString();
      }
    }
  } catch (error) {
    // If URL parsing fails, return original URL
    console.warn('Failed to parse URL for dynamic affiliate tag:', error);
    return deal.dealUrl;
  }

  // Return original URL for non-affiliate stores or if processing failed
  return deal.dealUrl;
}

export function generateShareText(deal: { title: string; store?: { name: string }; dealPrice: number | string | Decimal; originalPrice?: number | string | null | Decimal; id: number }) {
  const dealPriceNum = typeof deal.dealPrice === 'object' && 'toNumber' in deal.dealPrice ? deal.dealPrice.toNumber() : Number(deal.dealPrice);
  const originalPriceNum = deal.originalPrice ? (typeof deal.originalPrice === 'object' && 'toNumber' in deal.originalPrice ? deal.originalPrice.toNumber() : Number(deal.originalPrice)) : null;
  const hasDiscount = originalPriceNum && originalPriceNum > dealPriceNum;
  // Use short URL for sharing to save characters on Twitter
  const shareUrl = getShortDealUrl(deal.id);
  const storeHashtag = deal.store?.name ? `#${deal.store.name.replace(/\s+/g, '')}` : '';
  const priceText = hasDiscount 
    ? `Price currently down to £${dealPriceNum.toFixed(2)} from £${originalPriceNum.toFixed(2)}`
    : `Price just £${dealPriceNum.toFixed(2)}`;
  const shareText = `Nicedeals UK presents ${deal.title} on ${deal.store?.name || 'this store'}\n\n${priceText}\n\n#deal #ukdeals #bargains ${storeHashtag}`.trim();
  return { shareText, shareUrl };
}

/**
 * Generate short deal URL for sharing (saves characters on Twitter)
 * @param dealId The deal ID
 * @returns Short URL like https://www.nicedeals.app/deals/183
 */
export function getShortDealUrl(dealId: number): string {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "https://www.nicedeals.app";
  return `${baseUrl}/deals/${dealId}`;
}

/**
 * Generate long SEO-friendly deal URL (canonical version)
 * @param title The deal title
 * @param dealId The deal ID
 * @returns Long URL like https://www.nicedeals.app/deals/dewalt-dwst83541-1-soft-tool-organiser-tstak-compatible-183
 */
export function getLongDealUrl(title: string, dealId: number): string {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "https://www.nicedeals.app";
  const slug = generateDealSlugWithId(title, dealId);
  return `${baseUrl}/deals/${slug}`;
}

/**
 * Check if a given deal route parameter is a numeric ID (short format) or slug (long format)
 * @param param The route parameter
 * @returns true if it's a numeric ID, false if it's a slug
 */
export function isShortDealFormat(param: string): boolean {
  return /^\d+$/.test(param);
}
