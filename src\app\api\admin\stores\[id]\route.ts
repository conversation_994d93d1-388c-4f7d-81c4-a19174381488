import { NextRequest, NextResponse } from "next/server"
import { requireAdmin } from "@/lib/auth"
import { db } from "@/lib/db"
import { generateSlug } from "@/lib/slug"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAdmin()

    const resolvedParams = await params
    const storeId = parseInt(resolvedParams.id, 10)
    if (isNaN(storeId)) {
      return NextResponse.json(
        { error: "Invalid store ID" },
        { status: 400 }
      )
    }

    const store = await db.store.findUnique({
      where: { id: storeId }
    })

    if (!store) {
      return NextResponse.json(
        { error: "Store not found" },
        { status: 404 }
      )
    }

    const response = NextResponse.json({
      id: store.id,
      name: store.name,
      slug: store.slug,
      url: store.url,
      logoUrl: store.logoUrl,
      description: store.description,
      active: store.active,
      featured: store.featured,
      dealCount: store.dealCount,
      averageDiscount: store.averageDiscount
    })
    
    // Prevent caching for admin endpoints to ensure fresh data
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, max-age=0')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')
    
    return response
  } catch (error) {
    console.error("Error fetching store:", error)
    return NextResponse.json(
      { error: "Failed to fetch store" },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAdmin()

    const resolvedParams = await params
    const storeId = parseInt(resolvedParams.id, 10)
    if (isNaN(storeId)) {
      return NextResponse.json(
        { error: "Invalid store ID" },
        { status: 400 }
      )
    }

    const { name, url, description, logoUrl, active, featured } = await request.json()

    if (!name || !name.trim()) {
      return NextResponse.json(
        { error: "Store name is required" },
        { status: 400 }
      )
    }

    if (!url || !url.trim()) {
      return NextResponse.json(
        { error: "Store URL is required" },
        { status: 400 }
      )
    }

    const existingStore = await db.store.findUnique({
      where: { id: storeId }
    })

    if (!existingStore) {
      return NextResponse.json(
        { error: "Store not found" },
        { status: 404 }
      )
    }

    const slug = generateSlug(name)

    // Check if another store with this slug already exists
    const duplicateStore = await db.store.findFirst({
      where: { 
        slug,
        id: { not: storeId }
      }
    })

    if (duplicateStore) {
      return NextResponse.json(
        { error: "A store with this name already exists" },
        { status: 400 }
      )
    }

    const updatedStore = await db.store.update({
      where: { id: storeId },
      data: {
        name: name.trim(),
        slug,
        url: url.trim(),
        description: description?.trim() || null,
        logoUrl: logoUrl?.trim() || null,
        active: active !== undefined ? active : existingStore.active,
        featured: featured !== undefined ? featured : existingStore.featured
      }
    })

    return NextResponse.json({
      id: updatedStore.id,
      name: updatedStore.name,
      slug: updatedStore.slug,
      url: updatedStore.url,
      logoUrl: updatedStore.logoUrl,
      description: updatedStore.description,
      active: updatedStore.active,
      featured: updatedStore.featured,
      dealCount: updatedStore.dealCount,
      averageDiscount: updatedStore.averageDiscount
    })
  } catch (error) {
    console.error("Error updating store:", error)
    return NextResponse.json(
      { error: "Failed to update store" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAdmin()

    const resolvedParams = await params
    const storeId = parseInt(resolvedParams.id, 10)
    if (isNaN(storeId)) {
      return NextResponse.json(
        { error: "Invalid store ID" },
        { status: 400 }
      )
    }

    const store = await db.store.findUnique({
      where: { id: storeId }
    })

    if (!store) {
      return NextResponse.json(
        { error: "Store not found" },
        { status: 404 }
      )
    }

    // Check if store has deals (using dealCount field)
    if (store.dealCount > 0) {
      return NextResponse.json(
        { error: "Cannot delete store with existing deals" },
        { status: 400 }
      )
    }

    await db.store.delete({
      where: { id: storeId }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting store:", error)
    return NextResponse.json(
      { error: "Failed to delete store" },
      { status: 500 }
    )
  }
}
