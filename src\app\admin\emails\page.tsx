import { redirect } from "next/navigation"
import { requireAdmin } from "@/lib/auth"
import AdminLayout from "@/components/admin/AdminLayout"
import { getAdminStats } from "@/lib/admin"
import EmailTestPanel from "@/components/admin/EmailTestPanel"

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'

export default async function AdminEmailsPage() {
  try {
    await requireAdmin()
  } catch {
    redirect("/auth/signin")
  }

  const stats = await getAdminStats()

  return (
    <AdminLayout stats={stats} currentPage="emails">
      <EmailTestPanel />
    </AdminLayout>
  )
} 