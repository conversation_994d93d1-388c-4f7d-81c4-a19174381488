import { redirect } from "next/navigation"
import { requireAdmin } from "@/lib/auth"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import AdminLayout from "@/components/admin/AdminLayout"
import { getAdminStats } from "@/lib/admin"
import StoreManagement from "@/components/admin/StoreManagement"

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'

export default async function AdminStoresPage() {
  try {
    await requireAdmin()
  } catch {
    redirect("/auth/signin")
  }

  const stats = await getAdminStats()

  return (
    <AdminLayout stats={stats} currentPage="stores">
      <Card>
        <CardHeader>
          <CardTitle>Store Management</CardTitle>
          <CardDescription>
            Manage store information and featured status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <StoreManagement />
        </CardContent>
      </Card>
    </AdminLayout>
  )
}
