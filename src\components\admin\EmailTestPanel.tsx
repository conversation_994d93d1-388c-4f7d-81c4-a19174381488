'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card } from '@/components/ui/card'
import { Alert } from '@/components/ui/alert'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'

export default function EmailTestPanel() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<{ success?: boolean; message?: string; error?: string } | null>(null)

  const sendTestEmail = async (type: string, data: Record<string, string>) => {
    setLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/email/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ type, ...data }),
      })

      const result = await response.json()
      setResult(result)
    } catch {
      setResult({ error: 'Failed to send email' })
    } finally {
      setLoading(false)
    }
  }

  const WelcomeEmailForm = () => {
    const [email, setEmail] = useState('<EMAIL>')
    const [userName, setUserName] = useState('Test User')

    return (
      <div className="space-y-4">
        <div>
          <Label htmlFor="welcome-email">Email Address</Label>
          <Input
            id="welcome-email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
          />
        </div>
        <div>
          <Label htmlFor="welcome-name">User Name</Label>
          <Input
            id="welcome-name"
            value={userName}
            onChange={(e) => setUserName(e.target.value)}
            placeholder="John Doe"
          />
        </div>
        <Button
          onClick={() => sendTestEmail('welcome', { email, name: userName })}
          disabled={loading || !email || !userName}
          className="w-full"
        >
          {loading ? 'Sending...' : 'Send Welcome Email'}
        </Button>
      </div>
    )
  }

  const DealApprovalForm = () => {
    const [email, setEmail] = useState('<EMAIL>')
    const [dealTitle, setDealTitle] = useState('Amazing Product Deal - 50% Off!')
    const [dealUrl, setDealUrl] = useState('http://localhost:3010/deals/1')

    return (
      <div className="space-y-4">
        <div>
          <Label htmlFor="approval-email">Email Address</Label>
          <Input
            id="approval-email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
          />
        </div>
        <div>
          <Label htmlFor="approval-title">Deal Title</Label>
          <Input
            id="approval-title"
            value={dealTitle}
            onChange={(e) => setDealTitle(e.target.value)}
            placeholder="Amazing Deal Title"
          />
        </div>
        <div>
          <Label htmlFor="approval-url">Deal URL</Label>
          <Input
            id="approval-url"
            value={dealUrl}
            onChange={(e) => setDealUrl(e.target.value)}
            placeholder="https://nicedeals.app/deals/1"
          />
        </div>
        <Button
          onClick={() => sendTestEmail('deal-approval', { email, dealTitle, dealUrl })}
          disabled={loading || !email || !dealTitle || !dealUrl}
          className="w-full"
        >
          {loading ? 'Sending...' : 'Send Approval Email'}
        </Button>
      </div>
    )
  }

  const DealRejectionForm = () => {
    const [email, setEmail] = useState('<EMAIL>')
    const [dealTitle, setDealTitle] = useState('Product Deal Submission')
    const [reason, setReason] = useState('Insufficient product information provided')

    return (
      <div className="space-y-4">
        <div>
          <Label htmlFor="rejection-email">Email Address</Label>
          <Input
            id="rejection-email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
          />
        </div>
        <div>
          <Label htmlFor="rejection-title">Deal Title</Label>
          <Input
            id="rejection-title"
            value={dealTitle}
            onChange={(e) => setDealTitle(e.target.value)}
            placeholder="Deal Title"
          />
        </div>
        <div>
          <Label htmlFor="rejection-reason">Rejection Reason</Label>
          <Input
            id="rejection-reason"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder="Reason for rejection"
          />
        </div>
        <Button
          onClick={() => sendTestEmail('deal-rejection', { email, dealTitle, reason })}
          disabled={loading || !email || !dealTitle || !reason}
          className="w-full"
        >
          {loading ? 'Sending...' : 'Send Rejection Email'}
        </Button>
      </div>
    )
  }

  const PasswordResetForm = () => {
    const [email, setEmail] = useState('<EMAIL>')
    const [name, setName] = useState('Test User')
    const [resetToken, setResetToken] = useState('test-reset-token-123456789')

    return (
      <div className="space-y-4">
        <div>
          <Label htmlFor="reset-email">Email Address</Label>
          <Input
            id="reset-email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
          />
        </div>
        <div>
          <Label htmlFor="reset-name">User Name</Label>
          <Input
            id="reset-name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="John Doe"
          />
        </div>
        <div>
          <Label htmlFor="reset-token">Reset Token</Label>
          <Input
            id="reset-token"
            value={resetToken}
            onChange={(e) => setResetToken(e.target.value)}
            placeholder="reset-token-here"
          />
        </div>
        <Button
          onClick={() => sendTestEmail('password-reset', { email, name, resetToken })}
          disabled={loading || !email || !name || !resetToken}
          className="w-full"
        >
          {loading ? 'Sending...' : 'Send Password Reset Email'}
        </Button>
      </div>
    )
  }

  const CustomEmailForm = () => {
    const [email, setEmail] = useState('<EMAIL>')
    const [subject, setSubject] = useState('Test Email from NiceDeals')
    const [html, setHtml] = useState('<h1>Hello!</h1><p>This is a test email from the NiceDeals admin panel.</p>')

    return (
      <div className="space-y-4">
        <div>
          <Label htmlFor="custom-email">Email Address</Label>
          <Input
            id="custom-email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
          />
        </div>
        <div>
          <Label htmlFor="custom-subject">Subject</Label>
          <Input
            id="custom-subject"
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
            placeholder="Email subject"
          />
        </div>
        <div>
          <Label htmlFor="custom-html">HTML Content</Label>
          <textarea
            id="custom-html"
            value={html}
            onChange={(e) => setHtml(e.target.value)}
            placeholder="<h1>Hello!</h1><p>Your message here...</p>"
            className="w-full h-32 p-2 border border-gray-300 rounded-md"
          />
        </div>
        <Button
          onClick={() => sendTestEmail('custom', { email, subject, message: html })}
          disabled={loading || !email || !subject || !html}
          className="w-full"
        >
          {loading ? 'Sending...' : 'Send Custom Email'}
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">📧 Email Test Panel</h2>
        <p className="text-gray-600">Test the email functionality powered by Resend</p>
      </div>

      <Tabs defaultValue="welcome" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="welcome">Welcome</TabsTrigger>
          <TabsTrigger value="approval">Approval</TabsTrigger>
          <TabsTrigger value="rejection">Rejection</TabsTrigger>
          <TabsTrigger value="password-reset">Password Reset</TabsTrigger>
          <TabsTrigger value="custom">Custom</TabsTrigger>
        </TabsList>

        <TabsContent value="welcome">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Welcome Email</h3>
            <WelcomeEmailForm />
          </Card>
        </TabsContent>

        <TabsContent value="approval">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Deal Approval Email</h3>
            <DealApprovalForm />
          </Card>
        </TabsContent>

        <TabsContent value="rejection">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Deal Rejection Email</h3>
            <DealRejectionForm />
          </Card>
        </TabsContent>

        <TabsContent value="password-reset">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Password Reset Email</h3>
            <PasswordResetForm />
          </Card>
        </TabsContent>

        <TabsContent value="custom">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Custom Email</h3>
            <CustomEmailForm />
          </Card>
        </TabsContent>
      </Tabs>

      {result && (
        <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
          <div className="flex items-center space-x-2">
            <span className={result.success ? 'text-green-600' : 'text-red-600'}>
              {result.success ? '✅' : '❌'}
            </span>
            <span className={result.success ? 'text-green-800' : 'text-red-800'}>
              {result.success ? result.message : result.error}
            </span>
          </div>
        </Alert>
      )}

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-semibold text-blue-800 mb-2">📊 Email Service Status</h4>
        <div className="space-y-1 text-sm text-blue-700">
          <p>✅ Resend API configured and working</p>
          <p>✅ Email templates implemented</p>
          <p>✅ Deal moderation emails automated</p>
          <p>✅ Welcome emails on registration</p>
          <p>🔧 API Key: {process.env.RESEND_API_KEY ? 'Configured' : 'Missing'}</p>
        </div>
      </div>
    </div>
  )
} 