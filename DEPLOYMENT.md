# 🚀 NiceDeals v2 Production Deployment Guide

This guide shows you how to deploy and update NiceDeals v2 using one simple command that handles everything automatically.

## 📋 Prerequisites

- Ubuntu 20.04+ server with root access
- Node.js already installed on the server
- Domain name pointing to your server (www.nicedeals.app)
- Cloudflare account with Flexible SSL configured
- GitHub repository with your code
- `.env.prod` file ready with production configuration

## 🔧 Deployment Process

### 1. Connect to your server

```bash
# SSH into your server
ssh gazza@your-server-ip
```

### 2. Clone your repository and prepare environment

```bash
# Clone your repository to the correct location
cd /home/<USER>
git clone https://github.com/YOUR_USERNAME/nicedeals.git

# Go into the app directory
cd nicedeals

# Ensure you have .env.prod file in the repository
ls -la .env.prod  # Should exist
```

### 3. Deploy the application

```bash
# From your local machine, copy deploy.sh to the app directory
scp deploy.sh gazza@your-server-ip:/home/<USER>/nicedeals/

# On the server, run the deployment (works for both initial setup and updates):
cd /home/<USER>/nicedeals
chmod +x deploy.sh
sudo ./deploy.sh
```

The script will auto-detect whether this is first-time setup or an update and will:

**First Time Setup:**
- ✅ Install system packages and configure Nginx
- ✅ Set up firewall and log rotation  
- ✅ Copy dev database to production database
- ✅ Configure PM2 and system services

**Updates/Re-deployments:**
- ✅ Skip system configuration (already done)
- ✅ Backup production database before proceeding
- ✅ Pull latest code and rebuild application
- ✅ Restart services with zero downtime

**Both Modes:**
- ✅ Check Node.js and install PM2 if needed
- ✅ Use your existing repository and .env.prod
- ✅ Build your Next.js application
- ✅ Deploy with proper permissions and security
- ✅ Disable debug logging for clean production logs

## 🔑 Post-Deployment Configuration

### 1. Configure Cloudflare SSL

In your Cloudflare dashboard:
1. Go to SSL/TLS → Overview
2. Set encryption mode to **"Flexible"**
3. Enable **"Always Use HTTPS"**
4. Enable **"Automatic HTTPS Rewrites"**

### 2. DNS Configuration

Point your domain to your server:
- **A Record**: `nicedeals.app` → `your-server-ip`
- **A Record**: `www.nicedeals.app` → `your-server-ip`

## 🔄 Future Updates

For any future code updates, simply run the same command:

```bash
cd /home/<USER>/nicedeals
sudo ./deploy.sh
```

The script automatically detects this is an update and will:
- 📦 Backup your production database
- 📥 Pull the latest code from GitHub
- 🔨 Rebuild the application
- 🔄 Restart services with zero downtime
- ⚡ Skip system setup (already configured)

## 📊 Monitoring Commands

### Check Application Status
```bash
# PM2 status
pm2 status

# View logs
pm2 logs nicedeals

# Monitor in real-time
pm2 monit
```

### Check Nginx Status
```bash
# Nginx status
systemctl status nginx

# Test configuration
nginx -t

# View access logs
tail -f /var/log/nginx/access.log
```

### Database Management
```bash
# View production database location
ls -la /home/<USER>/nicedeals/prisma/nicedeals.db

# Backup database manually
cp /home/<USER>/nicedeals/prisma/nicedeals.db ~/backup-$(date +%Y%m%d).db
```

## 🔐 Security Features

Your deployment includes:

- **Rate Limiting**: API endpoints limited to prevent abuse
- **Security Headers**: XSS protection, CSRF protection, etc.
- **Firewall**: Only SSH and HTTP/HTTPS ports open
- **File Permissions**: Proper ownership and permissions set
- **Log Rotation**: Automatic log cleanup to prevent disk space issues

## 🚨 Troubleshooting

### Application won't start
```bash
# Check PM2 logs
pm2 logs nicedeals

# Restart application
pm2 restart nicedeals

# Check if port 3010 is in use
netstat -tulpn | grep :3010
```

### Excessive SQL logging in production
```bash
# This should not happen with the deploy script, but if you see Prisma queries:
# Check environment variables
pm2 env nicedeals

# Ensure DEBUG is not set
pm2 restart nicedeals
```

### Nginx issues
```bash
# Test configuration
nginx -t

# Check error logs
tail -f /var/log/nginx/error.log

# Restart Nginx
systemctl restart nginx
```

### Database issues
```bash
# Check database file exists
ls -la /home/<USER>/nicedeals/prisma/nicedeals.db

# Check file permissions
ls -la /home/<USER>/nicedeals/prisma/

# Fix permissions if needed
chown gazza:gazza /home/<USER>/nicedeals/prisma/nicedeals.db
```

## 📝 Important Notes

1. **One Command**: Whether it's first-time setup or updates, always use `sudo ./deploy.sh` from the app directory.

2. **Smart Detection**: The script automatically detects what needs to be done based on your current setup.

3. **Database**: After initial deployment, your production database (`nicedeals.db`) becomes the source of truth. Updates automatically backup before proceeding.

4. **SSL**: Cloudflare Flexible SSL means traffic between Cloudflare and your server is HTTP, but users see HTTPS. This is perfect for your setup.

5. **Safety**: Always test updates in development first. The deploy script automatically backs up your database before updating.

6. **Monitoring**: Set up monitoring alerts for your server (disk space, memory, CPU usage).

## 🎉 Success!

Once deployment is complete, your NiceDeals v2 app should be live at:
- **https://www.nicedeals.app**
- **https://nicedeals.app**

The deployment includes all your new features:
- ✅ View tracking system
- ✅ Efficient database structure
- ✅ Duplicate prevention
- ✅ All existing functionalitys