// Structured data utilities for SEO

export interface Deal {
  id: string
  title: string
  description: string
  dealPrice: number
  originalPrice?: number
  store: string
  category: string
  dealUrl: string
  imageUrl?: string
  averageRating?: number
  ratingCount?: number
  createdAt: Date
}

export function generateDealStructuredData(deal: Deal, baseUrl: string) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    "@id": `${baseUrl}/deals/${deal.id}`,
    "name": deal.title,
    "description": deal.description,
    "image": deal.imageUrl ? [deal.imageUrl] : [],
    "url": `${baseUrl}/deals/${deal.id}`,
    "brand": {
      "@type": "Brand",
      "name": deal.store
    },
    "category": deal.category,
    "offers": {
      "@type": "Offer",
      "price": deal.dealPrice.toString(),
      "priceCurrency": "GBP",
      "availability": "https://schema.org/InStock",
      "url": deal.dealUrl,
      "seller": {
        "@type": "Organization",
        "name": deal.store
      },
      "priceValidUntil": new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
      ...(deal.originalPrice && {
        "priceSpecification": {
          "@type": "PriceSpecification",
          "price": deal.originalPrice.toString(),
          "priceCurrency": "GBP"
        }
      })
    },
    ...(deal.averageRating && deal.ratingCount && {
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": deal.averageRating.toString(),
        "reviewCount": deal.ratingCount.toString(),
        "bestRating": "5",
        "worstRating": "1"
      }
    }),
    "datePublished": deal.createdAt.toISOString(),
    "publisher": {
      "@type": "Organization",
      "name": "NiceDeals",
      "url": baseUrl
    }
  }

  return structuredData
}

export function generateWebsiteStructuredData(baseUrl: string) {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "NiceDeals",
    "description": "Discover and share the best deals from top UK retailers. Community-driven deals platform with user reviews and ratings.",
    "url": baseUrl,
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/browse?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "NiceDeals",
      "url": baseUrl
    }
  }
}

export function generateBreadcrumbStructuredData(items: Array<{ name: string; url: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  }
}

export function generateOrganizationStructuredData(baseUrl: string) {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "NiceDeals",
    "description": "Community-driven deals platform featuring the best offers from top UK retailers",
    "url": baseUrl,
    "logo": `${baseUrl}/nicedeals-logo.png`,
    "sameAs": [
      // Add social media URLs when available
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": "English"
    }
  }
}

export function generateItemListStructuredData(
  deals: Deal[], 
  baseUrl: string, 
  listName: string
) {
  return {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": listName,
    "numberOfItems": deals.length,
    "itemListElement": deals.map((deal, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "item": {
        "@type": "Product",
        "@id": `${baseUrl}/deals/${deal.id}`,
        "name": deal.title,
        "url": `${baseUrl}/deals/${deal.id}`,
        "image": deal.imageUrl,
        "offers": {
          "@type": "Offer",
          "price": deal.dealPrice.toString(),
          "priceCurrency": "GBP"
        }
      }
    }))
  }
}

export function generateFAQStructuredData(faqs: Array<{ question: string; answer: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  }
}

// Utility to safely stringify JSON-LD
export function safeJsonLd(data: any): string {
  return JSON.stringify(data, null, 0)
    .replace(/</g, '\\u003c')
    .replace(/>/g, '\\u003e')
    .replace(/&/g, '\\u0026')
}
