"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Check, ExternalLink, Clock, Trash2, CheckSquare, Square, Sparkles, X } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import { getSafeImageUrl } from "@/lib/utils"
import { generateSlug, generateDealSlugWithId } from "@/lib/slug"
import SetOriginalPrice from "./SetOriginalPrice"

interface PendingDeal {
  id: string
  title: string
  description: string
  slug?: string
  originalPrice?: number | string | object | null
  dealPrice: number | string | object
  discountPercentage?: number
  store: {
    id: number
    name: string
    slug: string
  }
  category: {
    id: number
    name: string
    slug: string
  }
  dealUrl: string
  imageUrl?: string
  couponCode?: string
  expiresAt?: string
  createdAt: string
  author: {
    id: string
    name: string
    image?: string
  }
}

interface ApprovalProgress {
  step: number
  total: number
  currentStep: string
  isProcessing: boolean
  error?: string
  logs: string[]
}

interface PersistentToast {
  message: string
  type: 'success' | 'error' | 'info'
  details?: string
  onClose: () => void
}

// Helper function to format price values safely
const formatPrice = (price: number | string | object | null | undefined): string => {
  if (price === null || price === undefined) return "0.00";
  
  // Handle various types that might come from Prisma/API
  if (typeof price === 'number') {
    return price.toFixed(2);
  } else if (typeof price === 'string') {
    return parseFloat(price).toFixed(2);
  } else if (typeof price === 'object' && price !== null) {
    // Handle Prisma Decimal objects which might be serialized as objects
    if ('toString' in price) {
      return parseFloat((price as { toString(): string }).toString()).toFixed(2);
    }
  }
  
  // Fallback
  return "0.00";
};

// Persistent Toast Component
function PersistentToast({ message, type, details, onClose }: PersistentToast) {
  const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
  const textColor = 'text-white';
  
  return (
    <div className="fixed top-4 right-4 z-50 max-w-md">
      <div className={`${bgColor} ${textColor} p-4 rounded-lg shadow-lg`}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="font-semibold">{message}</div>
            {details && (
              <div className="text-sm mt-1 opacity-90">{details}</div>
            )}
          </div>
          <button
            onClick={onClose}
            className="ml-4 text-white hover:text-gray-200 flex-shrink-0"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
}

export default function PendingDeals() {
  const router = useRouter()
  const [deals, setDeals] = useState<PendingDeal[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const [selectedDeals, setSelectedDeals] = useState<Set<string>>(new Set())
  const [bulkDeleteLoading, setBulkDeleteLoading] = useState(false)
  const [massApproveLoading, setMassApproveLoading] = useState(false)
  const [approvalProgress, setApprovalProgress] = useState<Record<string, ApprovalProgress>>({})
  const [persistentToast, setPersistentToast] = useState<PersistentToast | null>(null)

  useEffect(() => {
    fetchPendingDeals()
  }, [])

  async function fetchPendingDeals() {
    try {
      const response = await fetch("/api/admin/pending-deals")
      if (response.ok) {
        const data = await response.json()
        setDeals(data.deals)
      }
    } catch (error) {
      console.error("Error fetching pending deals:", error)
    } finally {
      setLoading(false)
    }
  }

  async function handleEnhancedApprove(deal: PendingDeal) {
    const dealId = deal.id
    console.log("🚀🚀🚀 ENHANCED APPROVE FUNCTION CALLED FOR DEAL:", dealId, deal.title)
    console.log("🔍 Deal object:", deal)
    setActionLoading(dealId)
    
    // Initialize progress
    const initProgress: ApprovalProgress = {
      step: 0,
      total: 4,
      currentStep: "Starting approval process...",
      isProcessing: true,
      error: undefined,
      logs: [`🚀 Starting enhanced approval for deal: ${deal.title}`]
    }
    setApprovalProgress(prev => ({ ...prev, [dealId]: initProgress }))

    try {
      // Track failures for critical steps
      let titleFailed = false
      let descriptionFailed = false
      let imageFailed = false

      // Step 1: Improve Title
      setApprovalProgress(prev => ({
        ...prev,
        [dealId]: {
          ...prev[dealId],
          step: 1,
          currentStep: "Improving title with AI...",
          logs: [...prev[dealId].logs, "📝 Step 1/4: Improving title..."]
        }
      }))

      let improvedTitle = deal.title
      try {
        const titleResponse = await fetch("/api/admin/ai/improve-title", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ title: deal.title }),
        })
        const titleData = await titleResponse.json()
        
        if (titleData.success && titleData.title) {
          // Strip surrounding quotes from AI-improved title (handles multiple quotes)
          improvedTitle = titleData.title.replace(/^["']+|["']+$/g, '').trim()
          setApprovalProgress(prev => ({
            ...prev,
            [dealId]: {
              ...prev[dealId],
              logs: [...prev[dealId].logs, `✅ Title improved: "${improvedTitle}"`]
            }
          }))
        } else {
          titleFailed = true
          setApprovalProgress(prev => ({
            ...prev,
            [dealId]: {
              ...prev[dealId],
              logs: [...prev[dealId].logs, `❌ Title improvement failed: ${titleData.error || 'Unknown error'}`]
            }
          }))
        }
      } catch (error) {
        titleFailed = true
        setApprovalProgress(prev => ({
          ...prev,
          [dealId]: {
            ...prev[dealId],
            logs: [...prev[dealId].logs, `❌ Title improvement error: ${error}`]
          }
        }))
      }

      // Step 2: Improve Description (takes ~20 seconds)
      setApprovalProgress(prev => ({
        ...prev,
        [dealId]: {
          ...prev[dealId],
          step: 2,
          currentStep: "Generating description with AI (this takes ~20 seconds)...",
          logs: [...prev[dealId].logs, "🤖 Step 2/4: Generating description (please wait ~20 seconds)..."]
        }
      }))

      let improvedDescription = deal.description
      try {
        const descResponse = await fetch("/api/admin/ai/improve-description", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ title: improvedTitle, url: deal.dealUrl }),
        })
        const descData = await descResponse.json()
        
        if (descData.success && descData.description && descData.description !== "COULD NOT CREATE DESCRIPTION") {
          improvedDescription = descData.description
          setApprovalProgress(prev => ({
            ...prev,
            [dealId]: {
              ...prev[dealId],
              logs: [...prev[dealId].logs, `✅ Description generated: "${improvedDescription.substring(0, 100)}..."`]
            }
          }))
        } else {
          descriptionFailed = true
          setApprovalProgress(prev => ({
            ...prev,
            [dealId]: {
              ...prev[dealId],
              logs: [...prev[dealId].logs, `❌ Description generation failed: ${descData.error || 'AI could not create description'}`]
            }
          }))
        }
      } catch (error) {
        descriptionFailed = true
        setApprovalProgress(prev => ({
          ...prev,
          [dealId]: {
            ...prev[dealId],
            logs: [...prev[dealId].logs, `❌ Description generation error: ${error}`]
          }
        }))
      }

      // Step 3: Localize Image (only if remote)
      setApprovalProgress(prev => ({
        ...prev,
        [dealId]: {
          ...prev[dealId],
          step: 3,
          currentStep: "Localizing image...",
          logs: [...prev[dealId].logs, "🖼️ Step 3/4: Checking image localization..."]
        }
      }))

      let finalImageUrl = deal.imageUrl
      const isRemoteImage = deal.imageUrl && !deal.imageUrl.startsWith("/uploads/")
      
      if (isRemoteImage) {
        try {
          setApprovalProgress(prev => ({
            ...prev,
            [dealId]: {
              ...prev[dealId],
              logs: [...prev[dealId].logs, `📥 Remote image detected, localizing: ${deal.imageUrl}`]
            }
          }))

          // Localize the remote image using server-side fetching
          if (!deal.imageUrl) throw new Error("No image URL provided")
          
          // Use the new API endpoint to localize external images
          const uploadResponse = await fetch("/api/images/upload", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              localizeUrl: deal.imageUrl,
              dealId: dealId
            }),
          })
          
          const uploadData = await uploadResponse.json()
          if (uploadData.success && uploadData.images?.[0]?.url) {
            finalImageUrl = uploadData.images[0].url
            const finalThumbnailUrl = uploadData.images[0].thumbnailUrl || finalImageUrl
            
            // Update the image URL in database
            const updateImageResponse = await fetch(`/api/admin/execute-sql`, {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                sql: "UPDATE deals SET image_url = ?, thumbnail_url = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                params: [finalImageUrl, finalThumbnailUrl, dealId]
              }),
            })
            
            const updateResult = await updateImageResponse.json()
            if (updateResult.success) {
              setApprovalProgress(prev => ({
                ...prev,
                [dealId]: {
                  ...prev[dealId],
                  logs: [...prev[dealId].logs, `✅ Image localized successfully: ${finalImageUrl}`, `🖼️ Thumbnail created: ${finalThumbnailUrl}`]
                }
              }))
            } else {
              throw new Error(updateResult.error || "Failed to update image URL in database")
            }
          } else {
            throw new Error(uploadData.message || "Image upload failed")
          }
        } catch (error) {
          imageFailed = true
          setApprovalProgress(prev => ({
            ...prev,
            [dealId]: {
              ...prev[dealId],
              logs: [...prev[dealId].logs, `❌ Image localization error: ${error}`]
            }
          }))
        }
      } else {
        setApprovalProgress(prev => ({
          ...prev,
          [dealId]: {
            ...prev[dealId],
            logs: [...prev[dealId].logs, `✅ Image already local, skipping: ${deal.imageUrl}`]
          }
        }))
      }

      // Check if any critical steps failed
      const hasFailures = titleFailed || descriptionFailed || imageFailed
      const failureCount = [titleFailed, descriptionFailed, imageFailed].filter(Boolean).length
      
      if (hasFailures) {
        // Don't activate the deal if any step failed
        setApprovalProgress(prev => ({
          ...prev,
          [dealId]: {
            ...prev[dealId],
            step: 4,
            currentStep: "❌ Approval failed - deal remains PENDING",
            isProcessing: false,
            error: `${failureCount} critical step(s) failed`,
            logs: [
              ...prev[dealId].logs, 
              `⚠️ APPROVAL BLOCKED: ${failureCount} critical step(s) failed`,
              "🔒 Deal will remain as PENDING until all steps succeed",
              titleFailed ? "❌ Title improvement failed" : "✅ Title improvement succeeded",
              descriptionFailed ? "❌ Description generation failed" : "✅ Description generation succeeded", 
              imageFailed ? "❌ Image localization failed" : "✅ Image localization succeeded"
            ]
          }
        }))

        // Show error toast for single approval
        if (!massApproveLoading) {
          setPersistentToast({
            message: "Deal Approval Failed!",
            type: "error",
            details: `${failureCount} critical step(s) failed. Deal remains PENDING. Check logs for details.`,
            onClose: () => {
              setPersistentToast(null)
            }
          })
        }
        
        setActionLoading("")
        return
      }

      // Step 4: Save All Changes and Activate (only if all 3 critical steps succeeded)
      setApprovalProgress(prev => ({
        ...prev,
        [dealId]: {
          ...prev[dealId],
          step: 4,
          currentStep: "Saving changes and activating deal...",
          logs: [
            ...prev[dealId].logs, 
            "✅ All critical steps succeeded!",
            "💾 Step 4/4: Saving all changes and activating..."
          ]
        }
      }))

      // Get current deal data to preserve store and category IDs
      setApprovalProgress(prev => ({
        ...prev,
        [dealId]: {
          ...prev[dealId],
          logs: [...prev[dealId].logs, "🔍 Fetching current deal data for store/category IDs..."]
        }
      }))

      const currentDealResponse = await fetch(`/api/admin/deals?id=${dealId}`)
      const currentDealData = await currentDealResponse.json()
      const currentDeal = currentDealData.deal || currentDealData.deals?.[0]

      setApprovalProgress(prev => ({
        ...prev,
        [dealId]: {
          ...prev[dealId],
          logs: [...prev[dealId].logs, `📋 Store ID: ${currentDeal?.storeId || currentDeal?.store?.id}, Category ID: ${currentDeal?.categoryId || currentDeal?.category?.id}`]
        }
      }))

      // Regenerate slug if title was improved
      let newSlug = deal.slug || generateSlug(deal.title) // fallback if no slug
      if (improvedTitle !== deal.title) {
        // Title was improved, regenerate slug with new title and deal ID
        newSlug = generateDealSlugWithId(improvedTitle, parseInt(dealId, 10))
        setApprovalProgress(prev => ({
          ...prev,
          [dealId]: {
            ...prev[dealId],
            logs: [...prev[dealId].logs, `🔗 Slug regenerated: "${newSlug}" (title changed)`]
          }
        }))
      }

      // Prepare deal data for saving
      const dealData = {
        title: improvedTitle,
        description: improvedDescription,
        slug: newSlug,
        price: deal.dealPrice.toString(),
        originalPrice: deal.originalPrice?.toString() || "",
        url: deal.dealUrl,
        status: "ACTIVE",
        storeId: (currentDeal?.storeId || currentDeal?.store?.id || "1").toString(),
        categoryId: (currentDeal?.categoryId || currentDeal?.category?.id || "1").toString(),
        coupon: deal.couponCode || null,
        imageUrl: finalImageUrl,
      }

      // Save the deal
      setApprovalProgress(prev => ({
        ...prev,
        [dealId]: {
          ...prev[dealId],
          logs: [...prev[dealId].logs, `💾 Saving deal with data: ${JSON.stringify(dealData, null, 2).substring(0, 200)}...`]
        }
      }))

      const saveResponse = await fetch(`/api/admin/deals?id=${dealId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(dealData),
      })

      if (saveResponse.ok) {
        setApprovalProgress(prev => ({
          ...prev,
          [dealId]: {
            ...prev[dealId],
            step: 4,
            currentStep: "✅ Deal approved and activated successfully!",
            isProcessing: false,
            logs: [...prev[dealId].logs, "🎉 Deal approved and activated successfully!", "📧 Sending approval notification to user..."]
          }
        }))

        // Show persistent toast for single approval (but don't remove deal from list)
        if (!massApproveLoading) {
          setPersistentToast({
            message: "Deal Approved Successfully!",
            type: "success",
            details: `"${deal.title}" has been approved and activated.`,
            onClose: () => {
              setPersistentToast(null)
              fetchPendingDeals() // Reload only when user closes toast
            }
          })
        }

      } else {
        const errorData = await saveResponse.json().catch(() => ({}))
        setApprovalProgress(prev => ({
          ...prev,
          [dealId]: {
            ...prev[dealId],
            logs: [...prev[dealId].logs, `❌ Save failed: ${saveResponse.status} - ${JSON.stringify(errorData)}`]
          }
        }))
        throw new Error(`Failed to save deal changes: ${saveResponse.status} - ${errorData.error || 'Unknown error'}`)
      }

    } catch (error) {
      console.error("Enhanced approval error:", error)
      setApprovalProgress(prev => ({
        ...prev,
        [dealId]: {
          ...prev[dealId],
          error: `❌ Approval failed: ${error}`,
          isProcessing: false,
          logs: [...prev[dealId].logs, `💥 FATAL ERROR: ${error}`]
        }
      }))
      
      // Show error toast for single approval
      if (!massApproveLoading) {
        setPersistentToast({
          message: "Deal Approval Failed!",
          type: "error",
          details: `Failed to approve "${deal.title}": ${error}`,
          onClose: () => setPersistentToast(null)
        })
      }
    } finally {
      setActionLoading(null)
    }
  }

  async function handleDeleteDeal(dealId: string) {
    if (!confirm("Are you sure you want to delete this deal?")) return

    setActionLoading(dealId)
    try {
      const response = await fetch("/api/admin/delete-deal", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ dealId }),
      })

      if (response.ok) {
        const dealTitle = deals.find(d => d.id === dealId)?.title || "Deal"
        setPersistentToast({
          message: "Deal Deleted Successfully!",
          type: "success",
          details: `"${dealTitle}" has been deleted.`,
          onClose: () => {
            setPersistentToast(null)
            fetchPendingDeals() // Reload when user closes toast
          }
        })
      } else {
        setPersistentToast({
          message: "Delete Failed!",
          type: "error",
          details: "Failed to delete the deal",
          onClose: () => setPersistentToast(null)
        })
      }
    } catch (error) {
      console.error("Error deleting deal:", error)
      setPersistentToast({
        message: "Delete Failed!",
        type: "error",
        details: "Error deleting deal",
        onClose: () => setPersistentToast(null)
      })
    } finally {
      setActionLoading(null)
    }
  }

  async function handleBulkDelete() {
    if (selectedDeals.size === 0) {
      setPersistentToast({
        message: "No Deals Selected",
        type: "error",
        details: "Please select deals to delete",
        onClose: () => setPersistentToast(null)
      })
      return
    }

    if (!confirm(`Are you sure you want to delete ${selectedDeals.size} selected deals? This action cannot be undone.`)) {
      return
    }

    setBulkDeleteLoading(true)
    try {
      const response = await fetch("/api/admin/bulk-delete-deals", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ dealIds: Array.from(selectedDeals) }),
      })

      if (response.ok) {
        const result = await response.json()
        setSelectedDeals(new Set())
        setPersistentToast({
          message: "Deals Deleted Successfully!",
          type: "success",
          details: `Successfully deleted ${result.deletedCount} deals`,
          onClose: () => {
            setPersistentToast(null)
            fetchPendingDeals() // Reload when user closes toast
          }
        })
      } else {
        const error = await response.json()
        setPersistentToast({
          message: "Delete Failed!",
          type: "error",
          details: error.error,
          onClose: () => setPersistentToast(null)
        })
      }
    } catch (error) {
      console.error("Error bulk deleting deals:", error)
      setPersistentToast({
        message: "Delete Failed!",
        type: "error",
        details: "Error deleting deals",
        onClose: () => setPersistentToast(null)
      })
    } finally {
      setBulkDeleteLoading(false)
    }
  }

  async function handleMassApprove() {
    if (selectedDeals.size === 0) {
      setPersistentToast({
        message: "No Deals Selected",
        type: "error",
        details: "Please select deals to approve",
        onClose: () => setPersistentToast(null)
      })
      return
    }

    if (!confirm(`Are you sure you want to approve ${selectedDeals.size} selected deals? This process may take several minutes.`)) {
      return
    }

    setMassApproveLoading(true)
    const selectedDealsList = Array.from(selectedDeals)
    let approvedCount = 0
    let errorCount = 0
    const failedDeals: string[] = []

    try {
      for (let i = 0; i < selectedDealsList.length; i++) {
        const dealId = selectedDealsList[i]
        const deal = deals.find(d => d.id === dealId)
        
        if (!deal) continue

        try {
          await handleEnhancedApprove(deal)
          approvedCount++
        } catch {
          errorCount++
          failedDeals.push(deal.title)
        }

        // Small delay between approvals to prevent overwhelming the system
        if (i < selectedDealsList.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }

      // Clear selections
      setSelectedDeals(new Set())

      // Show final summary toast
      const message = errorCount === 0 ? "Mass Approval Completed!" : "Mass Approval Completed with Errors"
      const details = errorCount === 0 
        ? `Successfully approved all ${approvedCount} deals.`
        : `Approved ${approvedCount} deals. ${errorCount} failed: ${failedDeals.join(', ')}`

      setPersistentToast({
        message,
        type: errorCount === 0 ? "success" : "error",
        details,
        onClose: () => {
          setPersistentToast(null)
          fetchPendingDeals() // Reload when user closes toast
        }
      })

    } catch (error) {
      console.error("Mass approve error:", error)
      setPersistentToast({
        message: "Mass Approval Failed!",
        type: "error",
        details: "Mass approval process failed",
        onClose: () => setPersistentToast(null)
      })
    } finally {
      setMassApproveLoading(false)
    }
  }

  function handleSelectDeal(dealId: string, checked: boolean) {
    const newSelectedDeals = new Set(selectedDeals)
    if (checked) {
      newSelectedDeals.add(dealId)
    } else {
      newSelectedDeals.delete(dealId)
    }
    setSelectedDeals(newSelectedDeals)
  }

  function handleSelectAll() {
    if (selectedDeals.size === filteredDeals.length) {
      // Deselect all
      setSelectedDeals(new Set())
    } else {
      // Select all filtered deals
      setSelectedDeals(new Set(filteredDeals.map(deal => deal.id)))
    }
  }

  const filteredDeals = deals.filter(deal =>
    deal.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    deal.store.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    deal.category.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
      </div>
    )
  }

  if (deals.length === 0) {
    return (
      <div className="text-center py-8">
        <Clock className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No pending deals</h3>
        <p className="text-gray-500">All deals have been reviewed.</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Persistent Toast */}
      {persistentToast && (
        <PersistentToast {...persistentToast} />
      )}

      {/* Search and Bulk Actions */}
      <div className="flex gap-4 items-center">
        <Input
          placeholder="Search deals..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="flex-1"
        />
        <Button
          variant="outline"
          onClick={handleSelectAll}
          disabled={filteredDeals.length === 0}
        >
          {selectedDeals.size === filteredDeals.length && filteredDeals.length > 0 ? (
            <>
              <Square className="w-4 h-4 mr-2" />
              Deselect All
            </>
          ) : (
            <>
              <CheckSquare className="w-4 h-4 mr-2" />
              Select All
            </>
          )}
        </Button>
        {selectedDeals.size > 0 && (
          <>
            <Button
              variant="default"
              onClick={handleMassApprove}
              disabled={massApproveLoading}
              className="min-w-[180px] bg-green-600 hover:bg-green-700"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              {massApproveLoading ? "Approving..." : `Mass Approve (${selectedDeals.size})`}
            </Button>
            <Button
              variant="destructive"
              onClick={handleBulkDelete}
              disabled={bulkDeleteLoading}
              className="min-w-[160px]"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              {bulkDeleteLoading ? "Deleting..." : `Delete Selected (${selectedDeals.size})`}
            </Button>
          </>
        )}
      </div>

      {/* Deals List */}
      <div className="space-y-4">
        {filteredDeals.map((deal) => (
          <Card key={deal.id} className="overflow-hidden">
            <CardContent className="p-6">
              <div className="flex gap-4">
                {/* Checkbox */}
                <div className="flex-shrink-0 flex items-start pt-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSelectDeal(deal.id, !selectedDeals.has(deal.id))}
                    className="h-8 w-8 p-0"
                  >
                    {selectedDeals.has(deal.id) ? (
                      <CheckSquare className="w-5 h-5 text-orange-600" />
                    ) : (
                      <Square className="w-5 h-5 text-gray-400" />
                    )}
                  </Button>
                </div>

                {/* Deal Image */}
                <div className="flex-shrink-0 w-24 h-24 bg-gray-100 rounded-lg overflow-hidden">
                  <img
                    src={getSafeImageUrl(deal.imageUrl)}
                    alt={deal.title}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Deal Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {deal.title}
                      </h3>
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                        {deal.description}
                      </p>

                      {/* Deal Meta */}
                      <div className="flex flex-wrap items-center gap-2 mb-3">
                        <Badge variant="secondary">
                          {deal.store.name}
                        </Badge>
                        <Badge variant="outline">
                          {deal.category.name}
                        </Badge>
                        {deal.couponCode && (
                          <Badge variant="outline">
                            Code: {deal.couponCode}
                          </Badge>
                        )}
                      </div>

                      {/* Price Info */}
                      <div className="flex items-center justify-between gap-2 mb-3">
                        <div className="flex items-center gap-2">
                          <span className="text-2xl font-bold text-orange-600">
                            £{formatPrice(deal.dealPrice)}
                          </span>
                          {deal.originalPrice && (
                            <>
                              <span className="text-lg text-gray-500 line-through">
                                £{formatPrice(deal.originalPrice)}
                              </span>
                              <span className="text-green-600 font-semibold">
                                {deal.discountPercentage}% off
                              </span>
                            </>
                          )}
                        </div>
                        {!deal.originalPrice && (
                          <SetOriginalPrice
                            dealId={parseInt(deal.id, 10)}
                            hasOriginalPrice={!!deal.originalPrice}
                            onSuccess={fetchPendingDeals}
                          />
                        )}
                      </div>

                      {/* Author & Date */}
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <span>Posted by {deal.author.name}</span>
                        <span>•</span>
                        <span>{formatDistanceToNow(new Date(deal.createdAt))} ago</span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex flex-col gap-2">
                      <Button
                        size="sm"
                        onClick={() => handleEnhancedApprove(deal)}
                        disabled={actionLoading === deal.id}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Check className="w-4 h-4 mr-1" />
                        {actionLoading === deal.id ? "Processing..." : "Approve"}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => window.open(deal.dealUrl, "_blank")}
                      >
                        <ExternalLink className="w-4 h-4 mr-1" />
                        View Deal
                      </Button>
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={() => router.push(`/admin/deals/${deal.id}/edit`)}
                      >
                        Edit
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleDeleteDeal(deal.id)}
                        disabled={actionLoading === deal.id}
                      >
                        <Trash2 className="w-4 h-4 mr-1" />
                        Delete
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Progress Bar */}
              {approvalProgress[deal.id] && (
                <div className="mt-4 bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">
                      {approvalProgress[deal.id].currentStep}
                    </span>
                    <span className="text-sm text-gray-500">
                      {approvalProgress[deal.id].step}/{approvalProgress[deal.id].total}
                    </span>
                  </div>
                  
                  {/* Progress Bar */}
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${(approvalProgress[deal.id].step / approvalProgress[deal.id].total) * 100}%` }}
                    ></div>
                  </div>

                  {/* Error Display */}
                  {approvalProgress[deal.id].error && (
                    <div className="text-red-600 text-sm mb-2">
                      {approvalProgress[deal.id].error}
                    </div>
                  )}

                  {/* Logs */}
                  <div className="max-h-40 overflow-y-auto text-xs text-gray-600 space-y-1">
                    {approvalProgress[deal.id].logs.map((log, index) => (
                      <div key={index} className="font-mono">{log}</div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        ))}

        {filteredDeals.length === 0 && deals.length > 0 && (
          <div className="text-center py-8">
            <p className="text-gray-500">No deals found matching your search.</p>
          </div>
        )}
      </div>
    </div>
  )
}

