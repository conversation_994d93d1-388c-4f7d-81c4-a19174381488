import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"
import { EmailService } from "@/lib/email"
import crypto from "crypto"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { newEmail } = await request.json()

    if (!newEmail || typeof newEmail !== 'string') {
      return NextResponse.json({ error: "New email is required" }, { status: 400 })
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(newEmail)) {
      return NextResponse.json({ error: "Invalid email format" }, { status: 400 })
    }

    // Check if new email is same as current
    if (newEmail.toLowerCase() === session.user.email.toLowerCase()) {
      return NextResponse.json({ error: "New email must be different from current email" }, { status: 400 })
    }

    // Check if new email is already taken
    const existingUser = await db.user.findUnique({
      where: { email: newEmail.toLowerCase() }
    })

    if (existingUser) {
      return NextResponse.json({ error: "Email address is already in use" }, { status: 400 })
    }

    // Get current user
    const currentUser = await db.user.findUnique({
      where: { email: session.user.email }
    })

    if (!currentUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Delete any existing pending email change requests for this user
    await db.emailChangeRequest.deleteMany({
      where: { userId: currentUser.id }
    })

    // Generate confirmation token
    const token = crypto.randomBytes(32).toString('hex')
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours

    // Create email change request
    await db.emailChangeRequest.create({
      data: {
        userId: currentUser.id,
        currentEmail: session.user.email,
        newEmail: newEmail.toLowerCase(),
        token,
        expiresAt
      }
    })

    // Send confirmation email to NEW email address
    const confirmationUrl = `${process.env.NEXTAUTH_URL}/auth/confirm-email-change?token=${token}`
    const emailService = EmailService.getInstance()
    
    await emailService.sendEmail({
      to: newEmail,
      subject: "Confirm Your New Email Address - NiceDeals",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #f97316;">Confirm Your New Email Address</h2>
          <p>Hello,</p>
          <p>You have requested to change your email address on NiceDeals from <strong>${session.user.email}</strong> to <strong>${newEmail}</strong>.</p>
          <p>To confirm this change, please click the button below:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${confirmationUrl}" 
               style="background-color: #f97316; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Confirm Email Change
            </a>
          </div>
          <p><strong>Important:</strong> Your current email address will remain active until you confirm this change.</p>
          <p>This link will expire in 24 hours. If you did not request this change, please ignore this email.</p>
          <p>Best regards,<br>The NiceDeals Team</p>
        </div>
      `
    })

    return NextResponse.json({ 
      message: "Confirmation email sent to your new email address. Please check your inbox and click the confirmation link." 
    })

  } catch (error) {
    console.error("Email change request error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
} 