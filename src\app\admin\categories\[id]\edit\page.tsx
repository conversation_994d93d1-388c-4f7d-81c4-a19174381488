import { redirect } from "next/navigation"
import { requireAdmin } from "@/lib/auth"
import { db } from "@/lib/db"
import AdminLayout from "@/components/admin/AdminLayout"
import { getAdminStats } from "@/lib/admin"
import CategoryEditForm from "@/components/admin/CategoryEditForm"

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'

async function getCategory(id: string) {
  const categoryId = parseInt(id, 10)
  if (isNaN(categoryId)) {
    throw new Error('Invalid category ID')
  }
  
  const category = await db.category.findUnique({
    where: { id: categoryId }
  })
  
  if (!category) {
    throw new Error('Category not found')
  }
  
  return {
    id: category.id,
    name: category.name,
    slug: category.slug,
    description: category.description || undefined,
    icon: category.icon || undefined,
    active: category.active,
    dealCount: category.dealCount
  }
}

export default async function EditCategoryPage({ params }: { params: Promise<{ id: string }> }) {
  try {
    await requireAdmin()
  } catch {
    redirect("/auth/signin")
  }

  try {
    const { id } = await params
    const [stats, category] = await Promise.all([
      getAdminStats(),
      getCategory(id)
    ])

    return (
      <AdminLayout stats={stats} currentPage="categories">
        <div className="w-full">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Edit Category</h2>
            <p className="text-gray-600">Update category information and settings</p>
          </div>
          <CategoryEditForm category={category} />
        </div>
      </AdminLayout>
    )
  } catch {
    redirect("/admin/categories")
  }
}
