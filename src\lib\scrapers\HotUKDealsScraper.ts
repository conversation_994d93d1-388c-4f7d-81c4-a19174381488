import { PrismaClient } from '@prisma/client';
import { generateSlug } from '@/lib/slug';
import { addAffiliateTag } from '@/lib/utils';
import fs from 'fs';
import path from 'path';

interface ScrapedDeal {
  title: string;
  description: string;
  dealPrice: number;
  originalPrice?: number;
  dealUrl: string;
  imageUrl?: string;
  storeName: string;
  categoryName: string;
  hukdId: number;
  temperature: number;
  couponCode?: string;
}

interface ScraperResult {
  success: boolean;
  dealsFound: number;
  dealsAdded: number;
  dealsSkipped: number;
  dataFile?: string;
  error?: string;
}

export class HotUKDealsScraper {
  private prisma: PrismaClient;
  private logDir: string;
  private startTime: Date;
  private totalDealsFound: number = 0;
  private dealsAdded: number = 0;
  private skippedDeals: number = 0;
  private readonly SYSTEM_USER_ID = 1; // Admin user ID
  private logId?: number;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.logDir = path.join(process.cwd(), 'scraper-data');
    this.startTime = new Date();

    // Ensure scraper data directory exists
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  async run(): Promise<ScraperResult> {
    console.log('Starting HUKD scraper at:', this.startTime.toISOString());

    try {
      // Insert initial log entry
      const log = await this.prisma.scrapeLog.create({
        data: {
          source: 'hukd',
          status: 'started',
          dealsFound: 0,
          dealsAdded: 0,
          dealsSkipped: 0,
        }
      });
      
      this.logId = log.id;

      // Fetch and process deals
      const deals = await this.fetchDeals();
      
      // Save detailed data to JSON file
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0] + '-' + 
                       new Date().toISOString().replace(/[:.]/g, '-').split('T')[1].split('-')[0];
      const dataFile = `hukd-${timestamp}.json`;
      const dataFilePath = path.join(this.logDir, dataFile);
      
      fs.writeFileSync(dataFilePath, JSON.stringify(deals, null, 2));

      // Update log with results
      await this.prisma.scrapeLog.update({
        where: { id: this.logId },
        data: {
          status: 'completed',
          dealsFound: this.totalDealsFound,
          dealsAdded: this.dealsAdded,
          dealsSkipped: this.skippedDeals,
          dataFile: dataFile,
          completedAt: new Date(),
        }
      });

      console.log(`Scraping completed. Found: ${this.totalDealsFound}, Added: ${this.dealsAdded}`);
      return { 
        success: true, 
        dealsFound: this.totalDealsFound, 
        dealsAdded: this.dealsAdded,
        dealsSkipped: this.skippedDeals,
        dataFile: dataFile
      };

    } catch (error) {
      console.error('Scraper error:', error);
      
      // Update log with error
      if (this.logId) {
        await this.prisma.scrapeLog.update({
          where: { id: this.logId },
          data: {
            status: 'error',
            error: error instanceof Error ? error.message : 'Unknown error',
            completedAt: new Date(),
          }
        });
      }
      
      return { 
        success: false, 
        dealsFound: this.totalDealsFound,
        dealsAdded: this.dealsAdded,
        dealsSkipped: this.skippedDeals,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async fetchDeals(): Promise<ScrapedDeal[]> {
    console.log('Fetching HUKD hot deals page...');
    
    const response = await fetch('https://www.hotukdeals.com/hot', {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Upgrade-Insecure-Requests': '1'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const html = await response.text();
    console.log('Page fetched successfully');

    // Extract Vue.js component data using regex
    const threadDataRegex = /data-vue2='(\{"name":"ThreadMainListItemNormalizer","props":\{"thread":[^']+\}})'/g;
    const deals: ScrapedDeal[] = [];
    let match;

    while ((match = threadDataRegex.exec(html)) !== null) {
      try {
        // Parse the JSON data embedded in the HTML
        const jsonStr = match[1].replace(/&quot;/g, '"');
        const vueData = JSON.parse(jsonStr);
        const currentDealData = vueData.props.thread;
        
        this.totalDealsFound++;
        
        if (!currentDealData || !currentDealData.link) {
          console.log(`Skipping deal: No URL provided`);
          this.skippedDeals++;
          continue;
        }

        // Process the deal data
        const deal = await this.processDeal(currentDealData);
        if (deal) {
          deals.push(deal);
        }

      } catch (error) {
        this.skippedDeals++;
        console.log('Skipping deal: Invalid deal data', error);
      }
    }

    return deals;
  }

  private async processDeal(dealData: any): Promise<ScrapedDeal | null> {
    try {
      const url = new URL(dealData.link);
      const storeName = dealData.merchant?.merchantName || url.hostname.replace('www.', '').split('.')[0];
      const cleanUrl = dealData.link.split('?')[0];
      
      console.log(`Processing deal: ${dealData.title}`);
      
      const scrapedDeal: ScrapedDeal = {
        title: dealData.title,
        description: dealData.description || '',
        dealPrice: parseFloat(dealData.price) || 0,
        originalPrice: parseFloat(dealData.nextBestPrice) || undefined,
        dealUrl: cleanUrl,
        imageUrl: dealData.mainImage ? 
          `https://images.hotukdeals.com/${dealData.mainImage.path}/${dealData.mainImage.uid}` : 
          undefined,
        storeName: storeName,
        temperature: parseFloat(dealData.temperature) || 0,
        categoryName: dealData.mainGroup?.threadGroupName || 'Uncategorized',
        hukdId: parseInt(dealData.threadId),
        couponCode: dealData.voucherCode || undefined,
      };

      // Check if deal already exists
      const existingDeal = await this.prisma.deal.findFirst({
        where: {
          externalId: scrapedDeal.hukdId.toString()
        }
      });

      if (!existingDeal) {
        // Create store if doesn't exist
        const store = await this.findOrCreateStore(scrapedDeal.storeName, scrapedDeal.dealUrl);
        const category = await this.findOrCreateCategory(scrapedDeal.categoryName);

        // Generate unique slug
        const baseSlug = generateSlug(scrapedDeal.title);
        let slug = baseSlug;
        let counter = 1;
        
        while (await this.prisma.deal.findUnique({ where: { slug } })) {
          slug = `${baseSlug}-${counter}`;
          counter++;
        }

        // Add affiliate tag to Amazon URLs before saving
        const finalDealUrl = addAffiliateTag(scrapedDeal.dealUrl, store.id);

        // Insert the deal
        await this.prisma.deal.create({
          data: {
            authorId: this.SYSTEM_USER_ID,
            title: scrapedDeal.title,
            description: scrapedDeal.description,
            slug: slug,
            dealPrice: scrapedDeal.dealPrice,
            originalPrice: scrapedDeal.originalPrice,
            discountPercentage: scrapedDeal.originalPrice ? 
              Math.round(((scrapedDeal.originalPrice - scrapedDeal.dealPrice) / scrapedDeal.originalPrice) * 100) : 
              undefined,
            storeId: store.id,
            categoryId: category.id,
            dealUrl: finalDealUrl,
            imageUrl: scrapedDeal.imageUrl,
            couponCode: scrapedDeal.couponCode,
            source: 'hukd',
            externalId: scrapedDeal.hukdId.toString(),
            temperature: scrapedDeal.temperature,
            status: 'PENDING',
          }
        });

        this.dealsAdded++;
        console.log(`Added new deal: ${scrapedDeal.title}`);
      } else {
        console.log(`Deal already exists: ${scrapedDeal.title}`);
        this.skippedDeals++;
      }

      return scrapedDeal;

    } catch (error) {
      console.log(`Error processing deal: ${error instanceof Error ? error.message : 'Unknown error'}`);
      this.skippedDeals++;
      return null;
    }
  }

  private async findOrCreateStore(storeName: string, storeUrl: string) {
    // Try to find existing store (case insensitive for SQLite)
    let store = await this.prisma.store.findFirst({
      where: {
        name: storeName
      }
    });

    if (!store) {
      // Generate unique slug for store
      const baseSlug = generateSlug(storeName);
      let slug = baseSlug;
      let counter = 1;
      
      while (await this.prisma.store.findUnique({ where: { slug } })) {
        slug = `${baseSlug}-${counter}`;
        counter++;
      }

      // Create new store
      store = await this.prisma.store.create({
        data: {
          name: storeName,
          slug: slug,
          url: storeUrl,
        }
      });
      
      console.log(`Created new store: ${storeName}`);
    }

    return store;
  }

  private async findOrCreateCategory(categoryName: string) {
    // Try to find existing category (case insensitive for SQLite)
    let category = await this.prisma.category.findFirst({
      where: {
        name: categoryName
      }
    });

    if (!category) {
      // Generate unique slug for category
      const baseSlug = generateSlug(categoryName);
      let slug = baseSlug;
      let counter = 1;
      
      while (await this.prisma.category.findUnique({ where: { slug } })) {
        slug = `${baseSlug}-${counter}`;
        counter++;
      }

      // Create new category
      category = await this.prisma.category.create({
        data: {
          name: categoryName,
          slug: slug,
        }
      });
      
      console.log(`Created new category: ${categoryName}`);
    }

    return category;
  }
} 