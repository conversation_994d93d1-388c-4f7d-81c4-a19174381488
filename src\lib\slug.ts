// Utility functions for generating SEO-friendly slugs

export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .trim()
    // Replace spaces and special characters with hyphens
    .replace(/[\s\W-]+/g, '-')
    // Remove multiple consecutive hyphens
    .replace(/-+/g, '-')
    // Remove leading/trailing hyphens
    .replace(/^-+|-+$/g, '')
    // Limit length
    .substring(0, 100)
}

export function generateDealSlug(title: string, store?: string): string {
  // Create base slug from title
  let slug = generateSlug(title)
  
  // Add store if provided for uniqueness
  if (store) {
    const storeSlug = generateSlug(store)
    slug = `${slug}-${storeSlug}`
  }
  
  return slug
}

// NEW: Generate deal slug with ID at the end for SEO-friendly URLs
export function generateDealSlugWithId(title: string, dealId: number): string {
  const baseSlug = generateSlug(title)
  return `${baseSlug}-${dealId}`
}

// NEW: Extract deal ID from slug (for parsing SEO-friendly URLs)
export function extractDealIdFromSlug(slug: string): number | null {
  const lastHyphenIndex = slug.lastIndexOf('-')
  if (lastHyphenIndex === -1) return null
  
  const idPart = slug.substring(lastHyphenIndex + 1)
  const id = parseInt(idPart, 10)
  
  return isNaN(id) ? null : id
}

// NEW: Check if slug is in the new format (with ID at end)
export function isNewFormatSlug(slug: string): boolean {
  const dealId = extractDealIdFromSlug(slug)
  return dealId !== null && dealId > 0
}

// NEW: Convert old slug to new format with deal ID
export function upgradeSlugWithId(oldSlug: string, dealId: number): string {
  // If already in new format, return as-is
  if (isNewFormatSlug(oldSlug)) {
    return oldSlug
  }
  
  // Otherwise append the deal ID
  return `${oldSlug}-${dealId}`
}

export function ensureUniqueSlug(baseSlug: string, existingSlugs: string[]): string {
  let slug = baseSlug
  let counter = 1
  
  while (existingSlugs.includes(slug)) {
    slug = `${baseSlug}-${counter}`
    counter++
  }
  
  return slug
}
