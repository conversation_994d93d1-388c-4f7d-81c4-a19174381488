import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json({ message: 'Email is required' }, { status: 400 });
    }

    const normalizedEmail = email.toLowerCase();

    // Check if user already exists
    const existingUser = await db.user.findUnique({ where: { email: normalizedEmail } });

    if (existingUser) {
      // If already subscribed, just return success
      if (existingUser.newsletter) {
        return NextResponse.json({ message: 'You are already subscribed to the NiceDeals daily update' });
      }

      // If they have a user account, toggle newsletter flag on
      await db.user.update({
        where: { id: existingUser.id },
        data: { newsletter: true },
      });

      return NextResponse.json({ message: "You've been registered for the NiceDeals daily update" });
    }

    // Create newsletter-only user
    await db.user.create({
      data: {
        email: normalizedEmail,
        role: 'NEWS',
        newsletter: true,
      },
    });

    // We could send a confirmation email here if desired.

    return NextResponse.json({ message: "You've been registered for the NiceDeals daily update" }, { status: 201 });
  } catch (error) {
    console.error('Newsletter subscribe error:', error);
    return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
  }
} 