import { NextRequest, NextResponse } from "next/server"
import bcrypt from "bcryptjs"
import { db } from "@/lib/db"
import { emailService } from "@/lib/email"

export async function POST(request: NextRequest) {
  try {
    const { name, email, password } = await request.json()

    // Validate required fields
    if (!name || !email || !password) {
      return NextResponse.json(
        { message: "All fields are required" },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { message: "Invalid email format" },
        { status: 400 }
      )
    }

    // Validate password length
    if (password.length < 6) {
      return NextResponse.json(
        { message: "Password must be at least 6 characters long" },
        { status: 400 }
      )
    }

    // Check if user already exists
    const existingUser = await db.user.findUnique({
      where: { email: email.toLowerCase() },
    })

    if (existingUser) {
      // If the email belongs to a newsletter-only account, upgrade it to a full user
      if (existingUser.role === "NEWS") {
        const hashedPassword = await bcrypt.hash(password, 12)

        const upgradedUser = await db.user.update({
          where: { id: existingUser.id },
          data: {
            name,
            password: hashedPassword,
            role: "USER",
            newsletter: true, // keep their newsletter flag on
          },
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            createdAt: true,
          },
        })

        // Send welcome email
        try {
          await emailService.sendWelcomeEmail(upgradedUser.email, upgradedUser.name || "New User")
        } catch (emailError) {
          console.error("Failed to send welcome email:", emailError)
        }

        return NextResponse.json(
          {
            message: "User upgraded successfully",
            user: upgradedUser,
          },
          { status: 200 },
        )
      }

      // For any other existing users, prevent duplicate registration
      return NextResponse.json(
        { message: "User with this email already exists" },
        { status: 409 },
      )
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12)

    // Create user
    const user = await db.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        role: "USER",
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
      }
    })

    // Send welcome email
    try {
      await emailService.sendWelcomeEmail(user.email, user.name || 'New User')
    } catch (emailError) {
      console.error("Failed to send welcome email:", emailError)
      // Don't fail registration if email fails, just log it
    }

    return NextResponse.json(
      { 
        message: "User created successfully",
        user 
      },
      { status: 201 }
    )
  } catch (error) {
    console.error("Registration error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
