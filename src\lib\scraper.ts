import axios from 'axios';

interface ScrapedContent {
  title: string;
  description: string;
  content: string;
  error?: string;
}

export async function scrapeProductPage(url: string): Promise<ScrapedContent> {
  try {
    // Validate URL
    if (!url || !isValidUrl(url)) {
      throw new Error('Invalid URL provided');
    }

    // Advanced anti-detection headers for Amazon
    const headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
      'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
      'Accept-Encoding': 'gzip, deflate, br',
      'Cache-Control': 'max-age=0',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Sec-Fetch-User': '?1',
      'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
      'Sec-Ch-Ua-Mobile': '?0',
      'Sec-Ch-Ua-Platform': '"Windows"',
      'Upgrade-Insecure-Requests': '1',
      'DNT': '1',
      // Add cookie to bypass consent (simulate accepted cookies)
      'Cookie': 'session-id=147-1234567-1234567; ubid-acbuk=147-1234567-1234567; i18n-prefs=GBP; lc-acbuk=en_GB',
    };

    // Fetch the webpage with timeout
    const response = await axios.get(url, {
      headers,
      timeout: 10000, // 10 second timeout
      maxRedirects: 5,
    });

    const html = response.data;
    
    // Extract basic information
    const result: ScrapedContent = {
      title: extractTitleFromHtml(html),
      description: extractDescriptionFromHtml(html),
      content: extractTextContent(html),
    };

    return result;

  } catch (error) {
    console.error('Scraping error:', error);
    return {
      title: '',
      description: '',
      content: '',
      error: error instanceof Error ? error.message : 'Failed to scrape webpage'
    };
  }
}

function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch {
    return false;
  }
}

function extractTitleFromHtml(html: string): string {
  // Extract title from HTML using regex
  const titleMatches = [
    /<title[^>]*>([^<]+)<\/title>/i,
    /<h1[^>]*>([^<]+)<\/h1>/i,
    /<meta[^>]*property="og:title"[^>]*content="([^"]+)"/i,
    /<meta[^>]*name="title"[^>]*content="([^"]+)"/i,
  ];

  for (const regex of titleMatches) {
    const match = html.match(regex);
    if (match && match[1]) {
      const title = cleanText(match[1]);
      if (title.length > 5) {
        return title;
      }
    }
  }

  return '';
}

function extractDescriptionFromHtml(html: string): string {
  // Extract description from HTML using regex
  const descMatches = [
    /<meta[^>]*name="description"[^>]*content="([^"]+)"/i,
    /<meta[^>]*property="og:description"[^>]*content="([^"]+)"/i,
    /<meta[^>]*name="twitter:description"[^>]*content="([^"]+)"/i,
  ];

  for (const regex of descMatches) {
    const match = html.match(regex);
    if (match && match[1]) {
      const desc = cleanText(match[1]);
      if (desc.length > 20) {
        return desc;
      }
    }
  }

  return '';
}

function extractTextContent(html: string): string {
  // Remove scripts, styles, and cookie consent content
  let content = html
    .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
    .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
    // Remove cookie consent dialog content specifically
    .replace(/Select your cookie preferences[\s\S]*?Try again\./gi, '')
    .replace(/We use cookies and similar tools[\s\S]*?Cookie notice/gi, '')
    .replace(/About this item About this item About this item/gi, 'About this item')
    // Remove navigation and menu content
    .replace(/Shortcuts menu[\s\S]*?shift \+ ALT \+ Z/gi, '')
    .replace(/Delivering to [A-Z0-9\s]+ Update location/gi, '')
    .replace(/Hello, sign in Account & Lists/gi, '')
    // Remove department listings
    .replace(/All Departments[\s\S]*?Toys & Games/gi, '')
    .replace(/<[^>]+>/g, ' ') // Remove all HTML tags
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .trim();

  // Focus on product content by looking for Amazon product patterns
  content = filterForProductContent(content);

  // Limit content length
  if (content.length > 3000) {
    content = content.substring(0, 3000) + '...';
  }

  return content;
}

function filterForProductContent(content: string): string {
  // First, try to extract Amazon's key product sections
  const productSections = extractAmazonProductSections(content);
  if (productSections.length > 50) {
    return productSections;
  }
  
  // Fallback to sentence-based filtering
  const sentences = content.split(/[.!?]+/);
  const productSentences = sentences.filter(sentence => {
    const s = sentence.toLowerCase().trim();
    
    // Skip cookie/legal/navigation content
    if (
      s.includes('cookie') ||
      s.includes('privacy') ||
      s.includes('advertising') ||
      s.includes('account & lists') ||
      s.includes('returns & orders') ||
      s.includes('keyboard shortcuts') ||
      s.includes('department') ||
      s.includes('delivering to') ||
      s.includes('sign in') ||
      s.includes('search amazon') ||
      s.length < 10
    ) {
      return false;
    }
    
    // Prioritize key Amazon product sections
    if (
      s.includes('product description') ||
      s.includes('product details') ||
      s.includes('about this item') ||
      s.includes('specifications') ||
      s.includes('features')
    ) {
      return true;
    }
    
    // Keep sentences that look like product information
    return (
      s.includes('xbox') ||
      s.includes('game') ||
      s.includes('edition') ||
      s.includes('brand') ||
      s.includes('model') ||
      s.includes('reviews') ||
      s.includes('buying') ||
      s.length > 20
    );
  });
  
  return productSentences.join('. ').trim();
}

function extractAmazonProductSections(content: string): string {
  const sections = [];
  
  // Look for "Product description" section
  const productDescMatch = content.match(/Product description[\s\S]*?(?=Product details|Technical Details|Additional Information|Customer reviews|$)/i);
  if (productDescMatch) {
    sections.push('PRODUCT DESCRIPTION: ' + productDescMatch[0].replace(/Product description/i, '').trim());
  }
  
  // Look for "Product details" section  
  const productDetailsMatch = content.match(/Product details[\s\S]*?(?=Technical Details|Additional Information|Customer reviews|$)/i);
  if (productDetailsMatch) {
    sections.push('PRODUCT DETAILS: ' + productDetailsMatch[0].replace(/Product details/i, '').trim());
  }
  
  // Look for "About this item" section
  const aboutItemMatch = content.match(/About this item[\s\S]*?(?=Product description|Product details|Technical Details|Customer reviews|$)/i);
  if (aboutItemMatch) {
    sections.push('ABOUT THIS ITEM: ' + aboutItemMatch[0].replace(/About this item/i, '').trim());
  }
  
  // Look for "Technical Details" or specifications
  const techDetailsMatch = content.match(/Technical Details[\s\S]*?(?=Additional Information|Customer reviews|$)/i);
  if (techDetailsMatch) {
    sections.push('TECHNICAL DETAILS: ' + techDetailsMatch[0].replace(/Technical Details/i, '').trim());
  }
  
  return sections.join('\n\n').trim();
}

function cleanText(text: string): string {
  return text
    .replace(/&quot;/g, '"')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&nbsp;/g, ' ')
    .replace(/\s+/g, ' ')
    .replace(/[\r\n\t]/g, ' ')
    .trim();
}

export function formatScrapedContentForAI(content: ScrapedContent, title: string, url: string): string {
  const parts = [];
  
  parts.push(`ORIGINAL TITLE: ${title}`);
  parts.push(`PRODUCT URL: ${url}`);
  
  if (content.title && content.title !== title) {
    parts.push(`WEBPAGE TITLE: ${content.title}`);
  }
  
  if (content.description) {
    parts.push(`WEBPAGE DESCRIPTION: ${content.description}`);
  }
  
  if (content.content) {
    parts.push(`WEBPAGE CONTENT: ${content.content}`);
  }
  
  if (content.error) {
    parts.push(`SCRAPING ERROR: ${content.error}`);
  }
  
  return parts.join('\n\n');
} 