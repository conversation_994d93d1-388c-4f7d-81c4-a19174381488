import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { db } from "@/lib/db";
import { getAdminStats } from "@/lib/admin";

export async function GET() {
  try {
    // Check authentication and admin role
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await db.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user || user.role !== "ADMIN") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Fetch admin stats
    const stats = await getAdminStats();

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('[ADMIN STATS API] Error:', error);
    return NextResponse.json({
      success: false,
      error: "Failed to fetch admin stats",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
} 