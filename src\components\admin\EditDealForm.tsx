"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "react-hot-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>les, FileText, Trash2 } from "lucide-react";
import { getSafeImageUrl } from "@/lib/utils";
import { generateDealSlugWithId } from "@/lib/slug";

const dealFormSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  price: z.string().refine((val) => !isNaN(parseFloat(val)) && parseFloat(val) >= 0, "Price must be a positive number"),
  originalPrice: z.string().refine((val) => val === "" || (!isNaN(parseFloat(val)) && parseFloat(val) >= 0), "Original price must be a positive number").optional(),
  url: z.string().url("Must be a valid URL"),
  status: z.enum(["ACTIVE", "PENDING", "EXPIRED", "REJECTED"]),
  storeId: z.string(),
  categoryId: z.string(),
  coupon: z.string().max(50, "Coupon code must be at most 50 characters").optional(),
  imageUrl: z.string().min(1, "Image URL is required"),
});

const dealSchema = dealFormSchema.transform((data) => ({
  ...data,
  storeId: parseInt(data.storeId, 10),
  categoryId: parseInt(data.categoryId, 10),
}));

type DealFormData = z.infer<typeof dealFormSchema>;

export default function EditDealForm({ dealId }: { dealId: string }) {
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(true);
  const [categories, setCategories] = useState<{id: string; name: string}[]>([]);
  const [stores, setStores] = useState<{id: string; name: string}[]>([]);
  const [isImprovingTitle, setIsImprovingTitle] = useState(false);
  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);
  const [isProcessingImage, setIsProcessingImage] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [additionalImages, setAdditionalImages] = useState<{id: string; image_url: string; alt_text?: string; caption?: string}[]>([]);
  const [isLoadingImages, setIsLoadingImages] = useState(true);

  const { register, handleSubmit, reset, setValue, watch, getValues, formState: { errors, isSubmitting } } = useForm<DealFormData>({
    resolver: zodResolver(dealFormSchema),
  });

  // Fetch deal, categories, stores
  // Fetch additional images for the deal
  const fetchAdditionalImages = async (dealId: string) => {
    try {
      setIsLoadingImages(true);
      const response = await fetch(`/api/admin/deal-images?dealId=${dealId}`);
      
      if (response.status === 404) {
        // No images found, set empty array and return
        setAdditionalImages([]);
        return;
      }
      
      if (!response.ok) {
        throw new Error(`Failed to fetch additional images: ${response.statusText}`);
      }
      
      const data = await response.json();
      setAdditionalImages(data.images || []);
    } catch (error) {
      console.error("Error fetching additional images:", error);
      // Don't show error toast for 404 as it's an expected case
      if (!(error instanceof Error && error.message.includes('404'))) {
        toast.error("Failed to load additional images");
      }
      setAdditionalImages([]);
    } finally {
      setIsLoadingImages(false);
    }
  };

  useEffect(() => {
    async function fetchData() {
      setIsLoading(true);
      try {
        const [dealRes, catRes, storeRes] = await Promise.all([
          fetch(`/api/admin/deals?id=${dealId}`),
          fetch("/api/admin/categories"),
          fetch("/api/admin/stores"),
        ]);
        if (!dealRes.ok) throw new Error("Failed to fetch deal");
        if (!catRes.ok) throw new Error("Failed to fetch categories");
        if (!storeRes.ok) throw new Error("Failed to fetch stores");
        
        const dealResponse = await dealRes.json();
        const dealData = dealResponse.deal || dealResponse.deals?.[0];
        const categoriesData = (await catRes.json()).categories || [];
        const storesData = (await storeRes.json()).stores || [];
        setCategories(categoriesData);
        setStores(storesData);
        if (dealData) {
          // Use the foreign key IDs directly from the deal data
          reset({
            title: dealData.title || "",
            description: dealData.description || "",
            price: dealData.dealPrice ? parseFloat(dealData.dealPrice.toString()).toFixed(2) : "0.00",
            originalPrice: dealData.originalPrice ? parseFloat(dealData.originalPrice.toString()).toFixed(2) : "",
            url: dealData.dealUrl || "",
            status: (dealData.status || "PENDING").toUpperCase(),
            storeId: (dealData.storeId || dealData.store?.id || "").toString(),
            categoryId: (dealData.categoryId || dealData.category?.id || "").toString(),
            coupon: dealData.couponCode || "",
            imageUrl: dealData.imageUrl || "",
          });
        }
        // Fetch additional images if this is an edit
        if (dealId !== "new") {
          await fetchAdditionalImages(dealId);
        }
      } catch (err: unknown) {
        console.error("Error in fetchData:", err);
        const errorMessage = err instanceof Error ? err.message : "Failed to load data";
        console.error("Error details:", {
          message: errorMessage,
          dealId: dealId
        });
        toast.error(errorMessage);
        // Don't redirect immediately, let's see the error first
        // router.push("/admin/deals");
      } finally {
        setIsLoading(false);
      }
    }
    fetchData();
    // eslint-disable-next-line
  }, [dealId, reset]);

  // AI Title Improver
  const handleImproveTitle = async () => {
    const currentTitle = getValues("title");
    if (!currentTitle) {
      toast.error("Please enter a title first");
      return;
    }
    try {
      setIsImprovingTitle(true);
      const res = await fetch("/api/admin/ai/improve-title", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ title: currentTitle }),
      });
      const data = await res.json();
      if (data.success && data.title) {
        const improvedTitle = data.title;
        setValue("title", improvedTitle);
        
        // Regenerate slug if title changed
        if (improvedTitle !== currentTitle) {
          const newSlug = generateDealSlugWithId(improvedTitle, parseInt(dealId, 10));
          
          // Update the slug in the database immediately
          try {
            const slugUpdateRes = await fetch(`/api/admin/deals?id=${dealId}`, {
              method: "PUT",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ slug: newSlug }),
            });
            
            if (slugUpdateRes.ok) {
              toast.success("Title improved and slug updated!");
            } else {
              toast.success("Title improved! (Note: Slug will be updated when you save)");
            }
          } catch {
            toast.success("Title improved! (Note: Slug will be updated when you save)");
          }
        } else {
          toast.success("Title improved!");
        }
      } else {
        toast.error("Could not improve title");
      }
    } catch {
      toast.error("Failed to improve title");
    } finally {
      setIsImprovingTitle(false);
    }
  };

  // AI Description Generator
  const handleGenerateDescription = async () => {
    const currentTitle = getValues("title");
    const currentUrl = getValues("url");
    if (!currentTitle || !currentUrl) {
      toast.error("Please enter a title and URL first");
      return;
    }
    try {
      setIsGeneratingDescription(true);
      const res = await fetch("/api/admin/ai/improve-description", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ title: currentTitle, url: currentUrl }),
      });
      const data = await res.json();
      if (data.success && data.description) {
        setValue("description", data.description);
        toast.success("Description generated!");
      } else {
        toast.error("Could not generate description");
      }
    } catch {
      toast.error("Failed to generate description");
    } finally {
      setIsGeneratingDescription(false);
    }
  };

  // Image Localization
  const handleLocalizeImage = async () => {
    const imageUrl = getValues("imageUrl");
    if (!imageUrl) {
      toast.error("Please enter an image URL first");
      return;
    }
    
    try {
      setIsProcessingImage(true);
      
      // Use the new API endpoint to localize external images
      const res = await fetch("/api/images/upload", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          localizeUrl: imageUrl,
          dealId: dealId
        }),
      });
      
      const data = await res.json();
      if (data.success && data.images?.[0]?.url) {
        const newImageUrl = data.images[0].url;
        
        // Update only the image URL in the database using direct SQL
        const updateResponse = await fetch(`/api/admin/execute-sql`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            sql: "UPDATE deals SET image_url = ?, thumbnail_url = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
            params: [newImageUrl, newImageUrl, dealId]
          }),
        });
        
        const result = await updateResponse.json();
        
        if (!updateResponse.ok || !result.success) {
          throw new Error(result.error || "Failed to update deal with new image URL");
        }
        
        // Update the form field with proper options to trigger re-render
        setValue("imageUrl", newImageUrl, { 
          shouldValidate: true, 
          shouldDirty: true, 
          shouldTouch: true 
        });
        
        toast.success("Image localized and saved successfully");
      } else {
        throw new Error(data.message || "Could not localize image");
      }
    } catch (error: unknown) {
      console.error("Error localizing image:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to localize image";
      toast.error(errorMessage);
    } finally {
      setIsProcessingImage(false);
    }
  };

  // Save Deal
  const onSubmit = async (data: DealFormData) => {
    try {
      // Transform the form data using the schema
      const transformedData = dealSchema.parse(data);
      
      const res = await fetch(`/api/admin/deals?id=${dealId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(transformedData),
      });
      if (res.ok) {
        toast.success("Deal updated successfully");
        router.push("/admin/deals");
      } else {
        toast.error("Failed to update deal");
      }
    } catch {
      toast.error("Failed to update deal");
    }
  };

  // Delete Deal
  const handleDelete = async () => {
    try {
      const res = await fetch(`/api/admin/deals?id=${dealId}`, {
        method: "DELETE",
      });
      if (res.ok) {
        toast.success("Deal deleted successfully");
        router.push("/admin/deals");
      } else {
        toast.error("Failed to delete deal");
      }
    } catch {
      toast.error("Failed to delete deal");
    }
  };

  // Watch image URL for preview
  const imageUrl = watch("imageUrl");
  const isLocalImage = imageUrl?.startsWith("/uploads/");

  return (
    <div className="py-8">
      <h1 className="text-2xl font-bold mb-6">Edit Deal</h1>
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500"></div>
        </div>
      ) : (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 bg-white shadow rounded-lg p-8 mx-auto">
          {/* Title */}
          <div className="flex gap-2 items-end">
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1">Title</label>
              <Input {...register("title")} />
              {errors.title && <p className="text-red-600 text-xs">{errors.title.message}</p>}
            </div>
            <Button type="button" onClick={handleImproveTitle} disabled={isImprovingTitle}>
              <Sparkles className="w-4 h-4 mr-1" />
              {isImprovingTitle ? "Improving..." : "Improve Title"}
            </Button>
          </div>
          {/* Description */}
          <div className="flex gap-2 items-end">
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1">Description</label>
              <textarea {...register("description")} rows={4} className="w-full rounded-md border border-gray-300 p-5"/>
              {errors.description && <p className="text-red-600 text-xs">{errors.description.message}</p>}
            </div>
            <Button type="button" onClick={handleGenerateDescription} disabled={isGeneratingDescription}>
              <FileText className="w-4 h-4 mr-1" />
              {isGeneratingDescription ? "Generating..." : "Grab a Description"}
            </Button>
          </div>
          {/* Price */}
          <div className="flex gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Price (£)</label>
              <Input type="number" step="0.01" {...register("price")} />
              {errors.price && <p className="text-red-600 text-xs">{errors.price.message}</p>}
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Original Price (£)</label>
              <Input type="number" step="0.01" {...register("originalPrice")} />
              {errors.originalPrice && <p className="text-red-600 text-xs">{errors.originalPrice.message}</p>}
            </div>
          </div>
          {/* URL */}
          <div>
            <label className="block text-sm font-medium mb-1">Deal URL</label>
            <Input {...register("url")} />
            {errors.url && <p className="text-red-600 text-xs">{errors.url.message}</p>}
          </div>
          {/* Coupon */}
          <div>
            <label className="block text-sm font-medium mb-1">Coupon Code</label>
            <Input {...register("coupon")} />
            {errors.coupon && <p className="text-red-600 text-xs">{errors.coupon.message}</p>}
          </div>
          {/* Category & Store */}
          <div className="flex gap-4">
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1">Category</label>
              <select
                {...register("categoryId")}
                className="w-full rounded-md border border-gray-300 px-3 py-2"
                defaultValue={getValues("categoryId")}
              >
                <option value="">Select a category</option>
                {categories.map((cat: { id: string; name: string }) => (
                  <option key={cat.id} value={cat.id}>{cat.name}</option>
                ))}
              </select>
              {errors.categoryId && <p className="text-red-600 text-xs">{errors.categoryId.message}</p>}
            </div>
            <div className="flex-1">
              <label className="block text-sm font-medium mb-1">Store</label>
              <select
                {...register("storeId")}
                className="w-full rounded-md border border-gray-300 px-3 py-2"
                defaultValue={getValues("storeId")}
              >
                <option value="">Select a store</option>
                {stores.map((store: { id: string; name: string }) => (
                  <option key={store.id} value={store.id}>{store.name}</option>
                ))}
              </select>
              {errors.storeId && <p className="text-red-600 text-xs">{errors.storeId.message}</p>}
            </div>
          </div>
          {/* Status */}
          <div>
            <label className="block text-sm font-medium mb-1">Status</label>
<select {...register("status")} className="w-full rounded-md border border-gray-300 px-3 py-2">
              <option value="PENDING">Pending</option>
              <option value="ACTIVE">Active</option>
              <option value="EXPIRED">Expired</option>
              <option value="REJECTED">Rejected</option>
            </select>
            {errors.status && <p className="text-red-600 text-xs">{errors.status.message}</p>}
          </div>
          {/* Image URL & Localization */}
          <div>
            <label className="block text-sm font-medium mb-1">Image URL</label>
            <div className="flex gap-2">
              <Input {...register("imageUrl")} className="flex-1" />
              <Button 
                type="button" 
                onClick={handleLocalizeImage} 
                disabled={isProcessingImage}
                variant="outline"
              >
                {isProcessingImage ? "Processing..." : "Localize"}
              </Button>
            </div>
            {errors.imageUrl && <p className="text-red-600 text-xs">{errors.imageUrl.message}</p>}
            
            {/* Thumbnail Preview */}
            {isLocalImage && (
              <div className="mt-4">
                <div className="border rounded p-4 bg-gray-50">
                  <h4 className="text-sm font-medium mb-2">Thumbnail Preview</h4>
                  <div className="flex justify-center">
                    <img
                      src={`/uploads/deals/thumbnails/${imageUrl.split("/").slice(-1)[0].replace(/^main_|^gallery_/, `deal_${dealId}_`)}`}
                      alt="Deal thumbnail"
                      className="max-h-48 w-auto rounded border"
                    />
                  </div>
                  <div className="mt-2 text-xs text-gray-500 break-all">
                    {imageUrl}
                  </div>
                </div>
              </div>
            )}
            
            {/* Additional Images */}
            {!isLoadingImages && additionalImages.length > 0 && (
              <div className="mt-6">
                <h4 className="text-sm font-medium mb-2">Additional Images</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {additionalImages.map((img) => (
                    <div key={img.id} className="border rounded p-2 bg-white">
                      <img 
                        src={getSafeImageUrl(img.image_url)} 
                        alt={img.alt_text || 'Additional image'} 
                        className="w-full h-32 object-cover rounded"
                      />
                      {img.caption && (
                        <p className="text-xs text-gray-600 mt-1 truncate">{img.caption}</p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          {/* Actions */}
          <div className="flex justify-between pt-4 border-t border-gray-200">
            <Button type="button" variant="destructive" onClick={() => setShowDeleteModal(true)}>
              <Trash2 className="w-4 h-4 mr-1" />
              Delete Deal
            </Button>
            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={() => router.push("/admin/deals")}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </div>
          {/* Delete Modal */}
          {showDeleteModal && (
            <div className="fixed inset-0 z-10 flex items-center justify-center bg-black bg-opacity-40">
              <div className="bg-white rounded shadow-sm p-6 max-w-sm w-full">
                <h2 className="text-lg font-semibold mb-4">Delete Deal</h2>
                <p className="mb-4">Are you sure you want to delete this deal? This action cannot be undone.</p>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowDeleteModal(false)}>
                    Cancel
                  </Button>
                  <Button variant="destructive" onClick={handleDelete}>
                    Delete
                  </Button>
                </div>
              </div>
            </div>
          )}
        </form>
      )}
    </div>
  );
}
