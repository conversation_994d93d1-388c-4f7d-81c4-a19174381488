// Simple in-memory cache for performance optimization
// In production, you would use Redis or similar

interface CacheItem {
  data: any
  timestamp: number
  ttl: number
}

class MemoryCache {
  private cache: Map<string, CacheItem> = new Map()

  set(key: string, data: any, ttlInSeconds: number = 300): void {
    const item: CacheItem = {
      data,
      timestamp: Date.now(),
      ttl: ttlInSeconds * 1000,
    }
    this.cache.set(key, item)
  }

  get(key: string): any | null {
    const item = this.cache.get(key)
    if (!item) return null

    const now = Date.now()
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return null
    }

    return item.data
  }

  delete(key: string): void {
    this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  // Clean expired items
  cleanup(): void {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key)
      }
    }
  }
}

export const cache = new MemoryCache()

// Cleanup expired items every 5 minutes
if (typeof window === 'undefined') {
  setInterval(() => {
    cache.cleanup()
  }, 5 * 60 * 1000)
}

// Cache utility functions
export function getCachedData<T>(key: string): T | null {
  return cache.get(key)
}

export function setCachedData<T>(key: string, data: T, ttlInSeconds: number = 300): void {
  cache.set(key, data, ttlInSeconds)
}

export function invalidateCache(pattern?: string): void {
  if (pattern) {
    // Delete keys matching pattern
    for (const key of cache['cache'].keys()) {
      if (key.includes(pattern)) {
        cache.delete(key)
      }
    }
  } else {
    cache.clear()
  }
}

// Common cache keys
export const CACHE_KEYS = {
  DEALS_BROWSE: 'deals:browse',
  DEALS_FEATURED: 'deals:featured',
  DEALS_HOT: 'deals:hot',
  CATEGORIES: 'categories:all',
  STORES: 'stores:all',
  STATS: 'stats:homepage',
  SEARCH: 'search',
} as const

// Cache TTL in seconds
export const CACHE_TTL = {
  SHORT: 60, // 1 minute
  MEDIUM: 300, // 5 minutes
  LONG: 1800, // 30 minutes
  VERY_LONG: 3600, // 1 hour
} as const
