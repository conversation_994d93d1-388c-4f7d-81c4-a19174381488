"use client"

import { useEffect, useState } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"

export default function ConfirmEmailChangeContent() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'expired'>('loading')
  const [message, setMessage] = useState('')
  const [newEmail, setNewEmail] = useState('')
  const searchParams = useSearchParams()
  const router = useRouter()

  useEffect(() => {
    const token = searchParams.get('token')
    
    if (!token) {
      setStatus('error')
      setMessage('Invalid confirmation link.')
      return
    }

    confirmEmailChange(token)
  }, [searchParams])

  const confirmEmailChange = async (token: string) => {
    try {
      const response = await fetch('/api/user/email/confirm', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token })
      })

      const data = await response.json()

      if (response.ok) {
        setStatus('success')
        setMessage(data.message)
        setNewEmail(data.newEmail)
        
        // Redirect to dashboard after 3 seconds
        setTimeout(() => {
          router.push('/dashboard?tab=settings')
        }, 3000)
      } else {
        if (data.error?.includes('expired')) {
          setStatus('expired')
        } else {
          setStatus('error')
        }
        setMessage(data.error || 'Failed to confirm email change')
      }
    } catch {
      setStatus('error')
      setMessage('An error occurred while confirming your email change.')
    }
  }

  if (status === 'loading') {
    return (
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4"></div>
        <p className="text-gray-600">Confirming your email change...</p>
      </div>
    )
  }

  if (status === 'success') {
    return (
      <div className="text-center">
        <div className="w-12 h-12 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
          <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Email Changed Successfully!</h3>
        <p className="text-gray-600 mb-4">
          Your email address has been updated to <strong>{newEmail}</strong>
        </p>
        <p className="text-sm text-gray-500 mb-6">
          You will be redirected to your dashboard in a few seconds...
        </p>
        <Button asChild>
          <Link href="/dashboard?tab=settings">
            Go to Dashboard
          </Link>
        </Button>
      </div>
    )
  }

  if (status === 'expired') {
    return (
      <div className="text-center">
        <div className="w-12 h-12 mx-auto mb-4 bg-yellow-100 rounded-full flex items-center justify-center">
          <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Link Expired</h3>
        <p className="text-gray-600 mb-6">{message}</p>
        <div className="space-y-3">
          <Button asChild className="w-full">
            <Link href="/dashboard?tab=settings">
              Request New Email Change
            </Link>
          </Button>
          <Button variant="outline" asChild className="w-full">
            <Link href="/dashboard">
              Back to Dashboard
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="text-center">
      <div className="w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
        <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">Confirmation Failed</h3>
      <p className="text-gray-600 mb-6">{message}</p>
      <Button variant="outline" asChild>
        <Link href="/dashboard">
          Back to Dashboard
        </Link>
      </Button>
    </div>
  )
} 