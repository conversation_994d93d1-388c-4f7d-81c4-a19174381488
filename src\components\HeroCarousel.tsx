"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"

interface CarouselSlide {
  id: number
  title: string
  subtitle: string
  description: string
  category: string
  categorySlug: string
  backgroundImage: string
  textColor: string
  buttonText: string
}

const slides: CarouselSlide[] = [
  {
    id: 1,
    title: "Tech & Electronics",
    subtitle: "Latest Gadgets, Lowest Prices",
    description: "Discover cutting-edge smartphones, laptops, headphones and more with unbeatable discounts from top retailers.",
    category: "Electronics",
    categorySlug: "electronics",
    backgroundImage: "linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.6)), url('https://images.unsplash.com/photo-1468495244123-6c6c332eeece?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80')",
    textColor: "text-white",
    buttonText: "Shop Electronics"
  },
  {
    id: 2,
    title: "Fashion & Style",
    subtitle: "Trendy Deals, Timeless Savings",
    description: "From designer clothing to everyday essentials, find the best fashion deals and express your unique style for less.",
    category: "Fashion",
    categorySlug: "clothing-accessories",
    backgroundImage: "linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80')",
    textColor: "text-white",
    buttonText: "Explore Fashion"
  },
  {
    id: 3,
    title: "Home & Garden",
    subtitle: "Transform Your Space",
    description: "Create your dream home with amazing deals on furniture, decor, appliances, and garden essentials.",
    category: "Home & Garden",
    categorySlug: "home-garden",
    backgroundImage: "linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-*************-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80')",
    textColor: "text-white",
    buttonText: "Shop Home"
  },
  {
    id: 4,
    title: "Entertainment",
    subtitle: "Level Up Your Experience",
    description: "Get the latest games, consoles, streaming devices, and entertainment gear at prices that won't break the bank.",
    category: "Gaming",
    categorySlug: "gaming",
    backgroundImage: "linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.6)), url('https://images.unsplash.com/photo-**********-adc38448a05e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2340&q=80')",
    textColor: "text-white",
    buttonText: "Game On"
  }
]

export default function HeroCarousel() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const [isMounted, setIsMounted] = useState(false)

  // Ensure component is mounted before running client-side effects
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Auto-advance slides
  useEffect(() => {
    if (!isAutoPlaying || !isMounted) return

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, 5000) // Change slide every 5 seconds

    return () => clearInterval(interval)
  }, [isAutoPlaying, isMounted])

  const goToSlide = (index: number) => {
    setCurrentSlide(index)
    setIsAutoPlaying(false)
    // Resume auto-play after 10 seconds
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length)
    setIsAutoPlaying(false)
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
    setIsAutoPlaying(false)
    setTimeout(() => setIsAutoPlaying(true), 10000)
  }

  return (
    <section className="relative h-[400px] overflow-hidden">
      {/* Slides */}
      <div className="relative h-full">
        {slides.map((slide, index) => (
          <div
            key={slide.id}
            className={`absolute inset-0 transition-opacity duration-1000 bg-cover bg-center bg-no-repeat ${
              index === currentSlide ? "opacity-100" : "opacity-0"
            }`}
            style={{ 
              backgroundImage: slide.backgroundImage,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat'
            }}
          >
            <div className="container mx-auto px-4 h-full flex items-center">
              <div className="max-w-2xl">
                <div className="mb-4">
                  <span className="inline-block bg-white/20 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium">
                    {slide.category}
                  </span>
                </div>
                
                <h1 className={`text-5xl md:text-7xl font-serif font-bold mb-4 ${slide.textColor}`}>
                  {slide.title}
                </h1>
                
                <h2 className={`text-2xl md:text-3xl font-medium mb-6 ${slide.textColor} opacity-90`}>
                  {slide.subtitle}
                </h2>
                
                <p className={`text-lg md:text-xl mb-8 ${slide.textColor} opacity-80 leading-relaxed`}>
                  {slide.description}
                </p>
                
                <div className="flex flex-row gap-3">
                  <Button asChild size="sm" className="bg-orange-600 text-white hover:bg-orange-700 flex-1 sm:flex-none sm:size-lg">
                    <Link href={`/categories/${slide.categorySlug}`}>
                      {slide.buttonText}
                    </Link>
                  </Button>
                  <Button asChild variant="outline" size="sm" className="border-white text-white bg-transparent hover:bg-white/10 hover:text-white flex-1 sm:flex-none sm:size-lg">
                    <Link href="/browse">
                      Browse All Deals
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Navigation Arrows */}
      <button
        onClick={prevSlide}
        className="absolute left-4 top-1/2 -translate-y-1/2 p-2 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors"
        aria-label="Previous slide"
      >
        <ChevronLeft className="w-6 h-6" />
      </button>
      
      <button
        onClick={nextSlide}
        className="absolute right-4 top-1/2 -translate-y-1/2 p-2 bg-white/20 backdrop-blur-sm rounded-full text-white hover:bg-white/30 transition-colors"
        aria-label="Next slide"
      >
        <ChevronRight className="w-6 h-6" />
      </button>

      {/* Slide Indicators */}
      <div className="absolute bottom-6 left-1/2 -translate-x-1/2 flex space-x-3">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-3 h-3 rounded-full transition-all ${
              index === currentSlide
                ? "bg-white scale-125"
                : "bg-white/50 hover:bg-white/75"
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>

      {/* Progress Bar */}
      {isAutoPlaying && isMounted && (
        <div className="absolute bottom-0 left-0 w-full h-1 bg-white/20">
          <div 
            className="h-full bg-white animate-progress"
          />
        </div>
      )}
    </section>
  )
}
