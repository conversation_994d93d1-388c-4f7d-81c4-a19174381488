"use client"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { toast } from "react-hot-toast"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Save, Trash2, Store, Star } from "lucide-react"
import Link from "next/link"

interface StoreData {
  id: number
  name: string
  slug: string
  url: string
  logoUrl?: string
  description?: string
  active: boolean
  featured: boolean
  dealCount: number
  averageDiscount: number
}

interface StoreEditFormProps {
  store: StoreData
}

export default function StoreEditForm({ store }: StoreEditFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: store.name,
    url: store.url,
    description: store.description || "",
    logoUrl: store.logoUrl || "",
    active: store.active,
    featured: store.featured
  })

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch(`/api/admin/stores/${store.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        toast.success("Store updated successfully!")
        // Store updated successfully - staying on edit page
      } else {
        const error = await response.json()
        toast.error(error.error || "Failed to update store")
      }
    } catch (error) {
      console.error("Error updating store:", error)
      toast.error("Failed to update store")
    } finally {
      setLoading(false)
    }
  }

  async function handleDelete() {
    if (!confirm("Are you sure you want to delete this store? This action cannot be undone.")) {
      return
    }

    if (store.dealCount > 0) {
      toast.error("Cannot delete store with existing deals")
      return
    }

    setDeleteLoading(true)

    try {
      const response = await fetch(`/api/admin/stores/${store.id}`, {
        method: "DELETE",
      })

      if (response.ok) {
        toast.success("Store deleted successfully!")
        router.push("/admin/stores")
        router.refresh()
      } else {
        const error = await response.json()
        toast.error(error.error || "Failed to delete store")
      }
    } catch (error) {
      console.error("Error deleting store:", error)
      toast.error("Failed to delete store")
    } finally {
      setDeleteLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Link href="/admin/stores">
          <Button variant="outline" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Stores
          </Button>
        </Link>
        
        <div className="flex items-center gap-2">
          <Badge variant={store.active ? "default" : "secondary"}>
            {store.active ? "Active" : "Inactive"}
          </Badge>
          {store.featured && (
            <Badge className="bg-yellow-500">
              <Star className="w-3 h-3 mr-1" />
              Featured
            </Badge>
          )}
          <Badge variant="outline">
            {store.dealCount} deals
          </Badge>
        </div>
      </div>

      {/* Edit Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Store className="w-5 h-5" />
            Edit Store
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Store Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  placeholder="Store name"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="url">Website URL *</Label>
                <Input
                  id="url"
                  value={formData.url}
                  onChange={(e) => setFormData({...formData, url: e.target.value})}
                  placeholder="https://example.com"
                  required
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                placeholder="Store description"
              />
            </div>

            <div>
              <Label htmlFor="logoUrl">Logo URL</Label>
              <Input
                id="logoUrl"
                value={formData.logoUrl}
                onChange={(e) => setFormData({...formData, logoUrl: e.target.value})}
                placeholder="https://example.com/logo.png"
              />
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="active"
                  checked={formData.active}
                  onChange={(e) => setFormData({...formData, active: e.target.checked})}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="active">Active</Label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="featured"
                  checked={formData.featured}
                  onChange={(e) => setFormData({...formData, featured: e.target.checked})}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="featured">Featured</Label>
              </div>
            </div>

            <div className="flex justify-between pt-4">
              <Button
                type="button"
                variant="destructive"
                onClick={handleDelete}
                disabled={deleteLoading || store.dealCount > 0}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                {deleteLoading ? "Deleting..." : "Delete Store"}
              </Button>

              <Button type="submit" disabled={loading}>
                <Save className="w-4 h-4 mr-2" />
                {loading ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Store Info */}
      <Card>
        <CardHeader>
          <CardTitle>Store Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div>
            <span className="font-medium">Slug:</span> <code className="bg-gray-100 px-2 py-1 rounded text-sm">{store.slug}</code>
          </div>
          <div>
            <span className="font-medium">Deal Count:</span> {store.dealCount}
          </div>
          <div>
            <span className="font-medium">Average Discount:</span> {store.averageDiscount > 0 ? `${store.averageDiscount.toFixed(1)}%` : "No data"}
          </div>
          <div>
            <span className="font-medium">Status:</span> {store.active ? "Active" : "Inactive"}
          </div>
          <div>
            <span className="font-medium">Featured:</span> {store.featured ? "Yes" : "No"}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
