import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { subscribed } = await request.json()

    if (typeof subscribed !== 'boolean') {
      return NextResponse.json({ error: "Subscribed must be a boolean" }, { status: 400 })
    }

    // Update newsletter subscription
    await db.user.update({
      where: { email: session.user.email },
      data: { newsletter: subscribed }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Newsletter update error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
} 