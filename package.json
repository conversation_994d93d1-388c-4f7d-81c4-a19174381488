{"name": "nicedeals-app", "version": "0.1.0", "private": true, "scripts": {"dev": "dotenv -e .env.dev -- next dev -p 3010", "db:push:dev": "dotenv -e .env.dev -- prisma db push", "db:studio:dev": "dotenv -e .env.dev -- prisma studio", "db:generate:dev": "dotenv -e .env.dev -- prisma generate", "db:migrate:dev": "dotenv -e .env.dev -- prisma migrate dev", "db:add-random-upvotes:dev": "dotenv -e .env.dev -- node scripts/add_random_upvotes.js", "db:create-dummy-users:dev": "dotenv -e .env.dev -- node scripts/create_dummy_users.js", "db:migrate-slugs:dev": "dotenv -e .env.dev -- node scripts/migrate-slugs.js", "build": "next build", "start": "next start -p 3010", "lint": "next lint", "db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:add-random-upvotes": "node scripts/add_random_upvotes.js", "db:create-dummy-users": "node scripts/create_dummy_users.js", "db:migrate-slugs": "node scripts/migrate-slugs.js"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.19.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.12", "@react-email/components": "^0.1.1", "@types/bcryptjs": "^2.4.6", "@types/cheerio": "^0.22.35", "@types/uuid": "^10.0.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "cheerio": "^1.1.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "critters": "^0.0.23", "date-fns": "^4.1.0", "lucide-react": "^0.515.0", "next": "15.0.3", "next-auth": "^4.24.8", "next-share": "^0.27.0", "node-fetch": "^3.3.2", "nodemailer": "^6.9.14", "react": "^18.3.1", "react-dom": "^18.3.1", "react-email": "^4.0.17", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "resend": "^4.6.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^20.19.2", "@types/nodemailer": "^6.4.15", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.20", "dotenv-cli": "^7.4.4", "eslint": "^8.57.1", "eslint-config-next": "15.0.3", "postcss": "^8.4.47", "prisma": "^5.19.1", "tailwindcss": "^3.4.13", "ts-node": "^10.9.2", "tsx": "^4.19.1", "typescript": "^5.8.3"}}