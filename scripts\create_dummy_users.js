/* eslint-disable */
const { PrismaClient } = require('@prisma/client');
const { randomUUID } = require('crypto');

// Load environment variables if dotenv is available
try {
  require('dotenv').config({ path: '.env' });
} catch (e) {
  console.warn('ℹ️  dotenv not installed – assuming environment variables are already set.');
}

const prisma = new PrismaClient();

const firstNames = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>'
];

const lastNames = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
];

function randomEmail() {
  return `${randomUUID().slice(0, 8)}@botmail.test`;
}

function randomName() {
  const first = firstNames[Math.floor(Math.random() * firstNames.length)];
  const last = lastNames[Math.floor(Math.random() * lastNames.length)];
  return `${first} ${last}`;
}

async function main() {
  console.log('👤 Creating 10 dummy users…');

  const desired = 10;
  let created = 0;

  while (created < desired) {
    const email = randomEmail();
    try {
      await prisma.user.create({
        data: {
          email,
          name: randomName(),
          // No password; accounts are inactive dummy voters
        },
      });
      console.log(`✅ Added user: ${email}`);
      created += 1;
    } catch (err) {
      if (err.code === 'P2002') {
        // Unique constraint violation – email already exists, try another
        continue;
      }
      console.error('Unexpected error:', err);
      break;
    }
  }

  console.log('🎉 Finished creating dummy users.');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 