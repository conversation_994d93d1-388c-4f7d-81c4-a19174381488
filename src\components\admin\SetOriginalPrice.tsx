"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface SetOriginalPriceProps {
  dealId: number
  hasOriginalPrice: boolean
  onSuccess?: () => void
}

export default function SetOriginalPrice({ dealId, hasOriginalPrice, onSuccess }: SetOriginalPriceProps) {
  const { data: session } = useSession()
  const [originalPrice, setOriginalPrice] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)

  // Only show if user is admin (id=1) and deal doesn't have original price
  if (!session?.user || session.user.id !== "1" || hasOriginalPrice) {
    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!originalPrice.trim()) {
      setError("Please enter an original price")
      return
    }

    const price = parseFloat(originalPrice)
    if (isNaN(price) || price <= 0) {
      setError("Please enter a valid price")
      return
    }

    setIsSubmitting(true)
    setError("")
    setSuccess(false)

    try {
      const response = await fetch("/api/admin/set-original-price", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          dealId: dealId,
          originalPrice: price
        }),
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess(true)
        setOriginalPrice("")
        if (onSuccess) {
          onSuccess()
        } else {
          // Reload the page after a short delay to show the updated discount
          setTimeout(() => {
            window.location.reload()
          }, 1500)
        }
      } else {
        setError(data.error || "Failed to set original price")
      }
    } catch (error) {
      console.error("Error setting original price:", error)
      setError("An error occurred. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="flex items-center gap-2">
      <Input
        type="number"
        step="0.01"
        min="0"
        placeholder="Original Price"
        value={originalPrice}
        onChange={(e) => setOriginalPrice(e.target.value)}
        className="w-32"
      />
      <Button
        onClick={handleSubmit}
        disabled={isSubmitting || !originalPrice}
        variant="secondary"
        size="sm"
      >
        Set Price
      </Button>
      {error && <p className="text-red-500 text-sm">{error}</p>}
      {success && <p className="text-green-500 text-sm">Price set! Refreshing...</p>}
    </div>
  )
} 