import { MetadataRoute } from 'next'
import { db } from '@/lib/db'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3010'

  // Static pages
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/browse`,
      lastModified: new Date(),
      changeFrequency: 'hourly' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/categories`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/stores`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/post`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    },
  ]

  try {
    // Get active deals
    const deals = await db.deal.findMany({
      where: { status: 'ACTIVE' },
      select: {
        id: true,
        slug: true,
        updatedAt: true,
      },
      orderBy: { updatedAt: 'desc' },
      take: 1000, // Limit for performance
    })

    // Get categories
    const categories = await db.category.findMany({
      where: { active: true },
      select: {
        slug: true,
        updatedAt: true,
      },
    })

    // Get stores
    const stores = await db.store.findMany({
      where: { active: true },
      select: {
        slug: true,
        updatedAt: true,
      },
    })

    // Deal pages
    const dealPages = deals.map((deal) => ({
      url: `${baseUrl}/deals/${deal.slug}`,
      lastModified: deal.updatedAt,
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    }))

    // Category pages
    const categoryPages = categories.map((category) => ({
      url: `${baseUrl}/categories/${category.slug}`,
      lastModified: category.updatedAt,
      changeFrequency: 'daily' as const,
      priority: 0.7,
    }))

    // Store pages
    const storePages = stores.map((store) => ({
      url: `${baseUrl}/stores/${store.slug}`,
      lastModified: store.updatedAt,
      changeFrequency: 'daily' as const,
      priority: 0.7,
    }))

    return [
      ...staticPages,
      ...dealPages,
      ...categoryPages,
      ...storePages,
    ]
  } catch (error) {
    console.error('Error generating sitemap:', error)
    // Return at least static pages if database fails
    return staticPages
  }
}
