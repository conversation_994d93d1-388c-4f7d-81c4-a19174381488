"use client"

import { useState, Suspense } from "react";
import { useRouter } from "next/navigation";
import { Search, Grid, List } from "lucide-react";
import DealCard from "@/components/DealCard";

// Define types for our data
interface Deal {
  id: number;
  slug?: string;
  title: string;
  description: string;
  originalPrice: number | null;
  dealPrice: number;
  discountPercentage: number | null;
  store: {
    id: number;
    name: string;
    slug: string;
  };
  category: {
    id: number;
    name: string;
    slug: string;
  };
  dealUrl: string;
  imageUrl?: string;
  couponCode?: string | null;
  expiresAt?: number | null;
  status: string;

  upvotes: number;
  downvotes: number;
  averageRating?: number;
  ratingCount?: number;
  viewCount?: number;
  createdAt: number;
  updatedAt: number;
  authorId?: number;
  niceAt?: number | null;
}

interface SearchParams {
  search?: string;
  category?: string;
  store?: string;
  sort?: string;
  view?: string;
}

interface BrowseContentProps {
  initialData: {
    deals: Deal[];
    categories: string[];
    stores: string[];
  };
  searchParams: SearchParams;
}

const sortOptions = [
  { value: "recent", label: "Most Recent" },
  { value: "nice", label: "Most Recent Nice" },
  { value: "popular", label: "Most Popular" },
  { value: "price-low", label: "Price: Low to High" },
  { value: "price-high", label: "Price: High to Low" },
  { value: "discount", label: "Highest Discount" },
  { value: "rating", label: "Highest Rated" },
];

export default function BrowseContent({ initialData, searchParams }: BrowseContentProps) {
  const router = useRouter()
  
  const urlSearch = searchParams.search || ""
  const urlCategory = searchParams.category || "All Categories"
  const urlStore = searchParams.store || "All Stores"
  const urlSort = searchParams.sort || "recent"
  const urlView = searchParams.view || "grid"

  // Local filter state (not applied until Submit is clicked)
  const [localSearch, setLocalSearch] = useState(urlSearch)
  const [localCategory, setLocalCategory] = useState(urlCategory)
  const [localStore, setLocalStore] = useState(urlStore)
  const [localSort, setLocalSort] = useState(urlSort)
  const [currentView, setCurrentView] = useState(urlView)

  const { deals, categories, stores } = initialData

  const handleViewChange = (newView: string) => {
    setCurrentView(newView)
    const params = new URLSearchParams()
    
    if (urlSearch.trim()) params.set('search', urlSearch.trim())
    if (urlCategory !== "All Categories") params.set('category', urlCategory)
    if (urlStore !== "All Stores") params.set('store', urlStore)
    if (urlSort !== "recent") params.set('sort', urlSort)
    params.set('view', newView)
    
    router.push(`/browse?${params.toString()}`)
  }

  const handleSubmitFilters = () => {
    const params = new URLSearchParams()
    
    if (localSearch.trim()) params.set('search', localSearch.trim())
    if (localCategory !== "All Categories") params.set('category', localCategory)
    if (localStore !== "All Stores") params.set('store', localStore)
    if (localSort !== "recent") params.set('sort', localSort)
    if (currentView !== "grid") params.set('view', currentView)
    
    router.push(`/browse?${params.toString()}`)
  }

  const handleClearFilters = () => {
    setLocalSearch("")
    setLocalCategory("All Categories")
    setLocalStore("All Stores")
    setLocalSort("recent")
    router.push('/browse')
  }

  // Filter and sort deals based on URL search params (applied filters)
  let filteredDeals = [...deals];
  
  if (urlSearch) {
    filteredDeals = filteredDeals.filter(
      (deal) =>
        deal.title.toLowerCase().includes(urlSearch.toLowerCase()) ||
        deal.description.toLowerCase().includes(urlSearch.toLowerCase())
    );
  }

  if (urlCategory !== "All Categories") {
    filteredDeals = filteredDeals.filter(
      (deal) => deal.category?.name === urlCategory
    );
  }

  if (urlStore !== "All Stores") {
    filteredDeals = filteredDeals.filter((deal) => deal.store?.name === urlStore);
  }

  // Sort deals
  switch (urlSort) {
    case "popular":
      filteredDeals.sort((a, b) => (b.upvotes - b.downvotes) - (a.upvotes - a.downvotes));
      break;
    case "price-low":
      filteredDeals.sort((a, b) => a.dealPrice - b.dealPrice);
      break;
    case "price-high":
      filteredDeals.sort((a, b) => b.dealPrice - a.dealPrice);
      break;
    case "discount":
      filteredDeals.sort((a, b) => (b.discountPercentage || 0) - (a.discountPercentage || 0));
      break;
    case "rating":
      filteredDeals.sort((a, b) => (b.averageRating || 0) - (a.averageRating || 0));
      break;
    case "nice":
      filteredDeals = filteredDeals
        .filter((d) => d.niceAt)
        .sort((a, b) => (b.niceAt || 0) - (a.niceAt || 0))
      break;
    default: // recent
      filteredDeals.sort((a, b) => b.createdAt - a.createdAt);
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-serif font-bold text-gray-900 mb-4">
            Browse All Deals
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl">
            Discover amazing deals from top retailers. Use filters to find exactly what you&apos;re looking for.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-8">
          {/* Large screens: Single row layout */}
          <div className="hidden lg:flex gap-4 items-center">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search for deals..."
                value={localSearch}
                onChange={(e) => setLocalSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              />
            </div>

            {/* Category Filter */}
            <select
              value={localCategory}
              onChange={(e) => setLocalCategory(e.target.value)}
              className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white"
            >
              {categories.map((cat) => (
                <option key={cat} value={cat}>
                  {cat}
                </option>
              ))}
            </select>

            {/* Store Filter */}
            <select
              value={localStore}
              onChange={(e) => setLocalStore(e.target.value)}
              className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white"
            >
              {stores.map((storeOption) => (
                <option key={storeOption} value={storeOption}>
                  {storeOption}
                </option>
              ))}
            </select>

            {/* Sort */}
            <select
              value={localSort}
              onChange={(e) => setLocalSort(e.target.value)}
              className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white"
            >
              {sortOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>

            {/* Submit Button */}
            <button
              onClick={handleSubmitFilters}
              className="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium"
            >
              Apply Filters
            </button>

            {/* View Toggle */}
            <div className="flex border border-gray-300 rounded-lg overflow-hidden">
              <button
                onClick={() => handleViewChange("grid")}
                className={`p-3 ${
                  currentView === "grid"
                    ? "bg-orange-500 text-white"
                    : "bg-white text-gray-600 hover:bg-gray-50"
                }`}
              >
                <Grid className="w-5 h-5" />
              </button>
              <button
                onClick={() => handleViewChange("list")}
                className={`p-3 ${
                  currentView === "list"
                    ? "bg-orange-500 text-white"
                    : "bg-white text-gray-600 hover:bg-gray-50"
                }`}
              >
                <List className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Small/Medium screens: Two row layout */}
          <div className="lg:hidden">
            {/* Search Bar - Full Width */}
            <div className="mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search for deals..."
                  value={localSearch}
                  onChange={(e) => setLocalSearch(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                />
              </div>
            </div>

            {/* Filters Row - Horizontal */}
            <div className="flex gap-2 items-center overflow-x-auto mb-4">
              {/* Category Filter */}
              <select
                value={localCategory}
                onChange={(e) => setLocalCategory(e.target.value)}
                className="px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white min-w-[120px] flex-shrink-0"
              >
                {categories.map((cat) => (
                  <option key={cat} value={cat}>
                    {cat}
                  </option>
                ))}
              </select>

              {/* Store Filter */}
              <select
                value={localStore}
                onChange={(e) => setLocalStore(e.target.value)}
                className="px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white min-w-[120px] flex-shrink-0"
              >
                {stores.map((storeOption) => (
                  <option key={storeOption} value={storeOption}>
                    {storeOption}
                  </option>
                ))}
              </select>

              {/* Sort */}
              <select
                value={localSort}
                onChange={(e) => setLocalSort(e.target.value)}
                className="px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white min-w-[140px] flex-shrink-0"
              >
                {sortOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>

              {/* Submit Button for Mobile */}
              <button
                onClick={handleSubmitFilters}
                className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium text-sm whitespace-nowrap flex-shrink-0"
              >
                Apply
              </button>
            </div>

            {/* Mobile Filter Actions */}
            <div className="flex gap-2">
              <button
                onClick={handleClearFilters}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium text-sm"
              >
                Clear All
              </button>

              {/* View Toggle */}
              <div className="flex border border-gray-300 rounded-lg overflow-hidden flex-shrink-0 ml-auto">
                <button
                  onClick={() => handleViewChange("grid")}
                  className={`p-2 ${
                    currentView === "grid"
                      ? "bg-orange-500 text-white"
                      : "bg-white text-gray-600 hover:bg-gray-50"
                  }`}
                >
                  <Grid className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleViewChange("list")}
                  className={`p-2 ${
                    currentView === "list"
                      ? "bg-orange-500 text-white"
                      : "bg-white text-gray-600 hover:bg-gray-50"
                  }`}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Results Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold text-gray-900">
            {filteredDeals.length} Deal{filteredDeals.length !== 1 ? "s" : ""} Found
            {urlSearch && (
              <span className="text-gray-600 font-normal">
                {" "}
                for &quot;{urlSearch}&quot;
              </span>
            )}
          </h2>
        </div>

        {/* Deals Grid/List */}
        <Suspense fallback={<div>Loading deals...</div>}>
          {filteredDeals.length > 0 ? (
            <div
              className={
                urlView === "grid"
                  ? "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 gap-6"
                  : "space-y-4"
              }
            >
              {filteredDeals.map((deal) => {
                // Handle date conversions safely
                let expiresAt;
                if (deal.expiresAt) {
                  try {
                    expiresAt = new Date(deal.expiresAt).toISOString();
                  } catch {
                    expiresAt = undefined;
                  }
                }

                const createdDate = new Date(deal.createdAt).toISOString();

                // Handle pricing logic - only show discount if there's a valid original price
                const originalPrice = deal.originalPrice;
                const dealPrice = deal.dealPrice;
                let discountPercentage = 0;
                
                if (originalPrice && originalPrice > 0 && originalPrice > dealPrice) {
                  // Use provided discount percentage or calculate it
                  discountPercentage = deal.discountPercentage || Math.round(((originalPrice - dealPrice) / originalPrice) * 100);
                }

                // Calculate isNew server-side to avoid hydration mismatch
                const isNew = (() => {
                  if (!deal.createdAt) return false;
                  const createdAt = new Date(deal.createdAt);
                  const now = new Date();
                  const diffInHours = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
                  return diffInHours <= 24;
                })();

                return (
                  <DealCard
                    key={deal.id}
                    deal={{
                      id: deal.id,
                      slug: deal.slug || `fallback-${deal.id}`,
                      title: deal.title,
                      description: deal.description,
                      originalPrice: originalPrice,
                      dealPrice: dealPrice,
                      discount: discountPercentage,
                      store: deal.store,
                      category: deal.category,
                      dealUrl: deal.dealUrl,
                      imageUrl: deal.imageUrl || '',
                      upvotes: deal.upvotes,
                      downvotes: deal.downvotes,
                      couponCode: deal.couponCode || undefined,
                      expiresAt: expiresAt,
                      createdDate: createdDate,
                      isNew: isNew
                    }}
                  />
                );
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Search className="w-16 h-16 mx-auto" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                No deals found
              </h3>
              <p className="text-gray-600 mb-6">
                Try adjusting your search criteria or browse all categories.
              </p>
              <button 
                onClick={handleClearFilters}
                className="bg-orange-500 text-white px-6 py-3 rounded-lg hover:bg-orange-600 transition-colors"
              >
                Clear Filters
              </button>
            </div>
          )}
        </Suspense>

        {/* Load More Button */}
        {filteredDeals.length > 0 && (
          <div className="text-center mt-12">
            <button className="bg-white text-orange-600 border border-orange-600 px-8 py-3 rounded-lg hover:bg-orange-50 transition-colors">
              Load More Deals
            </button>
          </div>
        )}
      </div>
    </div>
  );
} 