'use client';

import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AdminStats } from '@/lib/admin';

interface ScraperLog {
  id: number;
  source: string;
  status: string;
  dealsFound: number;
  dealsAdded: number;
  dealsSkipped: number;
  error?: string;
  dataFile?: string;
  startedAt: string;
  completedAt?: string;
  createdAt: string;
}

interface ScraperResult {
  success: boolean;
  dealsFound: number;
  dealsAdded: number;
  dealsSkipped: number;
  dataFile?: string;
  error?: string;
}

export default function AdminScrapersPage() {
  const [scraperLogs, setScraperLogs] = useState<ScraperLog[]>([]);
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    totalDeals: 0,
    activeDeals: 0,
    pendingDeals: 0,
    recentActivity: 0,
    totalComments: 0,
    totalCategories: 0,
    totalStores: 0,
  });
  const [loading, setLoading] = useState(true);
  const [runningScrapers, setRunningScrapers] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchScraperLogs();
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/stats');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setStats(data.data);
      }
    } catch (error) {
      console.error('Error fetching admin stats:', error);
    }
  };

  const fetchScraperLogs = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch('/api/admin/scrapers/logs');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setScraperLogs(data.data.logs);
      } else {
        setError(data.error || 'Failed to fetch scraper logs');
      }
    } catch (error) {
      console.error('Error fetching scraper logs:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch scraper logs');
    } finally {
      setLoading(false);
    }
  };

  const handleRunScraper = async (source: string) => {
    try {
      setRunningScrapers(prev => [...prev, source]);
      setError(null);
      
      const response = await fetch('/api/admin/scrapers/run', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ source })
      });
      
      const data = await response.json();
      
      if (data.success) {
        const result: ScraperResult = data.data;
        alert(`${source} scraper completed successfully!\n\nDeals Found: ${result.dealsFound}\nDeals Added: ${result.dealsAdded}\nDeals Skipped: ${result.dealsSkipped}`);
        
        if (result.dataFile) {
          console.log(`Scraper data saved to: ${result.dataFile}`);
        }
        await fetchScraperLogs();
        await fetchStats(); // Refresh stats to update pending count
      } else {
        setError(data.error || `Failed to run ${source} scraper`);
      }
    } catch (error) {
      console.error(`Error running ${source} scraper:`, error);
      setError(error instanceof Error ? error.message : `Failed to run ${source} scraper`);
    } finally {
      setRunningScrapers(prev => prev.filter(s => s !== source));
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'started': return 'bg-blue-100 text-blue-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDuration = (startedAt: string, completedAt?: string) => {
    const start = new Date(startedAt);
    const end = completedAt ? new Date(completedAt) : new Date();
    const duration = Math.round((end.getTime() - start.getTime()) / 1000);
    return `${duration}s`;
  };

  return (
    <AdminLayout stats={stats} currentPage="scrapers">
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">Scraper Management</h1>
          <Button 
            onClick={async () => {
              await fetchScraperLogs();
              await fetchStats();
            }}
            variant="outline"
            disabled={loading}
          >
            {loading ? 'Refreshing...' : 'Refresh'}
          </Button>
        </div>

        {error && (
          <Card className="p-4 bg-red-50 border-red-200">
            <p className="text-red-800 font-medium">Error: {error}</p>
          </Card>
        )}

        {/* Available Scrapers */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">HotUKDeals Scraper</h3>
              <Button
                onClick={() => handleRunScraper('hukd')}
                disabled={runningScrapers.includes('hukd')}
                variant={runningScrapers.includes('hukd') ? "outline" : "default"}
              >
                {runningScrapers.includes('hukd') ? (
                  <>
                    <span className="animate-spin mr-2">⚡</span>
                    Running...
                  </>
                ) : (
                  'Run Scraper'
                )}
              </Button>
            </div>
            <p className="text-sm text-gray-500">
              Scrapes deals from HotUKDeals.com/hot page. Automatically creates stores and categories as needed.
            </p>
          </Card>

          <Card className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Future Scrapers</h3>
              <Button disabled variant="outline">
                Coming Soon
              </Button>
            </div>
            <p className="text-sm text-gray-500">
              Additional scrapers for Amazon, Tesco, and other retailers will be added here.
            </p>
          </Card>
        </div>

        {/* Scraper Logs */}
        <Card>
          <div className="px-6 py-4 border-b">
            <h2 className="text-lg font-medium text-gray-900">Scraper Logs</h2>
            <p className="mt-1 text-sm text-gray-500">
              History of scraper runs and their results.
            </p>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Source
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Results
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Duration
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Started
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                      Loading...
                    </td>
                  </tr>
                ) : scraperLogs.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                      No scraper logs found. Run a scraper to see results here.
                    </td>
                  </tr>
                ) : (
                  scraperLogs.map((log) => (
                    <tr key={log.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {log.id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 capitalize">
                        {log.source === 'hukd' ? 'HotUKDeals' : log.source}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Badge className={getStatusColor(log.status)}>
                          {log.status}
                        </Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {log.status === 'completed' ? (
                          <div className="space-y-1">
                            <div>Found: {log.dealsFound}</div>
                            <div>Added: {log.dealsAdded}</div>
                            <div>Skipped: {log.dealsSkipped}</div>
                          </div>
                        ) : log.error ? (
                          <div className="text-red-600 text-xs max-w-xs truncate" title={log.error}>
                            Error: {log.error}
                          </div>
                        ) : (
                          '-'
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {getDuration(log.startedAt, log.completedAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(log.startedAt)}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </Card>
      </div>
    </AdminLayout>
  );
} 