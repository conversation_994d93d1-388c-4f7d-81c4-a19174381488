import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "@/lib/auth"
import { db } from "@/lib/db"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession()
    
    if (!session?.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      )
    }

    const userIdString = session.user.id
    const userId = parseInt(userIdString, 10)

    if (isNaN(userId)) {
      return NextResponse.json(
        { message: "Invalid user ID" },
        { status: 400 }
      )
    }

    const searchParams = request.nextUrl.searchParams
    const type = searchParams.get('type') // 'UPVOTE' or 'DOWNVOTE'

    const whereClause: { userId: number; type?: string } = {
      userId: userId
    }

    if (type && (type === 'UPVOTE' || type === 'DOWNVOTE')) {
      whereClause.type = type
    }

    const votes = await db.vote.findMany({
      where: whereClause,
      include: {
        deal: {
          include: {
            author: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
            store: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            category: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
            _count: {
              select: {
                votes: true,
                comments: true,
                savedDeals: true,
              },
            },
          },
        },
      },
      orderBy: { createdAt: "desc" },
    })

    return NextResponse.json({ votes })

  } catch (error) {
    console.error("Error fetching user votes:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
} 