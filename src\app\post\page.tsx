"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { ArrowLeft, Upload, Plus, Info, ExternalLink, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface Category {
  id: number
  name: string
  slug: string
}

interface Store {
  id: number
  name: string
  slug: string
}

interface FormData {
  categories: Category[]
  stores: Store[]
}

export default function PostDealPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [formData, setFormData] = useState<FormData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)
  const [submittedDeal, setSubmittedDeal] = useState<any>(null)

  // Form state
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [originalPrice, setOriginalPrice] = useState("")
  const [dealPrice, setDealPrice] = useState("")
  const [category, setCategory] = useState("")
  const [store, setStore] = useState("")
  const [dealUrl, setDealUrl] = useState("")
  const [couponCode, setCouponCode] = useState("")
  const [expiresAt, setExpiresAt] = useState("")
  const [imageUrl, setImageUrl] = useState("")
  const [imageUrls, setImageUrls] = useState<string[]>([])
  const [mainImageFile, setMainImageFile] = useState<File | null>(null)
  const [galleryImageFiles, setGalleryImageFiles] = useState<File[]>([])
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [uploadingImages, setUploadingImages] = useState(false)

  // Image handling functions
  const handleMainImageFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setMainImageFile(file)
      // Create preview
      const reader = new FileReader()
      reader.onload = (e) => setImagePreview(e.target?.result as string)
      reader.readAsDataURL(file)
    }
  }

  const handleGalleryImagesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    setGalleryImageFiles(prev => [...prev, ...files])
  }

  const removeGalleryImage = (index: number) => {
    setGalleryImageFiles(prev => prev.filter((_, i) => i !== index))
  }

  const addImageUrl = () => {
    setImageUrls(prev => [...prev, ""])
  }

  const updateImageUrl = (index: number, url: string) => {
    setImageUrls(prev => prev.map((u, i) => i === index ? url : u))
  }

  const removeImageUrl = (index: number) => {
    setImageUrls(prev => prev.filter((_, i) => i !== index))
  }

  // Fetch form data on component mount
  useEffect(() => {
    const fetchFormData = async () => {
      setIsLoading(true)
      try {
        const response = await fetch("/api/deals/form-data")
        if (response.ok) {
          const data = await response.json()
          setFormData(data)
        }
      } catch (error) {
        console.error("Failed to fetch form data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchFormData()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!session?.user) {
      router.push("/auth/signin?callbackUrl=/post")
      return
    }

    setIsSubmitting(true)
    setError("")

    try {
      // Prepare form data for submission
      const hasFiles = mainImageFile || galleryImageFiles.length > 0
      
      if (hasFiles) {
        // Use FormData for file uploads
        const formData = new FormData()
        
        // Add basic form fields
        formData.append("title", title)
        formData.append("description", description)
        formData.append("dealPrice", dealPrice)
        formData.append("category", category)
        formData.append("store", store)
        formData.append("dealUrl", dealUrl)
        
        if (originalPrice) formData.append("originalPrice", originalPrice)
        if (couponCode) formData.append("couponCode", couponCode)
        if (expiresAt) formData.append("expiresAt", expiresAt)
        if (imageUrl) formData.append("imageUrl", imageUrl)
        
        // Add image files
        if (mainImageFile) {
          formData.append("mainImage", mainImageFile)
        }
        
        galleryImageFiles.forEach((file) => {
          formData.append("galleryImages", file)
        })
        
        // Add image URLs
        imageUrls.filter(url => url.trim()).forEach((url) => {
          formData.append("imageUrls", url.trim())
        })

        const response = await fetch("/api/deals/submit", {
          method: "POST",
          body: formData, // Don't set Content-Type header for FormData
        })

        const data = await response.json()

        if (response.ok) {
          setSuccess(true)
          setSubmittedDeal(data.deal)
          // Reset form
          resetForm()
        } else {
          setError(data.message || "Failed to submit deal")
        }
      } else {
        // Use JSON for URL-only submissions
        const response = await fetch("/api/deals/submit", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            title,
            description,
            originalPrice: originalPrice || null,
            dealPrice,
            category,
            store,
            dealUrl,
            couponCode: couponCode || null,
            expiresAt: expiresAt || null,
            imageUrl: imageUrl || null,
            imageUrls: imageUrls.filter(url => url.trim())
          }),
        })

        const data = await response.json()

        if (response.ok) {
          setSuccess(true)
          setSubmittedDeal(data.deal)
          resetForm()
        } else {
          setError(data.message || "Failed to submit deal")
        }
      }
    } catch (error) {
      console.error("Submission error:", error)
      setError("An error occurred. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  const resetForm = () => {
    setTitle("")
    setDescription("")
    setOriginalPrice("")
    setDealPrice("")
    setCategory("")
    setStore("")
    setDealUrl("")
    setCouponCode("")
    setExpiresAt("")
    setImageUrl("")
    setImageUrls([])
    setMainImageFile(null)
    setGalleryImageFiles([])
    setImagePreview(null)
  }

  // Show success page
  if (success && submittedDeal) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
          <div className="max-w-2xl mx-auto">
            <Card className="text-center">
              <CardContent className="p-8">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <h1 className="text-2xl font-bold text-gray-900 mb-2">
                  Deal Submitted Successfully!
                </h1>
                <p className="text-gray-600 mb-6">
                  Your deal "{submittedDeal.title}" has been submitted for review. 
                  Our team will review it and publish it once approved (usually within 24 hours).
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild>
                    <Link href="/">
                      Back to Home
                    </Link>
                  </Button>
                  <Button variant="outline" onClick={() => {
                    setSuccess(false)
                    setSubmittedDeal(null)
                  }}>
                    Submit Another Deal
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-orange-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (status === "unauthenticated") {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
          <Link 
            href="/" 
            className="inline-flex items-center text-gray-600 hover:text-orange-600 mb-6 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Link>

          <div className="max-w-2xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-4xl font-serif font-bold text-gray-900 mb-4">
                Post a Deal
              </h1>
              <p className="text-xl text-gray-600">
                Share a great deal with the community and help others save money!
              </p>
            </div>

            <Card>
              <CardContent className="p-8 text-center">
                <Info className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Login Required</h3>
                <p className="text-gray-600 mb-6">
                  You need to be logged in to post deals. All submitted deals are reviewed by our team before going live.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild>
                    <Link href="/auth/signin?callbackUrl=/post">
                      Sign In
                    </Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href="/auth/register?callbackUrl=/post">
                      Create Account
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
        {/* Back Button */}
        <Link 
          href="/" 
          className="inline-flex items-center text-gray-600 hover:text-orange-600 mb-6 transition-colors"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Home
        </Link>

        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-serif font-bold text-gray-900 mb-4">
              Post a Deal
            </h1>
            <p className="text-xl text-gray-600">
              Share a great deal with the community and help others save money!
            </p>
          </div>

          {/* User Info Notice */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <div className="flex items-start gap-3">
              <Info className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <h3 className="font-semibold text-green-900 mb-2">Welcome, {session?.user?.name || session?.user?.email}!</h3>
                <p className="text-green-800">
                  You're logged in and ready to post deals. All submitted deals are reviewed by our team before going live.
                </p>
              </div>
            </div>
          </div>

          {/* Deal Form */}
          <Card>
            <CardContent className="p-8">
              {error && (
                <Alert variant="destructive" className="mb-6">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
              {/* Deal Title */}
              <div>
                <Label htmlFor="title">Deal Title *</Label>
                <Input
                  id="title"
                  type="text"
                  placeholder="e.g., Samsung Galaxy S24 Ultra 256GB - 25% off"
                  value={title}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setTitle(e.target.value)}
                  required
                />
                <p className="text-sm text-gray-500 mt-1">
                  Be descriptive and include the key details like product name, size, color, etc.
                </p>
              </div>

              {/* Description */}
              <div>
                <Label htmlFor="description">Description *</Label>
                <textarea
                  id="description"
                  rows={4}
                  placeholder="Describe the deal, its key features, and why it's worth sharing..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  value={description}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setDescription(e.target.value)}
                  required
                />
              </div>

              {/* Price Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="originalPrice">Original Price (£)</Label>
                  <Input
                    id="originalPrice"
                    type="number"
                    placeholder="199.99"
                    step="0.01"
                    min="0"
                    value={originalPrice}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setOriginalPrice(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="dealPrice">Deal Price (£) *</Label>
                  <Input
                    id="dealPrice"
                    type="number"
                    placeholder="149.99"
                    step="0.01"
                    min="0"
                    value={dealPrice}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setDealPrice(e.target.value)}
                    required
                  />
                </div>
              </div>

              {/* Category and Store */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="category">Category *</Label>
                  <select
                    id="category"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    value={category}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setCategory(e.target.value)}
                    required
                  >
                    <option value="">Select a category</option>
                    {formData?.categories.map((cat) => (
                      <option key={cat.id} value={cat.id}>
                        {cat.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <Label htmlFor="store">Store *</Label>
                  <select
                    id="store"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    value={store}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setStore(e.target.value)}
                    required
                  >
                    <option value="">Select a store</option>
                    {formData?.stores.map((storeItem) => (
                      <option key={storeItem.id} value={storeItem.id}>
                        {storeItem.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Deal URL */}
              <div>
                <Label htmlFor="dealUrl">Deal URL *</Label>
                <div className="relative">
                  <Input
                    id="dealUrl"
                    type="url"
                    placeholder="https://example.com/product-deal"
                    value={dealUrl}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setDealUrl(e.target.value)}
                    className="pr-10"
                    required
                  />
                  <ExternalLink className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  Link directly to the product page where the deal is available
                </p>
              </div>

              {/* Optional Fields */}
              <div className="border-t border-gray-200 pt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Optional Information</h3>
                
                <div className="space-y-4">
                  {/* Coupon Code */}
                  <div>
                    <Label htmlFor="couponCode">Coupon Code</Label>
                    <Input
                      id="couponCode"
                      type="text"
                      placeholder="SAVE20"
                      value={couponCode}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setCouponCode(e.target.value)}
                    />
                  </div>

                  {/* Expiry Date */}
                  <div>
                    <Label htmlFor="expiresAt">Deal Expires</Label>
                    <Input
                      id="expiresAt"
                      type="datetime-local"
                      value={expiresAt}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setExpiresAt(e.target.value)}
                    />
                  </div>

                  {/* Enhanced Image Upload Section */}
                  <div className="space-y-6">
                    <h4 className="text-md font-semibold text-gray-900">Product Images</h4>
                    
                    {/* Main Image Upload */}
                    <div>
                      <Label htmlFor="mainImage">Main Product Image</Label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-orange-500 transition-colors">
                        {imagePreview ? (
                          <div className="space-y-4">
                            <img 
                              src={imagePreview} 
                              alt="Preview" 
                              className="max-w-full max-h-48 mx-auto rounded-lg"
                            />
                            <Button 
                              type="button" 
                              variant="outline" 
                              size="sm"
                              onClick={() => {
                                setMainImageFile(null)
                                setImagePreview(null)
                              }}
                            >
                              Remove Image
                            </Button>
                          </div>
                        ) : (
                          <label htmlFor="mainImage" className="cursor-pointer block">
                            <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                            <p className="text-sm text-gray-600 mb-2">
                              Click to upload main product image
                            </p>
                            <p className="text-xs text-gray-500">
                              PNG, JPG, WebP up to 5MB
                            </p>
                          </label>
                        )}
                        <input
                          type="file"
                          id="mainImage"
                          accept="image/*"
                          onChange={handleMainImageFileChange}
                          className="hidden"
                        />
                      </div>
                    </div>

                    {/* Main Image URL Alternative */}
                    <div>
                      <Label htmlFor="imageUrl">Or enter image URL</Label>
                      <Input
                        id="imageUrl"
                        type="url"
                        placeholder="https://example.com/product-image.jpg"
                        value={imageUrl}
                        onChange={(e) => setImageUrl(e.target.value)}
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Use this if you prefer to link to an existing image online
                      </p>
                    </div>

                    {/* Gallery Images */}
                    <div>
                      <Label>Additional Images (Gallery)</Label>
                      
                      {/* Gallery File Upload */}
                      <div className="border-2 border-dashed border-gray-200 rounded-lg p-4 text-center hover:border-orange-400 transition-colors">
                        <Upload className="w-6 h-6 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-600 mb-2">
                          Upload additional product images
                        </p>
                        <input
                          type="file"
                          id="galleryImages"
                          accept="image/*"
                          multiple
                          onChange={handleGalleryImagesChange}
                          className="hidden"
                        />
                        <label 
                          htmlFor="galleryImages" 
                          className="cursor-pointer bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-lg text-sm transition-colors inline-block"
                        >
                          Choose Files
                        </label>
                      </div>

                      {/* Gallery Files Preview */}
                      {galleryImageFiles.length > 0 && (
                        <div className="mt-4">
                          <p className="text-sm font-medium text-gray-700 mb-2">Selected Files:</p>
                          <div className="space-y-2">
                            {galleryImageFiles.map((file, index) => (
                              <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                                <span className="text-sm text-gray-600 truncate">{file.name}</span>
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => removeGalleryImage(index)}
                                >
                                  Remove
                                </Button>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Gallery URLs */}
                      <div className="mt-4">
                        <div className="flex items-center justify-between mb-2">
                          <p className="text-sm font-medium text-gray-700">Or add image URLs:</p>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={addImageUrl}
                          >
                            <Plus className="w-4 h-4 mr-1" />
                            Add URL
                          </Button>
                        </div>
                        
                        {imageUrls.map((url, index) => (
                          <div key={index} className="flex gap-2 mb-2">
                            <Input
                              type="url"
                              placeholder="https://example.com/image.jpg"
                              value={url}
                              onChange={(e) => updateImageUrl(index, e.target.value)}
                            />
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => removeImageUrl(index)}
                            >
                              Remove
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Image Guidelines */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h5 className="font-medium text-blue-900 mb-2">Image Guidelines</h5>
                      <ul className="text-sm text-blue-800 space-y-1">
                        <li>• Use high-quality, clear product images</li>
                        <li>• Main image should show the product prominently</li>
                        <li>• Additional images can show different angles, details, or packaging</li>
                        <li>• Avoid images with watermarks or excessive text</li>
                        <li>• Supported formats: JPG, PNG, WebP (max 5MB each)</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {/* Submission Guidelines */}
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <h3 className="font-semibold text-gray-900 mb-3">Submission Guidelines</h3>
                <ul className="text-sm text-gray-700 space-y-1">
                  <li>• Make sure the deal is currently active and available</li>
                  <li>• Provide accurate pricing information</li>
                  <li>• Use clear, descriptive titles without excessive caps or symbols</li>
                  <li>• Include all relevant details about the product</li>
                  <li>• Check that the deal hasn't been posted recently</li>
                </ul>
              </div>

              {/* Submit Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 pt-6">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex-1"
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Plus className="w-5 h-5 mr-2" />
                      Submit Deal for Review
                    </>
                  )}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  disabled={isSubmitting}
                  className="flex-1"
                >
                  Save as Draft
                </Button>
              </div>

              <p className="text-sm text-gray-500 text-center">
                Your deal will be reviewed by our team and published once approved (usually within 24 hours)
              </p>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
