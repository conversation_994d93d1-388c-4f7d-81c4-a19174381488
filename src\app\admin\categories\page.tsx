import { redirect } from "next/navigation"
import { requireAdmin } from "@/lib/auth"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import AdminLayout from "@/components/admin/AdminLayout"
import { getAdminStats } from "@/lib/admin"
import CategoryManagement from "@/components/admin/CategoryManagement"

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic'

export default async function AdminCategoriesPage() {
  try {
    await requireAdmin()
  } catch {
    redirect("/auth/signin")
  }

  const stats = await getAdminStats()

  return (
    <AdminLayout stats={stats} currentPage="categories">
      <Card>
        <CardHeader>
          <CardTitle>Category Management</CardTitle>
          <CardDescription>
            Add, edit, and organize deal categories
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CategoryManagement />
        </CardContent>
      </Card>
    </AdminLayout>
  )
}
