import { NextRequest, NextResponse } from "next/server"
import { requireAuth } from "@/lib/auth"
import { db } from "@/lib/db"

export async function POST(request: NextRequest) {
  try {
    const session = await requireAuth()
    const { dealId: dealIdRaw, action } = await request.json()

    if (!dealIdRaw || !action) {
      return NextResponse.json(
        { error: "Missing dealId or action" },
        { status: 400 }
      )
    }

    const dealId = parseInt(dealIdRaw, 10)
    if (isNaN(dealId)) {
      return NextResponse.json(
        { error: "Invalid dealId" },
        { status: 400 }
      )
    }

    // Convert user ID to integer
    const userIdString = session.user.id
    const userId = parseInt(userIdString, 10)

    if (isNaN(userId)) {
      return NextResponse.json(
        { error: "Invalid user ID" },
        { status: 400 }
      )
    }

    if (action === "save") {
      // Check if already saved
      const existingSave = await db.savedDeal.findUnique({
        where: {
          dealId_userId: {
            dealId,
            userId,
          },
        },
      })

      if (existingSave) {
        return NextResponse.json(
          { error: "Deal already saved" },
          { status: 400 }
        )
      }

      // Create new saved deal
      await db.savedDeal.create({
        data: {
          dealId,
          userId,
        },
      })

      return NextResponse.json({ success: true, action: "saved" })
    } else if (action === "unsave") {
      // Remove saved deal
      await db.savedDeal.delete({
        where: {
          dealId_userId: {
            dealId,
            userId,
          },
        },
      })

      return NextResponse.json({ success: true, action: "unsaved" })
    } else {
      return NextResponse.json(
        { error: "Invalid action" },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error("Error saving/unsaving deal:", error)
    return NextResponse.json(
      { error: "Failed to save/unsave deal" },
      { status: 500 }
    )
  }
}

// GET endpoint to check if a deal is saved by the current user
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const dealIdRaw = searchParams.get("dealId")

    if (!dealIdRaw) {
      return NextResponse.json(
        { error: "Missing dealId" },
        { status: 400 }
      )
    }

    const dealId = parseInt(dealIdRaw, 10)
    if (isNaN(dealId)) {
      return NextResponse.json(
        { error: "Invalid dealId" },
        { status: 400 }
      )
    }

    // Try to get session, but don't require authentication
    // If no session, just return that the deal is not saved
    try {
      const session = await requireAuth()
      
      // Convert user ID to integer
      const userIdString = session.user.id
      const userId = parseInt(userIdString, 10)

      if (isNaN(userId)) {
        return NextResponse.json(
          { error: "Invalid user ID" },
          { status: 400 }
        )
      }

      const savedDeal = await db.savedDeal.findUnique({
        where: {
          dealId_userId: {
            dealId,
            userId,
          },
        },
      })

      return NextResponse.json({ saved: !!savedDeal })
    } catch {
      // No valid session - user is not logged in, so deal cannot be saved
      return NextResponse.json({ saved: false })
    }
  } catch (error) {
    console.error("Error checking saved deal:", error)
    return NextResponse.json(
      { error: "Failed to check saved deal" },
      { status: 500 }
    )
  }
}
