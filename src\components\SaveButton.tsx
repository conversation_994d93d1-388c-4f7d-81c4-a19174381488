"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { Heart, Check, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface SaveButtonProps {
  dealId: number
  initialSaved?: boolean
  className?: string
  variant?: "icon" | "button"
}

// Simple toast notification component
function Toast({ message, type, onClose }: { message: string; type: 'success' | 'error'; onClose: () => void }) {
  useEffect(() => {
    const timer = setTimeout(onClose, 3000)
    return () => clearTimeout(timer)
  }, [onClose])

  return (
    <div className={cn(
      "fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-sm flex items-center gap-2 transition-all",
      type === 'success' ? "bg-green-600 text-white" : "bg-red-600 text-white"
    )}>
      {type === 'success' ? <Check className="w-4 h-4" /> : <X className="w-4 h-4" />}
      <span className="text-sm font-medium">{message}</span>
      <button onClick={onClose} className="ml-2 hover:opacity-70">
        <X className="w-4 h-4" />
      </button>
    </div>
  )
}

export default function SaveButton({ 
  dealId, 
  initialSaved = false, 
  className,
  variant = "icon" 
}: SaveButtonProps) {
  const { data: session } = useSession()
  const [saved, setSaved] = useState(initialSaved)
  const [loading, setLoading] = useState(false)
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' } | null>(null)

  // Check if deal is saved when component mounts and user is logged in
  useEffect(() => {
    const checkSavedStatus = async () => {
      if (!session?.user || !dealId) return

      try {
        const response = await fetch(`/api/deals/save?dealId=${dealId}`)
        if (response.ok) {
          const data = await response.json()
          setSaved(data.saved)
        }
      } catch (error) {
        console.error("Error checking saved status:", error)
      }
    }

    checkSavedStatus()
  }, [session?.user, dealId])

  const handleSave = async () => {
    if (!session?.user) {
      // Redirect to login or show login modal
      window.location.href = "/auth/signin"
      return
    }

    setLoading(true)
    try {
      const response = await fetch("/api/deals/save", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          dealId,
          action: saved ? "unsave" : "save",
        }),
      })

      if (response.ok) {
        const newSavedState = !saved
        setSaved(newSavedState)
        
        // Show success toast
        setToast({
          message: newSavedState ? "Deal saved successfully!" : "Deal removed from saved",
          type: 'success'
        })
      } else {
        const errorData = await response.json()
        setToast({
          message: errorData.error || "Failed to save deal",
          type: 'error'
        })
      }
    } catch (error) {
      console.error("Error saving deal:", error)
      setToast({
        message: "An error occurred. Please try again.",
        type: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  if (variant === "button") {
    return (
      <>
        <button
          onClick={handleSave}
          disabled={loading}
          className={cn(
            "flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-colors",
            saved 
              ? "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50" 
              : "bg-white border border-gray-300 text-gray-700 hover:bg-gray-50",
            loading && "opacity-50 cursor-not-allowed",
            className
          )}
        >
          <Heart className={cn("w-4 h-4", saved ? "fill-red-500 text-red-500" : "text-gray-500")} />
          {saved ? "Saved" : "Save Deal"}
        </button>
        
        {/* Toast Notification */}
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onClose={() => setToast(null)}
          />
        )}
      </>
    )
  }

  // Icon variant (default)
  return (
    <>
      <button
        onClick={handleSave}
        disabled={loading}
        className={cn(
          "p-2 rounded-full transition-colors",
          saved 
            ? "bg-red-100 text-red-600 hover:bg-red-200" 
            : "bg-white text-gray-600 hover:bg-gray-50",
          loading && "opacity-50 cursor-not-allowed",
          className
        )}
        title={saved ? "Remove from saved" : "Save deal"}
      >
        <Heart className={cn("w-5 h-5", saved && "fill-current")} />
      </button>
      
      {/* Toast Notification */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </>
  )
}
