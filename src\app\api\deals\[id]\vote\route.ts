import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "@/lib/auth"
import { db } from "@/lib/db"

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  const dealId = parseInt(resolvedParams.id, 10);

  if (isNaN(dealId)) {
    return NextResponse.json(
      { message: "Invalid deal ID" },
      { status: 400 }
    )
  }
  try {
    const session = await getServerSession()
    
    if (!session?.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      )
    }

    const { type } = await request.json()
    const userIdString = session.user.id
    const userId = parseInt(userIdString, 10)

    if (isNaN(userId)) {
      return NextResponse.json(
        { message: "Invalid user ID" },
        { status: 400 }
      )
    }

    // Debug logging
    console.log("Vote attempt - dealId:", dealId, "userId:", userId, "type:", type)

    if (!type || !["UPVOTE", "DOWNVOTE"].includes(type)) {
      return NextResponse.json(
        { message: "Invalid vote type" },
        { status: 400 }
      )
    }

    // Check if deal exists
    const deal = await db.deal.findUnique({
      where: { id: dealId }
    })

    if (!deal) {
      return NextResponse.json(
        { message: "Deal not found" },
        { status: 404 }
      )
    }

    // Check if user exists
    const user = await db.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      console.log("User not found in database:", userId)
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      )
    }

    // Check if user already voted
    const existingVote = await db.vote.findUnique({
      where: {
        dealId_userId: {
          dealId,
          userId
        }
      }
    })

    let voteChange = { upvotes: 0, downvotes: 0 }

    if (existingVote) {
      if (existingVote.type === type) {
        // Same vote type - remove the vote
        await db.vote.delete({
          where: { id: existingVote.id }
        })

        voteChange = type === "UPVOTE" 
          ? { upvotes: -1, downvotes: 0 }
          : { upvotes: 0, downvotes: -1 }
      } else {
        // Different vote type - update the vote
        await db.vote.update({
          where: { id: existingVote.id },
          data: { type }
        })

        voteChange = type === "UPVOTE"
          ? { upvotes: 1, downvotes: -1 }
          : { upvotes: -1, downvotes: 1 }
      }
    } else {
      // New vote
      await db.vote.create({
        data: {
          type,
          dealId,
          userId
        }
      })

      voteChange = type === "UPVOTE"
        ? { upvotes: 1, downvotes: 0 }
        : { upvotes: 0, downvotes: 1 }
    }

    // Update deal vote counts
    const updatedDeal = await db.deal.update({
      where: { id: dealId },
      data: {
        upvotes: {
          increment: voteChange.upvotes
        },
        downvotes: {
          increment: voteChange.downvotes
        }
      },
      select: {
        id: true,
        upvotes: true,
        downvotes: true,
        niceAt: true
      }
    })

    // NEW: Set niceAt timestamp when deal crosses NICE_THRESHOLD
    const threshold = Number(process.env.NICE_THRESHOLD) || 10
    const newScore = updatedDeal.upvotes - updatedDeal.downvotes
    if (newScore >= threshold && !updatedDeal.niceAt) {
      await db.deal.update({
        where: { id: dealId },
        data: { 
          niceAt: new Date() 
        }
      })
    }

    return NextResponse.json({
      success: true,
      deal: updatedDeal,
      userVote: existingVote?.type === type ? null : type
    })

  } catch (error) {
    console.error("Vote error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  const dealId = parseInt(resolvedParams.id, 10);

  if (isNaN(dealId)) {
    return NextResponse.json(
      { message: "Invalid deal ID" },
      { status: 400 }
    )
  }
  try {
    const session = await getServerSession()

    if (!session?.user) {
      return NextResponse.json({ userVote: null })
    }

    const userIdString = session.user.id
    const userId = parseInt(userIdString, 10)

    if (isNaN(userId)) {
      return NextResponse.json({ userVote: null })
    }

    const vote = await db.vote.findUnique({
      where: {
        dealId_userId: {
          dealId,
          userId
        }
      }
    })

    return NextResponse.json({
      userVote: vote?.type || null
    })

  } catch (error) {
    console.error("Get vote error:", error)
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    )
  }
}
