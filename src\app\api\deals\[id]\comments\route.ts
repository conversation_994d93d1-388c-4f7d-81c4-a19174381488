import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "@/lib/auth"
import { db } from "@/lib/db"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  const dealId = parseInt(resolvedParams.id, 10);

  if (isNaN(dealId)) {
    return NextResponse.json(
      { message: "Invalid deal ID" },
      { status: 400 }
    )
  }
  try {
    // Fetch comments with user information and replies
    const comments = await db.comment.findMany({
      where: {
        dealId,
        status: "APPROVED",
        parentCommentId: null // Only top-level comments
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true
          }
        },
        replies: {
          where: { status: "APPROVED" },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true
              }
            }
          },
          orderBy: { createdAt: 'asc' }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json({ comments })

  } catch (error) {
    console.error("Comments fetch error:", error)
    return NextResponse.json(
      { message: "Failed to fetch comments" },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession()
    
    if (!session?.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      )
    }

    const { content, parentCommentId } = await request.json()
    const resolvedParams = await params
    const dealId = parseInt(resolvedParams.id, 10)

    if (isNaN(dealId)) {
      return NextResponse.json(
        { message: "Invalid deal ID" },
        { status: 400 }
      )
    }

    // Validate content
    if (!content || content.trim().length < 3) {
      return NextResponse.json(
        { message: "Comment must be at least 3 characters long" },
        { status: 400 }
      )
    }

    if (content.trim().length > 1000) {
      return NextResponse.json(
        { message: "Comment must be less than 1000 characters" },
        { status: 400 }
      )
    }

    // Check if deal exists
    const deal = await db.deal.findUnique({
      where: { id: dealId }
    })

    if (!deal) {
      return NextResponse.json(
        { message: "Deal not found" },
        { status: 404 }
      )
    }

    // If this is a reply, check if parent comment exists
    if (parentCommentId) {
      const parentComment = await db.comment.findUnique({
        where: { id: parentCommentId }
      })

      if (!parentComment || parentComment.dealId !== dealId) {
        return NextResponse.json(
          { message: "Parent comment not found" },
          { status: 400 }
        )
      }
    }

    // Convert user ID to integer
    const userIdString = session.user.id
    const userId = parseInt(userIdString, 10)

    if (isNaN(userId)) {
      return NextResponse.json(
        { message: "Invalid user ID" },
        { status: 400 }
      )
    }

    // Create the comment
    const comment = await db.comment.create({
      data: {
        content: content.trim(),
        dealId,
        userId,
        parentCommentId: parentCommentId || null,
        status: "APPROVED" // Auto-approve for now, can add moderation later
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      comment
    }, { status: 201 })

  } catch (error) {
    console.error("Comment creation error:", error)
    return NextResponse.json(
      { message: "Failed to create comment" },
      { status: 500 }
    )
  }
}
