import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

// Email configuration
const FROM_EMAIL = process.env.EMAIL_FROM || 'NiceDeals <<EMAIL>>';

// Email header template with logo
const getEmailHeader = () => {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3010';
  const logoUrl = `${baseUrl}/nicedeals-logo.png`;
  return `
    <!-- Email Header with Logo -->
    <div style="background: white; padding: 20px 30px; text-align: center; border-bottom: 1px solid #e5e7eb;">
      <a href="${baseUrl}" style="text-decoration: none;">
        <img src="${logoUrl}" alt="NiceDeals Logo" style="height: 40px; width: auto; max-width: 200px;" />
      </a>
    </div>
  `;
};


// Email service class
export class EmailService {
  private static instance: EmailService;

  static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService();
    }
    return EmailService.instance;
  }

  async sendEmail(options: {
    to: string | string[];
    subject: string;
    html: string;
    text?: string;
  }) {
    try {
      const { data, error } = await resend.emails.send({
        from: FROM_EMAIL,
        to: Array.isArray(options.to) ? options.to : [options.to],
        subject: options.subject,
        html: options.html,
        text: options.text,
      });

      if (error) {
        console.error('Email sending error:', error);
        throw new Error(`Failed to send email: ${error.message}`);
      }

      console.log('Email sent successfully:', data?.id);
      return data;
    } catch (error) {
      console.error('Email service error:', error);
      throw error;
    }
  }

  // Deal notification emails
  async sendDealApprovalNotification(userEmail: string, dealTitle: string, dealUrl: string) {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3010';
    const subject = `✅ Your deal "${dealTitle}" has been approved!`;
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Deal Approved</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 0; background: #f8f9fa;">
          ${getEmailHeader()}
          
          <div style="background: #f97316; padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">🎉 Deal Approved!</h1>
          </div>
          
          <div style="background: white; padding: 30px;">
            <p style="font-size: 18px; margin-bottom: 20px;">Great news! Your deal has been approved and is now live on NiceDeals.</p>
            
            <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #f97316; margin: 20px 0;">
              <h3 style="margin: 0 0 10px 0; color: #f97316;">${dealTitle}</h3>
              <p style="margin: 0; color: #666;">Your deal is now visible to all NiceDeals users and ready to help people save money!</p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${dealUrl}" style="background: #f97316; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">View Your Deal</a>
            </div>
            
            <p style="color: #666; font-size: 14px; margin-top: 30px;">
              Thank you for contributing to the NiceDeals community! Keep sharing great deals.
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
            <p>This email was sent by NiceDeals. Visit us at <a href="${baseUrl}" style="color: #f97316;">nicedeals.app</a></p>
          </div>
        </body>
      </html>
    `;

    return this.sendEmail({
      to: userEmail,
      subject,
      html,
      text: `Your deal "${dealTitle}" has been approved! View it at: ${dealUrl}`
    });
  }

  async sendDealRejectionNotification(userEmail: string, dealTitle: string, reason: string) {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3010';
    const subject = `❌ Your deal "${dealTitle}" needs attention`;
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Deal Needs Attention</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 0; background: #f8f9fa;">
          ${getEmailHeader()}
          
          <div style="background: #dc2626; padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Deal Needs Attention</h1>
          </div>
          
          <div style="background: white; padding: 30px;">
            <p style="font-size: 18px; margin-bottom: 20px;">We've reviewed your deal submission and it needs some adjustments before it can go live.</p>
            
            <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #dc2626; margin: 20px 0;">
              <h3 style="margin: 0 0 10px 0; color: #dc2626;">${dealTitle}</h3>
              <p style="margin: 10px 0 0 0; color: #666;"><strong>Reason:</strong> ${reason}</p>
            </div>
            
            <p style="color: #666;">
              Please review the feedback above and feel free to submit a new deal with the necessary changes. We appreciate your contribution to the NiceDeals community!
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${baseUrl}/post" style="background: #f97316; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">Submit New Deal</a>
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
            <p>This email was sent by NiceDeals. Visit us at <a href="${baseUrl}" style="color: #f97316;">nicedeals.app</a></p>
          </div>
        </body>
      </html>
    `;

    return this.sendEmail({
      to: userEmail,
      subject,
      html,
      text: `Your deal "${dealTitle}" needs attention. Reason: ${reason}. Submit a new deal at: ${baseUrl}/post`
    });
  }

  // User notification emails
  async sendWelcomeEmail(userEmail: string, userName: string) {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3010';
    const subject = `🎉 Welcome to NiceDeals, ${userName}!`;
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Welcome to NiceDeals</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 0; background: #f8f9fa;">
          ${getEmailHeader()}
          
          <div style="background: #f97316; padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to NiceDeals!</h1>
          </div>
          
          <div style="background: white; padding: 30px;">
            <p style="font-size: 18px; margin-bottom: 20px;">Hi ${userName}! 👋</p>
            
            <p>Welcome to the NiceDeals community! We're excited to have you join thousands of savvy shoppers who are saving money every day.</p>
            
            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin: 0 0 15px 0; color: #f97316;">Here's what you can do:</h3>
              <ul style="margin: 0; padding-left: 20px;">
                <li style="margin-bottom: 8px;">🔍 <strong>Browse deals</strong> - Find amazing discounts on products you love</li>
                <li style="margin-bottom: 8px;">👍 <strong>Vote on deals</strong> - Help the community by rating deals</li>
                <li style="margin-bottom: 8px;">💬 <strong>Comment</strong> - Share your thoughts and ask questions</li>
                <li style="margin-bottom: 8px;">📝 <strong>Submit deals</strong> - Share great deals you've found</li>
                <li style="margin-bottom: 8px;">💾 <strong>Save deals</strong> - Keep track of deals you're interested in</li>
              </ul>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${baseUrl}/browse" style="background: #f97316; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block; margin-right: 10px;">Browse Deals</a>
              <a href="${baseUrl}/post" style="background: #059669; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">Submit a Deal</a>
            </div>
            
            <p style="color: #666; font-size: 14px;">
              Happy deal hunting! 🛍️
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
            <p>This email was sent by NiceDeals. Visit us at <a href="${baseUrl}" style="color: #f97316;">nicedeals.app</a></p>
          </div>
        </body>
      </html>
    `;

    return this.sendEmail({
      to: userEmail,
      subject,
      html,
      text: `Welcome to NiceDeals, ${userName}! Start browsing deals at: ${baseUrl}/browse`
    });
  }

  async sendPasswordResetEmail(userEmail: string, userName: string, resetToken: string) {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3010';
    const resetUrl = `${baseUrl}/auth/reset-password?token=${resetToken}`;
    const subject = `🔐 Reset Your NiceDeals Password`;
    
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Reset Your Password</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 0; background: #f8f9fa;">
          ${getEmailHeader()}
          
          <div style="background: #3b82f6; padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">🔐 Reset Your Password</h1>
          </div>
          
          <div style="background: white; padding: 30px;">
            <p style="font-size: 18px; margin-bottom: 20px;">Hi ${userName}!</p>
            
            <p>We received a request to reset your password for your NiceDeals account. If you didn't make this request, you can safely ignore this email.</p>
            
            <div style="background: #eff6ff; padding: 20px; border-radius: 8px; border-left: 4px solid #3b82f6; margin: 20px 0;">
              <h3 style="margin: 0 0 10px 0; color: #3b82f6;">Reset Your Password</h3>
              <p style="margin: 0; color: #666;">Click the button below to create a new password. This link will expire in 1 hour for security.</p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetUrl}" style="background: #3b82f6; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">Reset Password</a>
            </div>
            
            <div style="background: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0;">
              <p style="margin: 0; color: #92400e; font-size: 14px;">
                <strong>Security tip:</strong> If you didn't request this password reset, please secure your account by changing your password immediately and contact our support team.
              </p>
            </div>
            
            <p style="color: #666; font-size: 14px; margin-top: 20px;">
              If the button doesn't work, you can copy and paste this link into your browser:<br>
              <a href="${resetUrl}" style="color: #3b82f6; word-break: break-all;">${resetUrl}</a>
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
            <p>This email was sent by NiceDeals. Visit us at <a href="${baseUrl}" style="color: #f97316;">nicedeals.app</a></p>
          </div>
        </body>
      </html>
    `;

    return this.sendEmail({
      to: userEmail,
      subject,
      html,
      text: `Reset your NiceDeals password by clicking this link: ${resetUrl} (This link expires in 1 hour)`
    });
  }

  // Comment notification emails
  async sendCommentNotification(dealAuthorEmail: string, dealTitle: string, commenterName: string, comment: string, dealUrl: string) {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3010';
    const subject = `💬 New comment on your deal: "${dealTitle}"`;
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>New Comment</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 0; background: #f8f9fa;">
          ${getEmailHeader()}
          
          <div style="background: #3b82f6; padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">💬 New Comment</h1>
          </div>
          
          <div style="background: white; padding: 30px;">
            <p style="font-size: 18px; margin-bottom: 20px;">Someone commented on your deal!</p>
            
            <div style="background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #3b82f6; margin: 20px 0;">
              <h3 style="margin: 0 0 10px 0; color: #3b82f6;">${dealTitle}</h3>
              <p style="margin: 10px 0; color: #666;"><strong>${commenterName}</strong> said:</p>
              <p style="margin: 0; padding: 15px; background: #f1f5f9; border-radius: 6px; font-style: italic;">"${comment}"</p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${dealUrl}" style="background: #3b82f6; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">View Comment</a>
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
            <p>This email was sent by NiceDeals. Visit us at <a href="${baseUrl}" style="color: #f97316;">nicedeals.app</a></p>
          </div>
        </body>
      </html>
    `;

    return this.sendEmail({
      to: dealAuthorEmail,
      subject,
      html,
      text: `${commenterName} commented on your deal "${dealTitle}": "${comment}". View at: ${dealUrl}`
    });
  }

  // Weekly digest email
  async sendWeeklyDigest(userEmail: string, userName: string, topDeals: any[]) {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3010';
    const subject = `📊 Your Weekly NiceDeals Digest`;
    const dealsHtml = topDeals.map(deal => `
      <div style="background: white; padding: 20px; border-radius: 8px; margin: 15px 0; border: 1px solid #e5e7eb;">
        <h4 style="margin: 0 0 10px 0; color: #374151;">${deal.title}</h4>
        <p style="margin: 5px 0; color: #059669; font-weight: bold; font-size: 18px;">£${deal.dealPrice} <span style="color: #9ca3af; text-decoration: line-through; font-weight: normal;">£${deal.originalPrice}</span></p>
        <p style="margin: 5px 0; color: #6b7280; font-size: 14px;">👍 ${deal.upvotes} votes • 💬 ${deal.commentCount} comments</p>
        <a href="${baseUrl}/deals/${deal.id}" style="color: #f97316; text-decoration: none; font-weight: bold;">View Deal →</a>
      </div>
    `).join('');

    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Weekly Digest</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 0; background: #f8f9fa;">
          ${getEmailHeader()}
          
          <div style="background: #8b5cf6; padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">📊 Weekly Digest</h1>
          </div>
          
          <div style="background: white; padding: 30px;">
            <p style="font-size: 18px; margin-bottom: 20px;">Hi ${userName}! Here are this week's top deals:</p>
            
            ${dealsHtml}
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${baseUrl}/browse" style="background: #f97316; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">Browse All Deals</a>
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
            <p>This email was sent by NiceDeals. Visit us at <a href="${baseUrl}" style="color: #f97316;">nicedeals.app</a></p>
          </div>
        </body>
      </html>
    `;

    return this.sendEmail({
      to: userEmail,
      subject,
      html,
      text: `Your weekly NiceDeals digest is ready! View top deals at: ${baseUrl}/browse`
    });
  }

  // Admin notification emails
  async sendAdminNotification(subject: string, message: string, data?: any) {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3010';
    const adminEmail = '<EMAIL>'; // This should be configurable
    
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Admin Notification</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 0; background: #f8f9fa;">
          ${getEmailHeader()}
          
          <div style="background: #dc2626; padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">🚨 Admin Alert</h1>
          </div>
          
          <div style="background: white; padding: 30px;">
            <h2 style="margin: 0 0 20px 0; color: #dc2626;">${subject}</h2>
            <p style="font-size: 16px; margin-bottom: 20px;">${message}</p>
            
            ${data ? `
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="margin: 0 0 15px 0;">Additional Data:</h3>
                <pre style="background: #f1f5f9; padding: 15px; border-radius: 6px; overflow-x: auto; font-size: 12px;">${JSON.stringify(data, null, 2)}</pre>
              </div>
            ` : ''}
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${baseUrl}/admin" style="background: #dc2626; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">Go to Admin Panel</a>
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
            <p>This email was sent by NiceDeals. Visit us at <a href="${baseUrl}" style="color: #f97316;">nicedeals.app</a></p>
          </div>
        </body>
      </html>
    `;

    return this.sendEmail({
      to: adminEmail,
      subject: `[NiceDeals Admin] ${subject}`,
      html,
      text: `${subject}\n\n${message}\n\nData: ${data ? JSON.stringify(data) : 'None'}`
    });
  }
}

// Export singleton instance
export const emailService = EmailService.getInstance();

// Export individual functions for convenience
export const sendDealApprovalNotification = (userEmail: string, dealTitle: string, dealUrl: string) =>
  emailService.sendDealApprovalNotification(userEmail, dealTitle, dealUrl);

export const sendDealRejectionNotification = (userEmail: string, dealTitle: string, reason: string) =>
  emailService.sendDealRejectionNotification(userEmail, dealTitle, reason);

export const sendWelcomeEmail = (userEmail: string, userName: string) =>
  emailService.sendWelcomeEmail(userEmail, userName);

export const sendPasswordResetEmail = (userEmail: string, userName: string, resetToken: string) =>
  emailService.sendPasswordResetEmail(userEmail, userName, resetToken);

export const sendCommentNotification = (dealAuthorEmail: string, dealTitle: string, commenterName: string, comment: string, dealUrl: string) =>
  emailService.sendCommentNotification(dealAuthorEmail, dealTitle, commenterName, comment, dealUrl);

export const sendWeeklyDigest = (userEmail: string, userName: string, topDeals: any[]) =>
  emailService.sendWeeklyDigest(userEmail, userName, topDeals);

export const sendAdminNotification = (subject: string, message: string, data?: any) =>
  emailService.sendAdminNotification(subject, message, data); 