import { NextAuthOptions } from "next-auth"
import GoogleProvider from "next-auth/providers/google"
import FacebookProvider from "next-auth/providers/facebook"
import CredentialsProvider from "next-auth/providers/credentials"
import { PrismaAdapter } from "@next-auth/prisma-adapter"
import { db } from "./db"
import bcrypt from "bcryptjs"

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(db),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    FacebookProvider({
      clientId: process.env.FACEBOOK_CLIENT_ID!,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await db.user.findUnique({
          where: {
            email: credentials.email
          },
          select: {
            id: true,
            email: true,
            name: true,
            password: true,
            role: true
          }
        })

        if (!user || !user.password) {
          return null
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        )

        if (!isPasswordValid) {
          return null
        }

        return {
          id: user.id.toString(),
          email: user.email,
          name: user.name,
          role: user.role,
        }
      }
    }),
  ],
  callbacks: {
    session: async ({ session, token }) => {
      if (session?.user && token) {
        session.user.id = token.sub!
        session.user.email = token.email as string
        session.user.name = token.name as string
        session.user.role = token.role as string || "USER"
      }
      return session
    },
    jwt: async ({ user, token }) => {
      if (user) {
        token.sub = user.id
        token.email = user.email
        token.name = user.name
        token.role = user.role
      }
      
      // Refresh user data from database every time
      if (token.sub) {
        try {
          const userIdInt = parseInt(token.sub, 10)
          if (!isNaN(userIdInt)) {
            const dbUser = await db.user.findUnique({
              where: { id: userIdInt },
              select: { role: true, name: true, email: true }
            })
            if (dbUser) {
              token.role = dbUser.role
              token.name = dbUser.name
              token.email = dbUser.email
            } else {
              console.error("JWT: User not found in database:", userIdInt)
            }
          } else {
            console.error("JWT: Invalid user ID in token:", token.sub)
          }
        } catch (error) {
          console.error("JWT Callback: Error refreshing user data:", error)
        }
      }
      
      return token
    },
  },
  session: {
    strategy: "jwt",
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
}

// Helper function to get server session
export async function getServerSession() {
  const { getServerSession: nextAuthGetServerSession } = await import("next-auth/next")
  return nextAuthGetServerSession(authOptions)
}

// Helper function to check if user is admin
export async function isAdmin(userId: string): Promise<boolean> {
  try {
    const userIdInt = parseInt(userId, 10)
    if (isNaN(userIdInt)) return false
    
    const user = await db.user.findUnique({
      where: { id: userIdInt },
      select: { role: true }
    })
    return user?.role === "ADMIN"
  } catch {
    return false
  }
}

// Helper function to check if user is authenticated
export async function requireAuth() {
  const session = await getServerSession()
  
  if (!session?.user) {
    throw new Error("Authentication required")
  }
  return session
}

// Helper function to require admin access
export async function requireAdmin() {
  const session = await requireAuth()
  const adminStatus = await isAdmin(session.user.id)
  if (!adminStatus) {
    throw new Error("Admin access required")
  }
  return session
}
