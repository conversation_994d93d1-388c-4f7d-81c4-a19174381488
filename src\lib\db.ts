import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const db =
  globalForPrisma.prisma ??
  new PrismaClient({
    log: process.env.NODE_ENV === 'production' ? ['error'] : ['query', 'info', 'warn', 'error'],
  })

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = db

export async function getDealById(id: string | number) {
  const dealId = typeof id === 'string' ? parseInt(id, 10) : id;
  if (isNaN(dealId)) {
    return null;
  }
  
  return db.deal.findUnique({
    where: { id: dealId },
  });
}

/**
 * Shared function to fetch complete deal data with all relations
 * Used by both /deals/[slug] and /deals/[id] routes
 * @param dealId The numeric deal ID
 * @returns Deal with all relations or null if not found
 */
export async function getDealWithRelations(dealId: number) {
  const deal = await db.deal.findUnique({
    where: { id: dealId, status: "ACTIVE" },
    include: {
      author: { select: { name: true, image: true } },
      store: { select: { name: true, slug: true } },
      category: { select: { name: true, slug: true } },
      comments: {
        where: { status: "APPROVED" },
        include: { user: { select: { name: true, image: true } } },
        orderBy: { createdAt: "desc" }
      },
      _count: {
        select: { comments: true, savedDeals: true }
      }
    }
  });

  if (!deal) {
    return null;
  }

  // Get related deals from the same category
  const relatedDeals = await db.deal.findMany({
    where: {
      status: "ACTIVE",
      categoryId: deal.categoryId,
      id: { not: dealId }
    },
    take: 12,
    orderBy: { upvotes: "desc" }
  });

  return { deal, relatedDeals };
}
